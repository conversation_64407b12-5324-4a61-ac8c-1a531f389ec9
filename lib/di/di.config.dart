// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:firebase_remote_config/firebase_remote_config.dart' as _i627;
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart' as _i111;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:x1440/api/auth_interceptor.dart' as _i809;
import 'package:x1440/api/logger_api.dart' as _i222;
import 'package:x1440/api/logger_interceptor.dart' as _i991;
import 'package:x1440/api/salesforce/salesforce_interceptor.dart' as _i574;
import 'package:x1440/api/shim_service_api.dart' as _i145;
import 'package:x1440/debug/global_bloc_observer.dart' as _i734;
import 'package:x1440/di/modules/base_modules.dart' as _i212;
import 'package:x1440/di/modules/config_module.dart' as _i878;
import 'package:x1440/di/modules/demo_module.dart' as _i477;
import 'package:x1440/frameworks/conversations_service.dart' as _i653;
import 'package:x1440/frameworks/crash_manager.dart' as _i1000;
import 'package:x1440/frameworks/demo/end_user/demo_ai_end_user_service.dart'
    as _i1017;
import 'package:x1440/frameworks/message_queue/message_queue_manager.dart'
    as _i180;
import 'package:x1440/frameworks/messaging_service.dart' as _i478;
import 'package:x1440/frameworks/notifications/notifications_manager.dart'
    as _i993;
import 'package:x1440/frameworks/presence/presence_manager.dart' as _i826;
import 'package:x1440/frameworks/push_notification_manager.dart' as _i776;
import 'package:x1440/frameworks/remote_config/app_config.dart' as _i516;
import 'package:x1440/frameworks/routing/routing_manager.dart' as _i125;
import 'package:x1440/frameworks/settings/settings_manager.dart' as _i746;
import 'package:x1440/frameworks/typing_indicator/typing_indicator_manager.dart'
    as _i131;
import 'package:x1440/repositories/auth/auth_repository.dart' as _i561;
import 'package:x1440/repositories/channels/channels_repository.dart' as _i856;
import 'package:x1440/repositories/contacts/contact_details_repository.dart'
    as _i581;
import 'package:x1440/repositories/contacts/contacts_repository.dart' as _i761;
import 'package:x1440/repositories/conversation/conversation_repository.dart'
    as _i900;
import 'package:x1440/repositories/environment_info/environment_info_repository.dart'
    as _i310;
import 'package:x1440/repositories/graph_ql/graphql_repository.dart' as _i138;
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart'
    as _i652;
import 'package:x1440/repositories/logging/logging_repository.dart' as _i704;
import 'package:x1440/repositories/message_queue/message_queue_repository.dart'
    as _i770;
import 'package:x1440/repositories/messaging_sessions/messaging_sessions_repository.dart'
    as _i503;
import 'package:x1440/repositories/notifications/notifications_repository.dart'
    as _i598;
import 'package:x1440/repositories/presence/presence_repository.dart' as _i216;
import 'package:x1440/repositories/query/query_repository.dart' as _i95;
import 'package:x1440/repositories/quick_text_repository/quick_text_repository.dart'
    as _i856;
import 'package:x1440/repositories/salesforce_data_repository/salesforce_data_repository.dart'
    as _i886;
import 'package:x1440/repositories/search/search_repository.dart' as _i780;
import 'package:x1440/repositories/session/session_repository.dart' as _i503;
import 'package:x1440/repositories/storage/local_storage_repository.dart'
    as _i485;
import 'package:x1440/repositories/storage/secure_storage_repository.dart'
    as _i707;
import 'package:x1440/repositories/transfer/transfer_repository.dart' as _i93;
import 'package:x1440/repositories/websocket/messaging_definitions_repository.dart'
    as _i702;
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart'
    as _i1027;
import 'package:x1440/services/de_conversations_service.dart' as _i684;
import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart'
    as _i493;
import 'package:x1440/services/interfaces/productivity_tools_viewmodel_interface.dart'
    as _i764;
import 'package:x1440/services/security/encryption_service.dart' as _i746;
import 'package:x1440/services/shim_service/shim_service.dart' as _i259;
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_bloc.dart'
    as _i312;
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart' as _i675;
import 'package:x1440/ui/blocs/auth/auth_bloc.dart' as _i918;
import 'package:x1440/ui/blocs/channels/channels_bloc.dart' as _i945;
import 'package:x1440/ui/blocs/chat/chat_bloc.dart' as _i115;
import 'package:x1440/ui/blocs/contacts/contacts_bloc.dart' as _i854;
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart' as _i927;
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_manager.dart' as _i988;
import 'package:x1440/ui/blocs/image_size/image_size_bloc.dart' as _i133;
import 'package:x1440/ui/blocs/messaging/definitions/messaging_definitions_bloc.dart'
    as _i927;
import 'package:x1440/ui/blocs/messaging/messaging_bloc.dart' as _i134;
import 'package:x1440/ui/blocs/org/org_bloc.dart' as _i492;
import 'package:x1440/use_cases/ai_suggestions/ai_suggestions_use_case.dart'
    as _i399;
import 'package:x1440/use_cases/auth/auth_use_case.dart' as _i340;
import 'package:x1440/use_cases/channels/channels_use_case.dart' as _i438;
import 'package:x1440/use_cases/contacts/contacts_use_case.dart' as _i74;
import 'package:x1440/use_cases/conversations/conversations_use_case.dart'
    as _i1073;
import 'package:x1440/use_cases/demo/demo_ai_suggestions_use_case.dart'
    as _i263;
import 'package:x1440/use_cases/demo/demo_user_use_case.dart' as _i804;
import 'package:x1440/use_cases/logging/logging_use_case.dart' as _i23;
import 'package:x1440/use_cases/messaging/messaging_definitions_use_case.dart'
    as _i778;
import 'package:x1440/use_cases/messaging/messaging_use_case.dart' as _i398;
import 'package:x1440/use_cases/messaging_channels/messaging_channels_use_case.dart'
    as _i952;
import 'package:x1440/use_cases/notifications/notifications_use_case.dart'
    as _i428;
import 'package:x1440/use_cases/org/org_use_case.dart' as _i870;
import 'package:x1440/use_cases/presence/presence_use_case.dart' as _i972;
import 'package:x1440/use_cases/quick_actions/quick_actions_use_case.dart'
    as _i477;
import 'package:x1440/use_cases/salesforce_data/salesforce_data_use_case.dart'
    as _i214;
import 'package:x1440/use_cases/session/session_use_case.dart' as _i978;
import 'package:x1440/use_cases/settings/settings_use_case.dart' as _i1000;
import 'package:x1440/use_cases/typing_indicator/typing_indicator_use_case.dart'
    as _i765;
import 'package:x1440/viewmodels/conversations_viewmodel.dart' as _i909;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final moduleAppConfig = _$ModuleAppConfig();
    final baseModules = _$BaseModules();
    final demoModule = _$DemoModule();
    await gh.singletonAsync<_i627.FirebaseRemoteConfig>(
      () => moduleAppConfig.firebaseRemoteConfig(),
      preResolve: true,
    );
    await gh.singletonAsync<_i111.FlutterWebAuth2Options>(
      () => moduleAppConfig.flutterWebAuth2Options(),
      preResolve: true,
    );
    await gh.singletonAsync<_i259.ShimService>(
      () => baseModules.shimServiceFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i652.AppLifeCycleRepository>(
      () => baseModules.appLifeCycleRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i675.AppErrorBloc>(
      () => baseModules.appErrorBlocFactory(),
      preResolve: true,
    );
    gh.singleton<_i133.ImageSizeBloc>(() => baseModules.imageSizeBlocFactory());
    await gh.singletonAsync<_i746.EncryptionService>(
      () => baseModules.encryptionServiceFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i485.LocalStorageRepository>(
      () => baseModules.localStorageRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i707.SecureStorageRepository>(
      () => baseModules.secureStorageRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i765.TypingIndicatorUseCase>(
      () => baseModules.typingIndicatorUseCaseFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i516.AppConfig>(
      () => moduleAppConfig.appConfig(gh<_i627.FirebaseRemoteConfig>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i138.GraphQlRepository>(
      () => baseModules
          .graphQlRepositoryFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i310.EnvironmentInfoRepository>(
      () => baseModules
          .environmentInfoRepositoryFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i761.ContactsRepository>(
      () => baseModules
          .contactsRepositoryFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i581.ContactDetailsRepository>(
      () => baseModules
          .contactDetailsRepositoryFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i780.SearchRepository>(
      () => baseModules
          .searchRepositoryFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i503.MessagingSessionsRepository>(
      () => baseModules.messagingSessionsRepositoryFactory(
          gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i1000.SettingsUseCase>(
      () => baseModules
          .settingsUseCaseFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i870.OrgUseCase>(
      () => baseModules.orgUseCaseFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i574.SalesforceInterceptor>(
      () => baseModules
          .salesforceInterceptorFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i95.QueryRepository>(
      () => baseModules
          .queryRepositoryFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i704.LoggingRepository>(
      () => baseModules
          .loggingRepositoryFactory(gh<_i485.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i477.QuickActionsUseCase>(
      () => baseModules.quickActionsUseCaseFactory(
        gh<_i95.QueryRepository>(),
        gh<_i485.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i764.ProductivityToolsViewmodelInterface>(() => baseModules
        .productivityToolsViewmodelFactory(gh<_i138.GraphQlRepository>()));
    await gh.singletonAsync<_i746.SettingsManager>(
      () => baseModules.settingsManagerFactory(gh<_i1000.SettingsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i492.OrgBloc>(
      () => baseModules.orgBlocFactory(gh<_i870.OrgUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i131.TypingIndicatorManager>(
      () =>
          baseModules.typingManagerFactory(gh<_i765.TypingIndicatorUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i952.MessagingChannelsUseCase>(
      () => baseModules
          .messagingChannelsUseCaseFactory(gh<_i95.QueryRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i74.ContactsUseCase>(
      () => baseModules.contactsUseCaseFactory(
        gh<_i516.AppConfig>(),
        gh<_i780.SearchRepository>(),
        gh<_i761.ContactsRepository>(),
        gh<_i581.ContactDetailsRepository>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i95.QueryRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i145.ShimServiceApi>(
      () => baseModules.shimServiceApiFactory(gh<_i516.AppConfig>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i23.LoggingUseCase>(
      () => baseModules.loggingUseCaseFactory(
        gh<_i704.LoggingRepository>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i652.AppLifeCycleRepository>(),
        gh<_i516.AppConfig>(),
        gh<_i310.EnvironmentInfoRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i561.AuthRepository>(
      () => baseModules.authRepositoryFactory(
        gh<_i145.ShimServiceApi>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i111.FlutterWebAuth2Options>(),
        gh<_i746.EncryptionService>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i886.SalesforceDataRepository>(
      () => baseModules.salesforceDataRepositoryFactory(
        gh<_i23.LoggingUseCase>(),
        gh<_i485.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i493.ContactsViewmodelInterface>(
      () => baseModules.contactsViewmodelFactory(gh<_i74.ContactsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i1027.ShimWebsocketRepository>(
      () => baseModules.shimWebsocketRepositoryFactory(
        gh<_i23.LoggingUseCase>(),
        gh<_i485.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i770.MessageQueueRepository>(
      () => baseModules.messageQueueRepositoryFactory(
        gh<_i23.LoggingUseCase>(),
        gh<_i485.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i776.PushNotificationManager>(
      () => baseModules.pushNotificationManagerFactory(
        gh<_i145.ShimServiceApi>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i702.MessagingDefinitionsRepository>(
      () => baseModules.messagingRepositoryFactory(gh<_i145.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i856.ChannelsRepository>(
      () => baseModules.channelsRepositoryFactory(gh<_i145.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i503.SessionRepository>(
      () => baseModules.sessionRepositoryFactory(gh<_i145.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i216.PresenceRepository>(
      () => baseModules.presenceRepositoryFactory(gh<_i145.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i856.QuickTextRepository>(
      () => baseModules.quickTextRepositoryFactory(gh<_i145.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i93.TransferRepository>(
      () => baseModules.transferRepositoryFactory(gh<_i145.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i598.NotificationsRepository>(
      () => baseModules
          .notificationsRepositoryFactory(gh<_i145.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i778.MessagingDefinitionsUseCase>(
      () => baseModules.messagingDefinitionsUseCaseFactory(
        gh<_i23.LoggingUseCase>(),
        gh<_i702.MessagingDefinitionsRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i809.AuthInterceptor>(
      () => baseModules.authInterceptorFactory(
        gh<_i485.LocalStorageRepository>(),
        gh<_i707.SecureStorageRepository>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i900.ConversationRepository>(
      () => baseModules.conversationRepositoryFactory(
        gh<_i145.ShimServiceApi>(),
        gh<_i485.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i734.GlobalBlocObserver>(
      () => baseModules.globalBlocObserverFactory(gh<_i23.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i125.RoutingManager>(
      () => baseModules.routingManagerFactory(gh<_i23.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i991.LoggerInterceptor>(
      () => baseModules.loggerInterceptorFactory(gh<_i23.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i399.AiSuggestionsUseCase>(
      () => baseModules.aiSuggestionsManagerFactory(gh<_i23.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i1000.CrashManager>(
      () => baseModules.crashManagerFactory(gh<_i23.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i978.SessionUseCase>(
      () => baseModules.sessionUseCaseFactory(
        gh<_i503.SessionRepository>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i310.EnvironmentInfoRepository>(),
        gh<_i516.AppConfig>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i927.MessagingDefinitionsBloc>(
      () => baseModules.messagingDefinitionsBlocFactory(
          gh<_i778.MessagingDefinitionsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i214.SalesforceDataUseCase>(
      () => baseModules.salesforceDataUseCaseFactory(
        gh<_i23.LoggingUseCase>(),
        gh<_i886.SalesforceDataRepository>(),
        gh<_i485.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    gh.singletonAsync<_i804.DemoUserUseCase>(
        () => demoModule.demoUserUseCaseFactory(
              gh<_i310.EnvironmentInfoRepository>(),
              gh<_i485.LocalStorageRepository>(),
              gh<_i23.LoggingUseCase>(),
            ));
    await gh.singletonAsync<_i972.PresenceUseCase>(
      () => baseModules.presenceUseCaseFactory(
        gh<_i485.LocalStorageRepository>(),
        gh<_i216.PresenceRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i988.DemoModeManager>(
      () async => demoModule
          .demoModeManagerFactory(await getAsync<_i804.DemoUserUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i398.MessagingUseCase>(
      () => baseModules.messagingUseCaseFactory(
        gh<_i23.LoggingUseCase>(),
        gh<_i900.ConversationRepository>(),
        gh<_i770.MessageQueueRepository>(),
        gh<_i1027.ShimWebsocketRepository>(),
        gh<_i598.NotificationsRepository>(),
        gh<_i886.SalesforceDataRepository>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i652.AppLifeCycleRepository>(),
        gh<_i310.EnvironmentInfoRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i1073.ConversationsUseCase>(
      () => baseModules.conversationsUseCaseFactory(
        gh<_i95.QueryRepository>(),
        gh<_i900.ConversationRepository>(),
        gh<_i93.TransferRepository>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i131.TypingIndicatorManager>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i361.Dio>(
      () => baseModules.dioFactory(
        gh<_i809.AuthInterceptor>(),
        gh<_i991.LoggerInterceptor>(),
        gh<_i574.SalesforceInterceptor>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i854.ContactsBloc>(() => baseModules.contactsBlocFactory(
          gh<_i74.ContactsUseCase>(),
          gh<_i1073.ConversationsUseCase>(),
        ));
    await gh.singletonAsync<_i438.ChannelsUseCase>(
      () => baseModules.channelsUseCaseFactory(
        gh<_i856.ChannelsRepository>(),
        gh<_i23.LoggingUseCase>(),
        gh<_i485.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i684.DeConversationsService>(
      () => baseModules.DeConversationsServiceFactory(
        gh<_i259.ShimService>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i23.LoggingUseCase>(),
        gh<_i1073.ConversationsUseCase>(),
        gh<_i138.GraphQlRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i909.ConversationsViewmodel>(
      () => baseModules.conversationsViewmodelFactory(
        gh<_i972.PresenceUseCase>(),
        gh<_i1073.ConversationsUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i945.ChannelsBloc>(
      () => baseModules.channelsBlocFactory(gh<_i438.ChannelsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i428.NotificationsUseCase>(
      () => baseModules.notificationsUseCaseFactory(
        gh<_i598.NotificationsRepository>(),
        gh<_i652.AppLifeCycleRepository>(),
        gh<_i770.MessageQueueRepository>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i978.SessionUseCase>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i312.AiSuggestionsBloc>(
        () => baseModules.aiSuggestionsBlocFactory(
              gh<_i399.AiSuggestionsUseCase>(),
              gh<_i1073.ConversationsUseCase>(),
            ));
    await gh.singletonAsync<_i222.LoggerApi>(
      () => baseModules.loggerApiFactory(
        gh<_i361.Dio>(),
        gh<_i516.AppConfig>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i993.NotificationManager>(
      () => baseModules.notificationManagerFactory(
        gh<_i125.RoutingManager>(),
        gh<_i74.ContactsUseCase>(),
        gh<_i1073.ConversationsUseCase>(),
        gh<_i652.AppLifeCycleRepository>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i340.AuthUseCase>(
      () => baseModules.authUseCaseFactory(
        gh<_i561.AuthRepository>(),
        gh<_i485.LocalStorageRepository>(),
        gh<_i707.SecureStorageRepository>(),
        gh<_i516.AppConfig>(),
        gh<_i993.NotificationManager>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i826.PresenceManager>(
      () => baseModules.presenceManagerFactory(
        gh<_i972.PresenceUseCase>(),
        gh<_i978.SessionUseCase>(),
        gh<_i398.MessagingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i180.MessageQueueManager>(
      () => baseModules.messageQueueManagerFactory(
        gh<_i770.MessageQueueRepository>(),
        gh<_i340.AuthUseCase>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i918.AuthBloc>(
      () => baseModules.authBlocFactory(
        gh<_i340.AuthUseCase>(),
        gh<_i978.SessionUseCase>(),
        gh<_i1073.ConversationsUseCase>(),
        gh<_i398.MessagingUseCase>(),
        gh<_i652.AppLifeCycleRepository>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i478.MessagingService>(
      () => baseModules.messagingServiceFactory(
        gh<_i180.MessageQueueManager>(),
        gh<_i398.MessagingUseCase>(),
        gh<_i1073.ConversationsUseCase>(),
        gh<_i340.AuthUseCase>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i653.ConversationsService>(
      () => baseModules.conversationsServiceFactory(
        gh<_i1073.ConversationsUseCase>(),
        gh<_i428.NotificationsUseCase>(),
        gh<_i978.SessionUseCase>(),
        gh<_i398.MessagingUseCase>(),
        gh<_i23.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i927.ConversationsBloc>(
        () => baseModules.conversationsBlocFactory(
              gh<_i1073.ConversationsUseCase>(),
              gh<_i477.QuickActionsUseCase>(),
              gh<_i340.AuthUseCase>(),
              gh<_i23.LoggingUseCase>(),
            ));
    gh.singleton<_i115.ChatBloc>(() => baseModules.chatBlocFactory(
          gh<_i1073.ConversationsUseCase>(),
          gh<_i131.TypingIndicatorManager>(),
          gh<_i74.ContactsUseCase>(),
          gh<_i398.MessagingUseCase>(),
          gh<_i778.MessagingDefinitionsUseCase>(),
          gh<_i23.LoggingUseCase>(),
          gh<_i477.QuickActionsUseCase>(),
          gh<_i340.AuthUseCase>(),
        ));
    gh.singleton<_i134.MessagingBloc>(() => baseModules.messagingBlocFactory(
          gh<_i398.MessagingUseCase>(),
          gh<_i180.MessageQueueManager>(),
          gh<_i310.EnvironmentInfoRepository>(),
        ));
    return this;
  }

// initializes the registration of demo-scope dependencies inside of GetIt
  Future<_i174.GetIt> initDemoScope({_i174.ScopeDisposeFunc? dispose}) async {
    return _i526.GetItHelper(this).initScopeAsync(
      'demo',
      dispose: dispose,
      init: (_i526.GetItHelper gh) async {
        final demoModule = _$DemoModule();
        gh.singleton<_i263.DemoAiSuggestionsUseCase>(() => demoModule
            .demoAiSuggestionsUseCaseFactory(gh<_i23.LoggingUseCase>()));
        gh.singleton<_i1017.DemoAiEndUserService>(() => demoModule
            .demoAiEndUserServiceFactory(gh<_i263.DemoAiSuggestionsUseCase>()));
        gh.singleton<_i312.AiSuggestionsBloc>(() =>
            demoModule.demoAiSuggestionsBlocFactory(
                gh<_i263.DemoAiSuggestionsUseCase>()));
        await gh.singletonAsync<_i918.AuthBloc>(
          () => demoModule.authBlocFactory(),
          preResolve: true,
        );
        gh.singleton<_i927.ConversationsBloc>(
            () => demoModule.conversationsBlocFactory());
        gh.singleton<_i826.PresenceManager>(
            () => demoModule.demoPresenceManagerFactory());
        gh.singleton<_i764.ProductivityToolsViewmodelInterface>(
            () => demoModule.productivityToolsViewmodelFactory());
        await gh.singletonAsync<_i74.ContactsUseCase>(
          () => demoModule.demoContactsUseCaseFactory(),
          preResolve: true,
        );
        await gh.singletonAsync<_i945.ChannelsBloc>(
          () => demoModule.channelsBlocFactory(),
          preResolve: true,
        );
        await gh.singletonAsync<_i493.ContactsViewmodelInterface>(
          () => demoModule
              .demoContactsViewmodelFactory(gh<_i74.ContactsUseCase>()),
          preResolve: true,
        );
        gh.singleton<_i115.ChatBloc>(() => demoModule.demoChatBlocFactory(
              gh<_i1017.DemoAiEndUserService>(),
              gh<_i74.ContactsUseCase>(),
            ));
      },
    );
  }
}

class _$ModuleAppConfig extends _i878.ModuleAppConfig {}

class _$BaseModules extends _i212.BaseModules {}

class _$DemoModule extends _i477.DemoModule {}

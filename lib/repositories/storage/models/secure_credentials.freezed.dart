// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'secure_credentials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SecureCredentials {
  /// Salesforce refresh token for obtaining new access tokens
  String? get refreshToken;

  /// Salesforce access token for API calls
  String? get accessToken;

  /// Salesforce organization ID
  String? get orgId;

  /// Salesforce user ID
  String? get userId;

  /// Salesforce instance URL (e.g., https://re1693247397909.my.salesforce.com)
  String? get instanceUrl;

  /// Create a copy of SecureCredentials
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SecureCredentialsCopyWith<SecureCredentials> get copyWith =>
      _$SecureCredentialsCopyWithImpl<SecureCredentials>(
          this as SecureCredentials, _$identity);

  /// Serializes this SecureCredentials to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SecureCredentials &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, refreshToken, accessToken, orgId, userId, instanceUrl);

  @override
  String toString() {
    return 'SecureCredentials(refreshToken: $refreshToken, accessToken: $accessToken, orgId: $orgId, userId: $userId, instanceUrl: $instanceUrl)';
  }
}

/// @nodoc
abstract mixin class $SecureCredentialsCopyWith<$Res> {
  factory $SecureCredentialsCopyWith(
          SecureCredentials value, $Res Function(SecureCredentials) _then) =
      _$SecureCredentialsCopyWithImpl;
  @useResult
  $Res call(
      {String? refreshToken,
      String? accessToken,
      String? orgId,
      String? userId,
      String? instanceUrl});
}

/// @nodoc
class _$SecureCredentialsCopyWithImpl<$Res>
    implements $SecureCredentialsCopyWith<$Res> {
  _$SecureCredentialsCopyWithImpl(this._self, this._then);

  final SecureCredentials _self;
  final $Res Function(SecureCredentials) _then;

  /// Create a copy of SecureCredentials
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refreshToken = freezed,
    Object? accessToken = freezed,
    Object? orgId = freezed,
    Object? userId = freezed,
    Object? instanceUrl = freezed,
  }) {
    return _then(_self.copyWith(
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SecureCredentials].
extension SecureCredentialsPatterns on SecureCredentials {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SecureCredentials value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SecureCredentials() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SecureCredentials value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureCredentials():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SecureCredentials value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureCredentials() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? refreshToken, String? accessToken, String? orgId,
            String? userId, String? instanceUrl)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SecureCredentials() when $default != null:
        return $default(_that.refreshToken, _that.accessToken, _that.orgId,
            _that.userId, _that.instanceUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? refreshToken, String? accessToken, String? orgId,
            String? userId, String? instanceUrl)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureCredentials():
        return $default(_that.refreshToken, _that.accessToken, _that.orgId,
            _that.userId, _that.instanceUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? refreshToken, String? accessToken, String? orgId,
            String? userId, String? instanceUrl)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureCredentials() when $default != null:
        return $default(_that.refreshToken, _that.accessToken, _that.orgId,
            _that.userId, _that.instanceUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SecureCredentials implements SecureCredentials {
  const _SecureCredentials(
      {this.refreshToken,
      this.accessToken,
      this.orgId,
      this.userId,
      this.instanceUrl});
  factory _SecureCredentials.fromJson(Map<String, dynamic> json) =>
      _$SecureCredentialsFromJson(json);

  /// Salesforce refresh token for obtaining new access tokens
  @override
  final String? refreshToken;

  /// Salesforce access token for API calls
  @override
  final String? accessToken;

  /// Salesforce organization ID
  @override
  final String? orgId;

  /// Salesforce user ID
  @override
  final String? userId;

  /// Salesforce instance URL (e.g., https://re1693247397909.my.salesforce.com)
  @override
  final String? instanceUrl;

  /// Create a copy of SecureCredentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SecureCredentialsCopyWith<_SecureCredentials> get copyWith =>
      __$SecureCredentialsCopyWithImpl<_SecureCredentials>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SecureCredentialsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SecureCredentials &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, refreshToken, accessToken, orgId, userId, instanceUrl);

  @override
  String toString() {
    return 'SecureCredentials(refreshToken: $refreshToken, accessToken: $accessToken, orgId: $orgId, userId: $userId, instanceUrl: $instanceUrl)';
  }
}

/// @nodoc
abstract mixin class _$SecureCredentialsCopyWith<$Res>
    implements $SecureCredentialsCopyWith<$Res> {
  factory _$SecureCredentialsCopyWith(
          _SecureCredentials value, $Res Function(_SecureCredentials) _then) =
      __$SecureCredentialsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? refreshToken,
      String? accessToken,
      String? orgId,
      String? userId,
      String? instanceUrl});
}

/// @nodoc
class __$SecureCredentialsCopyWithImpl<$Res>
    implements _$SecureCredentialsCopyWith<$Res> {
  __$SecureCredentialsCopyWithImpl(this._self, this._then);

  final _SecureCredentials _self;
  final $Res Function(_SecureCredentials) _then;

  /// Create a copy of SecureCredentials
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? refreshToken = freezed,
    Object? accessToken = freezed,
    Object? orgId = freezed,
    Object? userId = freezed,
    Object? instanceUrl = freezed,
  }) {
    return _then(_SecureCredentials(
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$SecureSessionData {
  /// 1440 session token for API authentication
  String? get sessionToken;

  /// Shim service access token
  String? get shimAccessToken;

  /// Create a copy of SecureSessionData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SecureSessionDataCopyWith<SecureSessionData> get copyWith =>
      _$SecureSessionDataCopyWithImpl<SecureSessionData>(
          this as SecureSessionData, _$identity);

  /// Serializes this SecureSessionData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SecureSessionData &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.shimAccessToken, shimAccessToken) ||
                other.shimAccessToken == shimAccessToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sessionToken, shimAccessToken);

  @override
  String toString() {
    return 'SecureSessionData(sessionToken: $sessionToken, shimAccessToken: $shimAccessToken)';
  }
}

/// @nodoc
abstract mixin class $SecureSessionDataCopyWith<$Res> {
  factory $SecureSessionDataCopyWith(
          SecureSessionData value, $Res Function(SecureSessionData) _then) =
      _$SecureSessionDataCopyWithImpl;
  @useResult
  $Res call({String? sessionToken, String? shimAccessToken});
}

/// @nodoc
class _$SecureSessionDataCopyWithImpl<$Res>
    implements $SecureSessionDataCopyWith<$Res> {
  _$SecureSessionDataCopyWithImpl(this._self, this._then);

  final SecureSessionData _self;
  final $Res Function(SecureSessionData) _then;

  /// Create a copy of SecureSessionData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionToken = freezed,
    Object? shimAccessToken = freezed,
  }) {
    return _then(_self.copyWith(
      sessionToken: freezed == sessionToken
          ? _self.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      shimAccessToken: freezed == shimAccessToken
          ? _self.shimAccessToken
          : shimAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SecureSessionData].
extension SecureSessionDataPatterns on SecureSessionData {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SecureSessionData value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SecureSessionData() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SecureSessionData value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureSessionData():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SecureSessionData value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureSessionData() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? sessionToken, String? shimAccessToken)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SecureSessionData() when $default != null:
        return $default(_that.sessionToken, _that.shimAccessToken);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? sessionToken, String? shimAccessToken) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureSessionData():
        return $default(_that.sessionToken, _that.shimAccessToken);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? sessionToken, String? shimAccessToken)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureSessionData() when $default != null:
        return $default(_that.sessionToken, _that.shimAccessToken);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SecureSessionData implements SecureSessionData {
  const _SecureSessionData({this.sessionToken, this.shimAccessToken});
  factory _SecureSessionData.fromJson(Map<String, dynamic> json) =>
      _$SecureSessionDataFromJson(json);

  /// 1440 session token for API authentication
  @override
  final String? sessionToken;

  /// Shim service access token
  @override
  final String? shimAccessToken;

  /// Create a copy of SecureSessionData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SecureSessionDataCopyWith<_SecureSessionData> get copyWith =>
      __$SecureSessionDataCopyWithImpl<_SecureSessionData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SecureSessionDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SecureSessionData &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.shimAccessToken, shimAccessToken) ||
                other.shimAccessToken == shimAccessToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sessionToken, shimAccessToken);

  @override
  String toString() {
    return 'SecureSessionData(sessionToken: $sessionToken, shimAccessToken: $shimAccessToken)';
  }
}

/// @nodoc
abstract mixin class _$SecureSessionDataCopyWith<$Res>
    implements $SecureSessionDataCopyWith<$Res> {
  factory _$SecureSessionDataCopyWith(
          _SecureSessionData value, $Res Function(_SecureSessionData) _then) =
      __$SecureSessionDataCopyWithImpl;
  @override
  @useResult
  $Res call({String? sessionToken, String? shimAccessToken});
}

/// @nodoc
class __$SecureSessionDataCopyWithImpl<$Res>
    implements _$SecureSessionDataCopyWith<$Res> {
  __$SecureSessionDataCopyWithImpl(this._self, this._then);

  final _SecureSessionData _self;
  final $Res Function(_SecureSessionData) _then;

  /// Create a copy of SecureSessionData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sessionToken = freezed,
    Object? shimAccessToken = freezed,
  }) {
    return _then(_SecureSessionData(
      sessionToken: freezed == sessionToken
          ? _self.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      shimAccessToken: freezed == shimAccessToken
          ? _self.shimAccessToken
          : shimAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$SecureConfigData {
  /// Shim service URL endpoint
  String? get shimServiceUrl;

  /// Create a copy of SecureConfigData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SecureConfigDataCopyWith<SecureConfigData> get copyWith =>
      _$SecureConfigDataCopyWithImpl<SecureConfigData>(
          this as SecureConfigData, _$identity);

  /// Serializes this SecureConfigData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SecureConfigData &&
            (identical(other.shimServiceUrl, shimServiceUrl) ||
                other.shimServiceUrl == shimServiceUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, shimServiceUrl);

  @override
  String toString() {
    return 'SecureConfigData(shimServiceUrl: $shimServiceUrl)';
  }
}

/// @nodoc
abstract mixin class $SecureConfigDataCopyWith<$Res> {
  factory $SecureConfigDataCopyWith(
          SecureConfigData value, $Res Function(SecureConfigData) _then) =
      _$SecureConfigDataCopyWithImpl;
  @useResult
  $Res call({String? shimServiceUrl});
}

/// @nodoc
class _$SecureConfigDataCopyWithImpl<$Res>
    implements $SecureConfigDataCopyWith<$Res> {
  _$SecureConfigDataCopyWithImpl(this._self, this._then);

  final SecureConfigData _self;
  final $Res Function(SecureConfigData) _then;

  /// Create a copy of SecureConfigData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shimServiceUrl = freezed,
  }) {
    return _then(_self.copyWith(
      shimServiceUrl: freezed == shimServiceUrl
          ? _self.shimServiceUrl
          : shimServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SecureConfigData].
extension SecureConfigDataPatterns on SecureConfigData {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SecureConfigData value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SecureConfigData() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SecureConfigData value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureConfigData():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SecureConfigData value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureConfigData() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? shimServiceUrl)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SecureConfigData() when $default != null:
        return $default(_that.shimServiceUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? shimServiceUrl) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureConfigData():
        return $default(_that.shimServiceUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? shimServiceUrl)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SecureConfigData() when $default != null:
        return $default(_that.shimServiceUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SecureConfigData implements SecureConfigData {
  const _SecureConfigData({this.shimServiceUrl});
  factory _SecureConfigData.fromJson(Map<String, dynamic> json) =>
      _$SecureConfigDataFromJson(json);

  /// Shim service URL endpoint
  @override
  final String? shimServiceUrl;

  /// Create a copy of SecureConfigData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SecureConfigDataCopyWith<_SecureConfigData> get copyWith =>
      __$SecureConfigDataCopyWithImpl<_SecureConfigData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SecureConfigDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SecureConfigData &&
            (identical(other.shimServiceUrl, shimServiceUrl) ||
                other.shimServiceUrl == shimServiceUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, shimServiceUrl);

  @override
  String toString() {
    return 'SecureConfigData(shimServiceUrl: $shimServiceUrl)';
  }
}

/// @nodoc
abstract mixin class _$SecureConfigDataCopyWith<$Res>
    implements $SecureConfigDataCopyWith<$Res> {
  factory _$SecureConfigDataCopyWith(
          _SecureConfigData value, $Res Function(_SecureConfigData) _then) =
      __$SecureConfigDataCopyWithImpl;
  @override
  @useResult
  $Res call({String? shimServiceUrl});
}

/// @nodoc
class __$SecureConfigDataCopyWithImpl<$Res>
    implements _$SecureConfigDataCopyWith<$Res> {
  __$SecureConfigDataCopyWithImpl(this._self, this._then);

  final _SecureConfigData _self;
  final $Res Function(_SecureConfigData) _then;

  /// Create a copy of SecureConfigData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? shimServiceUrl = freezed,
  }) {
    return _then(_SecureConfigData(
      shimServiceUrl: freezed == shimServiceUrl
          ? _self.shimServiceUrl
          : shimServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

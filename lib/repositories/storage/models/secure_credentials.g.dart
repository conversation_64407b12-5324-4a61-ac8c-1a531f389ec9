// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'secure_credentials.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SecureCredentials _$SecureCredentialsFromJson(Map json) => $checkedCreate(
      '_SecureCredentials',
      json,
      ($checkedConvert) {
        final val = _SecureCredentials(
          refreshToken: $checkedConvert('refreshToken', (v) => v as String?),
          accessToken: $checkedConvert('accessToken', (v) => v as String?),
          orgId: $checkedConvert('orgId', (v) => v as String?),
          userId: $checkedConvert('userId', (v) => v as String?),
          instanceUrl: $checkedConvert('instanceUrl', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$SecureCredentialsToJson(_SecureCredentials instance) =>
    <String, dynamic>{
      if (instance.refreshToken case final value?) 'refreshToken': value,
      if (instance.accessToken case final value?) 'accessToken': value,
      if (instance.orgId case final value?) 'orgId': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.instanceUrl case final value?) 'instanceUrl': value,
    };

_SecureSessionData _$SecureSessionDataFromJson(Map json) => $checkedCreate(
      '_SecureSessionData',
      json,
      ($checkedConvert) {
        final val = _SecureSessionData(
          sessionToken: $checkedConvert('sessionToken', (v) => v as String?),
          shimAccessToken:
              $checkedConvert('shimAccessToken', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$SecureSessionDataToJson(_SecureSessionData instance) =>
    <String, dynamic>{
      if (instance.sessionToken case final value?) 'sessionToken': value,
      if (instance.shimAccessToken case final value?) 'shimAccessToken': value,
    };

_SecureConfigData _$SecureConfigDataFromJson(Map json) => $checkedCreate(
      '_SecureConfigData',
      json,
      ($checkedConvert) {
        final val = _SecureConfigData(
          shimServiceUrl:
              $checkedConvert('shimServiceUrl', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$SecureConfigDataToJson(_SecureConfigData instance) =>
    <String, dynamic>{
      if (instance.shimServiceUrl case final value?) 'shimServiceUrl': value,
    };

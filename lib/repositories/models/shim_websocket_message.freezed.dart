// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shim_websocket_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ShimWebsocketMessage {
  int get timestamp;
  String get notificationId;
  String get messageCategory;
  String get payload;
  String get sessionId;
  @StringToBoolConverter()
  bool get ack;
  @JsonKey(name: 'avatarUrl')
  String? get notificationAvatarUrl;
  @JsonKey(name: 'avatarName')
  String? get notificationAvatarName;
  @JsonKey(name: 'title')
  @StringToHtmlUnescapedStringConverter()
  String?
      get notificationTitle; // @Default(false) @StringToBoolConverter() bool ack,
  @JsonKey(name: 'body')
  @StringToHtmlUnescapedStringConverter()
  String? get notificationBody;

  /// Create a copy of ShimWebsocketMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShimWebsocketMessageCopyWith<ShimWebsocketMessage> get copyWith =>
      _$ShimWebsocketMessageCopyWithImpl<ShimWebsocketMessage>(
          this as ShimWebsocketMessage, _$identity);

  /// Serializes this ShimWebsocketMessage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShimWebsocketMessage &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            (identical(other.payload, payload) || other.payload == payload) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.ack, ack) || other.ack == ack) &&
            (identical(other.notificationAvatarUrl, notificationAvatarUrl) ||
                other.notificationAvatarUrl == notificationAvatarUrl) &&
            (identical(other.notificationAvatarName, notificationAvatarName) ||
                other.notificationAvatarName == notificationAvatarName) &&
            (identical(other.notificationTitle, notificationTitle) ||
                other.notificationTitle == notificationTitle) &&
            (identical(other.notificationBody, notificationBody) ||
                other.notificationBody == notificationBody));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      messageCategory,
      payload,
      sessionId,
      ack,
      notificationAvatarUrl,
      notificationAvatarName,
      notificationTitle,
      notificationBody);

  @override
  String toString() {
    return 'ShimWebsocketMessage(timestamp: $timestamp, notificationId: $notificationId, messageCategory: $messageCategory, payload: $payload, sessionId: $sessionId, ack: $ack, notificationAvatarUrl: $notificationAvatarUrl, notificationAvatarName: $notificationAvatarName, notificationTitle: $notificationTitle, notificationBody: $notificationBody)';
  }
}

/// @nodoc
abstract mixin class $ShimWebsocketMessageCopyWith<$Res> {
  factory $ShimWebsocketMessageCopyWith(ShimWebsocketMessage value,
          $Res Function(ShimWebsocketMessage) _then) =
      _$ShimWebsocketMessageCopyWithImpl;
  @useResult
  $Res call(
      {int timestamp,
      String notificationId,
      String messageCategory,
      String payload,
      String sessionId,
      @StringToBoolConverter() bool ack,
      @JsonKey(name: 'avatarUrl') String? notificationAvatarUrl,
      @JsonKey(name: 'avatarName') String? notificationAvatarName,
      @JsonKey(name: 'title')
      @StringToHtmlUnescapedStringConverter()
      String? notificationTitle,
      @JsonKey(name: 'body')
      @StringToHtmlUnescapedStringConverter()
      String? notificationBody});
}

/// @nodoc
class _$ShimWebsocketMessageCopyWithImpl<$Res>
    implements $ShimWebsocketMessageCopyWith<$Res> {
  _$ShimWebsocketMessageCopyWithImpl(this._self, this._then);

  final ShimWebsocketMessage _self;
  final $Res Function(ShimWebsocketMessage) _then;

  /// Create a copy of ShimWebsocketMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sessionId = null,
    Object? ack = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_self.copyWith(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _self.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _self.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      ack: null == ack
          ? _self.ack
          : ack // ignore: cast_nullable_to_non_nullable
              as bool,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _self.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _self.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _self.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _self.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ShimWebsocketMessage].
extension ShimWebsocketMessagePatterns on ShimWebsocketMessage {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShimWebsocketMessage value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShimWebsocketMessage() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShimWebsocketMessage value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShimWebsocketMessage():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShimWebsocketMessage value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShimWebsocketMessage() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int timestamp,
            String notificationId,
            String messageCategory,
            String payload,
            String sessionId,
            @StringToBoolConverter() bool ack,
            @JsonKey(name: 'avatarUrl') String? notificationAvatarUrl,
            @JsonKey(name: 'avatarName') String? notificationAvatarName,
            @JsonKey(name: 'title')
            @StringToHtmlUnescapedStringConverter()
            String? notificationTitle,
            @JsonKey(name: 'body')
            @StringToHtmlUnescapedStringConverter()
            String? notificationBody)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShimWebsocketMessage() when $default != null:
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.messageCategory,
            _that.payload,
            _that.sessionId,
            _that.ack,
            _that.notificationAvatarUrl,
            _that.notificationAvatarName,
            _that.notificationTitle,
            _that.notificationBody);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int timestamp,
            String notificationId,
            String messageCategory,
            String payload,
            String sessionId,
            @StringToBoolConverter() bool ack,
            @JsonKey(name: 'avatarUrl') String? notificationAvatarUrl,
            @JsonKey(name: 'avatarName') String? notificationAvatarName,
            @JsonKey(name: 'title')
            @StringToHtmlUnescapedStringConverter()
            String? notificationTitle,
            @JsonKey(name: 'body')
            @StringToHtmlUnescapedStringConverter()
            String? notificationBody)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShimWebsocketMessage():
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.messageCategory,
            _that.payload,
            _that.sessionId,
            _that.ack,
            _that.notificationAvatarUrl,
            _that.notificationAvatarName,
            _that.notificationTitle,
            _that.notificationBody);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int timestamp,
            String notificationId,
            String messageCategory,
            String payload,
            String sessionId,
            @StringToBoolConverter() bool ack,
            @JsonKey(name: 'avatarUrl') String? notificationAvatarUrl,
            @JsonKey(name: 'avatarName') String? notificationAvatarName,
            @JsonKey(name: 'title')
            @StringToHtmlUnescapedStringConverter()
            String? notificationTitle,
            @JsonKey(name: 'body')
            @StringToHtmlUnescapedStringConverter()
            String? notificationBody)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShimWebsocketMessage() when $default != null:
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.messageCategory,
            _that.payload,
            _that.sessionId,
            _that.ack,
            _that.notificationAvatarUrl,
            _that.notificationAvatarName,
            _that.notificationTitle,
            _that.notificationBody);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShimWebsocketMessage implements ShimWebsocketMessage {
  const _ShimWebsocketMessage(
      {required this.timestamp,
      required this.notificationId,
      required this.messageCategory,
      required this.payload,
      required this.sessionId,
      @StringToBoolConverter() this.ack = false,
      @JsonKey(name: 'avatarUrl') this.notificationAvatarUrl,
      @JsonKey(name: 'avatarName') this.notificationAvatarName,
      @JsonKey(name: 'title')
      @StringToHtmlUnescapedStringConverter()
      this.notificationTitle,
      @JsonKey(name: 'body')
      @StringToHtmlUnescapedStringConverter()
      this.notificationBody});
  factory _ShimWebsocketMessage.fromJson(Map<String, dynamic> json) =>
      _$ShimWebsocketMessageFromJson(json);

  @override
  final int timestamp;
  @override
  final String notificationId;
  @override
  final String messageCategory;
  @override
  final String payload;
  @override
  final String sessionId;
  @override
  @JsonKey()
  @StringToBoolConverter()
  final bool ack;
  @override
  @JsonKey(name: 'avatarUrl')
  final String? notificationAvatarUrl;
  @override
  @JsonKey(name: 'avatarName')
  final String? notificationAvatarName;
  @override
  @JsonKey(name: 'title')
  @StringToHtmlUnescapedStringConverter()
  final String? notificationTitle;
// @Default(false) @StringToBoolConverter() bool ack,
  @override
  @JsonKey(name: 'body')
  @StringToHtmlUnescapedStringConverter()
  final String? notificationBody;

  /// Create a copy of ShimWebsocketMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShimWebsocketMessageCopyWith<_ShimWebsocketMessage> get copyWith =>
      __$ShimWebsocketMessageCopyWithImpl<_ShimWebsocketMessage>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShimWebsocketMessageToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShimWebsocketMessage &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            (identical(other.payload, payload) || other.payload == payload) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.ack, ack) || other.ack == ack) &&
            (identical(other.notificationAvatarUrl, notificationAvatarUrl) ||
                other.notificationAvatarUrl == notificationAvatarUrl) &&
            (identical(other.notificationAvatarName, notificationAvatarName) ||
                other.notificationAvatarName == notificationAvatarName) &&
            (identical(other.notificationTitle, notificationTitle) ||
                other.notificationTitle == notificationTitle) &&
            (identical(other.notificationBody, notificationBody) ||
                other.notificationBody == notificationBody));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      messageCategory,
      payload,
      sessionId,
      ack,
      notificationAvatarUrl,
      notificationAvatarName,
      notificationTitle,
      notificationBody);

  @override
  String toString() {
    return 'ShimWebsocketMessage(timestamp: $timestamp, notificationId: $notificationId, messageCategory: $messageCategory, payload: $payload, sessionId: $sessionId, ack: $ack, notificationAvatarUrl: $notificationAvatarUrl, notificationAvatarName: $notificationAvatarName, notificationTitle: $notificationTitle, notificationBody: $notificationBody)';
  }
}

/// @nodoc
abstract mixin class _$ShimWebsocketMessageCopyWith<$Res>
    implements $ShimWebsocketMessageCopyWith<$Res> {
  factory _$ShimWebsocketMessageCopyWith(_ShimWebsocketMessage value,
          $Res Function(_ShimWebsocketMessage) _then) =
      __$ShimWebsocketMessageCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int timestamp,
      String notificationId,
      String messageCategory,
      String payload,
      String sessionId,
      @StringToBoolConverter() bool ack,
      @JsonKey(name: 'avatarUrl') String? notificationAvatarUrl,
      @JsonKey(name: 'avatarName') String? notificationAvatarName,
      @JsonKey(name: 'title')
      @StringToHtmlUnescapedStringConverter()
      String? notificationTitle,
      @JsonKey(name: 'body')
      @StringToHtmlUnescapedStringConverter()
      String? notificationBody});
}

/// @nodoc
class __$ShimWebsocketMessageCopyWithImpl<$Res>
    implements _$ShimWebsocketMessageCopyWith<$Res> {
  __$ShimWebsocketMessageCopyWithImpl(this._self, this._then);

  final _ShimWebsocketMessage _self;
  final $Res Function(_ShimWebsocketMessage) _then;

  /// Create a copy of ShimWebsocketMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sessionId = null,
    Object? ack = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_ShimWebsocketMessage(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _self.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _self.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      ack: null == ack
          ? _self.ack
          : ack // ignore: cast_nullable_to_non_nullable
              as bool,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _self.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _self.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _self.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _self.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

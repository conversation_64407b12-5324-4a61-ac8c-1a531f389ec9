// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_presence_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ActivePresenceStatus {
  String? get id;

  /// Create a copy of ActivePresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ActivePresenceStatusCopyWith<ActivePresenceStatus> get copyWith =>
      _$ActivePresenceStatusCopyWithImpl<ActivePresenceStatus>(
          this as ActivePresenceStatus, _$identity);

  /// Serializes this ActivePresenceStatus to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ActivePresenceStatus &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @override
  String toString() {
    return 'ActivePresenceStatus(id: $id)';
  }
}

/// @nodoc
abstract mixin class $ActivePresenceStatusCopyWith<$Res> {
  factory $ActivePresenceStatusCopyWith(ActivePresenceStatus value,
          $Res Function(ActivePresenceStatus) _then) =
      _$ActivePresenceStatusCopyWithImpl;
  @useResult
  $Res call({String? id});
}

/// @nodoc
class _$ActivePresenceStatusCopyWithImpl<$Res>
    implements $ActivePresenceStatusCopyWith<$Res> {
  _$ActivePresenceStatusCopyWithImpl(this._self, this._then);

  final ActivePresenceStatus _self;
  final $Res Function(ActivePresenceStatus) _then;

  /// Create a copy of ActivePresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ActivePresenceStatus].
extension ActivePresenceStatusPatterns on ActivePresenceStatus {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ActivePresenceStatus value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActivePresenceStatus() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ActivePresenceStatus value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivePresenceStatus():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ActivePresenceStatus value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivePresenceStatus() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? id)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActivePresenceStatus() when $default != null:
        return $default(_that.id);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? id) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivePresenceStatus():
        return $default(_that.id);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? id)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivePresenceStatus() when $default != null:
        return $default(_that.id);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ActivePresenceStatus extends ActivePresenceStatus {
  const _ActivePresenceStatus({this.id}) : super._();
  factory _ActivePresenceStatus.fromJson(Map<String, dynamic> json) =>
      _$ActivePresenceStatusFromJson(json);

  @override
  final String? id;

  /// Create a copy of ActivePresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ActivePresenceStatusCopyWith<_ActivePresenceStatus> get copyWith =>
      __$ActivePresenceStatusCopyWithImpl<_ActivePresenceStatus>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ActivePresenceStatusToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ActivePresenceStatus &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @override
  String toString() {
    return 'ActivePresenceStatus(id: $id)';
  }
}

/// @nodoc
abstract mixin class _$ActivePresenceStatusCopyWith<$Res>
    implements $ActivePresenceStatusCopyWith<$Res> {
  factory _$ActivePresenceStatusCopyWith(_ActivePresenceStatus value,
          $Res Function(_ActivePresenceStatus) _then) =
      __$ActivePresenceStatusCopyWithImpl;
  @override
  @useResult
  $Res call({String? id});
}

/// @nodoc
class __$ActivePresenceStatusCopyWithImpl<$Res>
    implements _$ActivePresenceStatusCopyWith<$Res> {
  __$ActivePresenceStatusCopyWithImpl(this._self, this._then);

  final _ActivePresenceStatus _self;
  final $Res Function(_ActivePresenceStatus) _then;

  /// Create a copy of ActivePresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_ActivePresenceStatus(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

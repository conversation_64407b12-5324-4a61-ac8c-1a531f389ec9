// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shim_websocket_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ShimWebsocketMessage _$ShimWebsocketMessageFromJson(Map json) =>
    $checkedCreate(
      '_ShimWebsocketMessage',
      json,
      ($checkedConvert) {
        final val = _ShimWebsocketMessage(
          timestamp: $checkedConvert('timestamp', (v) => (v as num).toInt()),
          notificationId: $checkedConvert('notificationId', (v) => v as String),
          messageCategory:
              $checkedConvert('messageCategory', (v) => v as String),
          payload: $checkedConvert('payload', (v) => v as String),
          sessionId: $checkedConvert('sessionId', (v) => v as String),
          ack: $checkedConvert(
              'ack',
              (v) => v == null
                  ? false
                  : const StringToBoolConverter().fromJson(v)),
          notificationAvatarUrl:
              $checkedConvert('avatarUrl', (v) => v as String?),
          notificationAvatarName:
              $checkedConvert('avatarName', (v) => v as String?),
          notificationTitle: $checkedConvert('title',
              (v) => const StringToHtmlUnescapedStringConverter().fromJson(v)),
          notificationBody: $checkedConvert('body',
              (v) => const StringToHtmlUnescapedStringConverter().fromJson(v)),
        );
        return val;
      },
      fieldKeyMap: const {
        'notificationAvatarUrl': 'avatarUrl',
        'notificationAvatarName': 'avatarName',
        'notificationTitle': 'title',
        'notificationBody': 'body'
      },
    );

Map<String, dynamic> _$ShimWebsocketMessageToJson(
        _ShimWebsocketMessage instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp,
      'notificationId': instance.notificationId,
      'messageCategory': instance.messageCategory,
      'payload': instance.payload,
      'sessionId': instance.sessionId,
      if (const StringToBoolConverter().toJson(instance.ack) case final value?)
        'ack': value,
      if (instance.notificationAvatarUrl case final value?) 'avatarUrl': value,
      if (instance.notificationAvatarName case final value?)
        'avatarName': value,
      if (const StringToHtmlUnescapedStringConverter()
              .toJson(instance.notificationTitle)
          case final value?)
        'title': value,
      if (const StringToHtmlUnescapedStringConverter()
              .toJson(instance.notificationBody)
          case final value?)
        'body': value,
    };

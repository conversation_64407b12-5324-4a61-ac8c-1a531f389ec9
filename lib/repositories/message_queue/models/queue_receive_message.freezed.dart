// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'queue_receive_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QueueReceiveMessage {
  @TimestampIntConverter()
  int get timestamp;

  /// if we want to sort by these, this should be an Isar index
  String get notificationId;

  /// if we want to query by this, it should be an Isar index
  String? get notificationAction;
  String get messageCategory;
  String get stringifiedPayload;
  String
      get sessionId; // TODO: handle this better/differently ... being used to get the message info from a WorkOffered notification
// NOTE: as of 2024-06, ISAR does not support Maps here, but will in the future
// ExtraNotificationInfo? extraNotificationInfo
  String? get notificationAvatarUrl;
  String? get notificationAvatarName;
  String? get notificationTitle;
  String? get notificationBody;

  /// Create a copy of QueueReceiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QueueReceiveMessageCopyWith<QueueReceiveMessage> get copyWith =>
      _$QueueReceiveMessageCopyWithImpl<QueueReceiveMessage>(
          this as QueueReceiveMessage, _$identity);

  /// Serializes this QueueReceiveMessage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QueueReceiveMessage &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.notificationAction, notificationAction) ||
                other.notificationAction == notificationAction) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            (identical(other.stringifiedPayload, stringifiedPayload) ||
                other.stringifiedPayload == stringifiedPayload) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.notificationAvatarUrl, notificationAvatarUrl) ||
                other.notificationAvatarUrl == notificationAvatarUrl) &&
            (identical(other.notificationAvatarName, notificationAvatarName) ||
                other.notificationAvatarName == notificationAvatarName) &&
            (identical(other.notificationTitle, notificationTitle) ||
                other.notificationTitle == notificationTitle) &&
            (identical(other.notificationBody, notificationBody) ||
                other.notificationBody == notificationBody));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      notificationAction,
      messageCategory,
      stringifiedPayload,
      sessionId,
      notificationAvatarUrl,
      notificationAvatarName,
      notificationTitle,
      notificationBody);

  @override
  String toString() {
    return 'QueueReceiveMessage(timestamp: $timestamp, notificationId: $notificationId, notificationAction: $notificationAction, messageCategory: $messageCategory, stringifiedPayload: $stringifiedPayload, sessionId: $sessionId, notificationAvatarUrl: $notificationAvatarUrl, notificationAvatarName: $notificationAvatarName, notificationTitle: $notificationTitle, notificationBody: $notificationBody)';
  }
}

/// @nodoc
abstract mixin class $QueueReceiveMessageCopyWith<$Res> {
  factory $QueueReceiveMessageCopyWith(
          QueueReceiveMessage value, $Res Function(QueueReceiveMessage) _then) =
      _$QueueReceiveMessageCopyWithImpl;
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String? notificationAction,
      String messageCategory,
      String stringifiedPayload,
      String sessionId,
      String? notificationAvatarUrl,
      String? notificationAvatarName,
      String? notificationTitle,
      String? notificationBody});
}

/// @nodoc
class _$QueueReceiveMessageCopyWithImpl<$Res>
    implements $QueueReceiveMessageCopyWith<$Res> {
  _$QueueReceiveMessageCopyWithImpl(this._self, this._then);

  final QueueReceiveMessage _self;
  final $Res Function(QueueReceiveMessage) _then;

  /// Create a copy of QueueReceiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? notificationAction = freezed,
    Object? messageCategory = null,
    Object? stringifiedPayload = null,
    Object? sessionId = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_self.copyWith(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAction: freezed == notificationAction
          ? _self.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      messageCategory: null == messageCategory
          ? _self.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      stringifiedPayload: null == stringifiedPayload
          ? _self.stringifiedPayload
          : stringifiedPayload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _self.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _self.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _self.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _self.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [QueueReceiveMessage].
extension QueueReceiveMessagePatterns on QueueReceiveMessage {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_QueueReceiveMessage value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QueueReceiveMessage() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_QueueReceiveMessage value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueReceiveMessage():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_QueueReceiveMessage value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueReceiveMessage() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @TimestampIntConverter() int timestamp,
            String notificationId,
            String? notificationAction,
            String messageCategory,
            String stringifiedPayload,
            String sessionId,
            String? notificationAvatarUrl,
            String? notificationAvatarName,
            String? notificationTitle,
            String? notificationBody)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QueueReceiveMessage() when $default != null:
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.notificationAction,
            _that.messageCategory,
            _that.stringifiedPayload,
            _that.sessionId,
            _that.notificationAvatarUrl,
            _that.notificationAvatarName,
            _that.notificationTitle,
            _that.notificationBody);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @TimestampIntConverter() int timestamp,
            String notificationId,
            String? notificationAction,
            String messageCategory,
            String stringifiedPayload,
            String sessionId,
            String? notificationAvatarUrl,
            String? notificationAvatarName,
            String? notificationTitle,
            String? notificationBody)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueReceiveMessage():
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.notificationAction,
            _that.messageCategory,
            _that.stringifiedPayload,
            _that.sessionId,
            _that.notificationAvatarUrl,
            _that.notificationAvatarName,
            _that.notificationTitle,
            _that.notificationBody);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @TimestampIntConverter() int timestamp,
            String notificationId,
            String? notificationAction,
            String messageCategory,
            String stringifiedPayload,
            String sessionId,
            String? notificationAvatarUrl,
            String? notificationAvatarName,
            String? notificationTitle,
            String? notificationBody)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueReceiveMessage() when $default != null:
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.notificationAction,
            _that.messageCategory,
            _that.stringifiedPayload,
            _that.sessionId,
            _that.notificationAvatarUrl,
            _that.notificationAvatarName,
            _that.notificationTitle,
            _that.notificationBody);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _QueueReceiveMessage extends QueueReceiveMessage {
  const _QueueReceiveMessage(
      {@TimestampIntConverter() required this.timestamp,
      required this.notificationId,
      this.notificationAction,
      required this.messageCategory,
      required this.stringifiedPayload,
      required this.sessionId,
      this.notificationAvatarUrl,
      this.notificationAvatarName,
      this.notificationTitle,
      this.notificationBody})
      : super._();
  factory _QueueReceiveMessage.fromJson(Map<String, dynamic> json) =>
      _$QueueReceiveMessageFromJson(json);

  @override
  @TimestampIntConverter()
  final int timestamp;

  /// if we want to sort by these, this should be an Isar index
  @override
  final String notificationId;

  /// if we want to query by this, it should be an Isar index
  @override
  final String? notificationAction;
  @override
  final String messageCategory;
  @override
  final String stringifiedPayload;
  @override
  final String sessionId;
// TODO: handle this better/differently ... being used to get the message info from a WorkOffered notification
// NOTE: as of 2024-06, ISAR does not support Maps here, but will in the future
// ExtraNotificationInfo? extraNotificationInfo
  @override
  final String? notificationAvatarUrl;
  @override
  final String? notificationAvatarName;
  @override
  final String? notificationTitle;
  @override
  final String? notificationBody;

  /// Create a copy of QueueReceiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QueueReceiveMessageCopyWith<_QueueReceiveMessage> get copyWith =>
      __$QueueReceiveMessageCopyWithImpl<_QueueReceiveMessage>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QueueReceiveMessageToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QueueReceiveMessage &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.notificationAction, notificationAction) ||
                other.notificationAction == notificationAction) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            (identical(other.stringifiedPayload, stringifiedPayload) ||
                other.stringifiedPayload == stringifiedPayload) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.notificationAvatarUrl, notificationAvatarUrl) ||
                other.notificationAvatarUrl == notificationAvatarUrl) &&
            (identical(other.notificationAvatarName, notificationAvatarName) ||
                other.notificationAvatarName == notificationAvatarName) &&
            (identical(other.notificationTitle, notificationTitle) ||
                other.notificationTitle == notificationTitle) &&
            (identical(other.notificationBody, notificationBody) ||
                other.notificationBody == notificationBody));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      notificationAction,
      messageCategory,
      stringifiedPayload,
      sessionId,
      notificationAvatarUrl,
      notificationAvatarName,
      notificationTitle,
      notificationBody);

  @override
  String toString() {
    return 'QueueReceiveMessage(timestamp: $timestamp, notificationId: $notificationId, notificationAction: $notificationAction, messageCategory: $messageCategory, stringifiedPayload: $stringifiedPayload, sessionId: $sessionId, notificationAvatarUrl: $notificationAvatarUrl, notificationAvatarName: $notificationAvatarName, notificationTitle: $notificationTitle, notificationBody: $notificationBody)';
  }
}

/// @nodoc
abstract mixin class _$QueueReceiveMessageCopyWith<$Res>
    implements $QueueReceiveMessageCopyWith<$Res> {
  factory _$QueueReceiveMessageCopyWith(_QueueReceiveMessage value,
          $Res Function(_QueueReceiveMessage) _then) =
      __$QueueReceiveMessageCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String? notificationAction,
      String messageCategory,
      String stringifiedPayload,
      String sessionId,
      String? notificationAvatarUrl,
      String? notificationAvatarName,
      String? notificationTitle,
      String? notificationBody});
}

/// @nodoc
class __$QueueReceiveMessageCopyWithImpl<$Res>
    implements _$QueueReceiveMessageCopyWith<$Res> {
  __$QueueReceiveMessageCopyWithImpl(this._self, this._then);

  final _QueueReceiveMessage _self;
  final $Res Function(_QueueReceiveMessage) _then;

  /// Create a copy of QueueReceiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? notificationAction = freezed,
    Object? messageCategory = null,
    Object? stringifiedPayload = null,
    Object? sessionId = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_QueueReceiveMessage(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAction: freezed == notificationAction
          ? _self.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      messageCategory: null == messageCategory
          ? _self.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      stringifiedPayload: null == stringifiedPayload
          ? _self.stringifiedPayload
          : stringifiedPayload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _self.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _self.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _self.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _self.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

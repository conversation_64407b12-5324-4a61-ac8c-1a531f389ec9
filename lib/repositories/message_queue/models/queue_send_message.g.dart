// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'queue_send_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_QueueSendMessage _$QueueSendMessageFromJson(Map json) => $checkedCreate(
      '_QueueSendMessage',
      json,
      ($checkedConvert) {
        final val = _QueueSendMessage(
          workTargetId: $checkedConvert(
              'workTargetId', (v) => const ParseSfIdConverter().from<PERSON>son(v)),
          messageId: $checkedConvert('messageId', (v) => v as String),
          type: $checkedConvert(
              'type', (v) => _queueSendMessageTypeFromJson(v as String)),
          messageContent:
              $checkedConvert('messageContent', (v) => v as String?),
          messagingDefinitionId: $checkedConvert('messagingDefinitionId',
              (v) => const ParseSfIdConverter().fromJson(v)),
          messagingEndUserId: $checkedConvert('messagingEndUserId',
              (v) => const ParseSfIdConverter().fromJson(v)),
          definitionId: $checkedConvert(
              'definitionId', (v) => const ParseSfIdConverter().fromJson(v)),
          definitionName:
              $checkedConvert('definitionName', (v) => v as String?),
          filePathsToAttach: $checkedConvert(
              'filePathsToAttach',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const <String>[]),
        );
        return val;
      },
    );

Map<String, dynamic> _$QueueSendMessageToJson(_QueueSendMessage instance) =>
    <String, dynamic>{
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.workTargetId, const ParseSfIdConverter().toJson)
          case final value?)
        'workTargetId': value,
      'messageId': instance.messageId,
      'type': _queueSendMessageTypeToJson(instance.type),
      if (instance.messageContent case final value?) 'messageContent': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.messagingDefinitionId, const ParseSfIdConverter().toJson)
          case final value?)
        'messagingDefinitionId': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.messagingEndUserId, const ParseSfIdConverter().toJson)
          case final value?)
        'messagingEndUserId': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.definitionId, const ParseSfIdConverter().toJson)
          case final value?)
        'definitionId': value,
      if (instance.definitionName case final value?) 'definitionName': value,
      'filePathsToAttach': instance.filePathsToAttach,
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);

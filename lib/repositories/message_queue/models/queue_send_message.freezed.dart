// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'queue_send_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QueueSendMessage {
  @ParseSfIdConverter()
  SfId? get workTargetId;
  String get messageId;
  @JsonKey(
      fromJson: _queueSendMessageTypeFromJson,
      toJson: _queueSendMessageTypeToJson)
  QueueSendMessageType get type;
  String? get messageContent;
  @ParseSfIdConverter()
  SfId? get messagingDefinitionId;
  @ParseSfIdConverter()
  SfId? get messagingEndUserId;
  @ParseSfIdConverter()
  SfId? get definitionId;
  String? get definitionName;
  @FileToPathConverter()
  List<String> get filePathsToAttach;

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QueueSendMessageCopyWith<QueueSendMessage> get copyWith =>
      _$QueueSendMessageCopyWithImpl<QueueSendMessage>(
          this as QueueSendMessage, _$identity);

  /// Serializes this QueueSendMessage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QueueSendMessage &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            (identical(other.messagingDefinitionId, messagingDefinitionId) ||
                other.messagingDefinitionId == messagingDefinitionId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.definitionId, definitionId) ||
                other.definitionId == definitionId) &&
            (identical(other.definitionName, definitionName) ||
                other.definitionName == definitionName) &&
            const DeepCollectionEquality()
                .equals(other.filePathsToAttach, filePathsToAttach));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      workTargetId,
      messageId,
      type,
      messageContent,
      messagingDefinitionId,
      messagingEndUserId,
      definitionId,
      definitionName,
      const DeepCollectionEquality().hash(filePathsToAttach));

  @override
  String toString() {
    return 'QueueSendMessage(workTargetId: $workTargetId, messageId: $messageId, type: $type, messageContent: $messageContent, messagingDefinitionId: $messagingDefinitionId, messagingEndUserId: $messagingEndUserId, definitionId: $definitionId, definitionName: $definitionName, filePathsToAttach: $filePathsToAttach)';
  }
}

/// @nodoc
abstract mixin class $QueueSendMessageCopyWith<$Res> {
  factory $QueueSendMessageCopyWith(
          QueueSendMessage value, $Res Function(QueueSendMessage) _then) =
      _$QueueSendMessageCopyWithImpl;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? workTargetId,
      String messageId,
      @JsonKey(
          fromJson: _queueSendMessageTypeFromJson,
          toJson: _queueSendMessageTypeToJson)
      QueueSendMessageType type,
      String? messageContent,
      @ParseSfIdConverter() SfId? messagingDefinitionId,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      @ParseSfIdConverter() SfId? definitionId,
      String? definitionName,
      @FileToPathConverter() List<String> filePathsToAttach});

  $SfIdCopyWith<$Res>? get workTargetId;
  $SfIdCopyWith<$Res>? get messagingDefinitionId;
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  $SfIdCopyWith<$Res>? get definitionId;
}

/// @nodoc
class _$QueueSendMessageCopyWithImpl<$Res>
    implements $QueueSendMessageCopyWith<$Res> {
  _$QueueSendMessageCopyWithImpl(this._self, this._then);

  final QueueSendMessage _self;
  final $Res Function(QueueSendMessage) _then;

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workTargetId = freezed,
    Object? messageId = null,
    Object? type = null,
    Object? messageContent = freezed,
    Object? messagingDefinitionId = freezed,
    Object? messagingEndUserId = freezed,
    Object? definitionId = freezed,
    Object? definitionName = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_self.copyWith(
      workTargetId: freezed == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as QueueSendMessageType,
      messageContent: freezed == messageContent
          ? _self.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionId: freezed == messagingDefinitionId
          ? _self.messagingDefinitionId
          : messagingDefinitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionId: freezed == definitionId
          ? _self.definitionId
          : definitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionName: freezed == definitionName
          ? _self.definitionName
          : definitionName // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _self.filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_self.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workTargetId!, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingDefinitionId {
    if (_self.messagingDefinitionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingDefinitionId!, (value) {
      return _then(_self.copyWith(messagingDefinitionId: value));
    });
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_self.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUserId!, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get definitionId {
    if (_self.definitionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.definitionId!, (value) {
      return _then(_self.copyWith(definitionId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [QueueSendMessage].
extension QueueSendMessagePatterns on QueueSendMessage {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_QueueSendMessage value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QueueSendMessage() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_QueueSendMessage value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueSendMessage():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_QueueSendMessage value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueSendMessage() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @ParseSfIdConverter() SfId? workTargetId,
            String messageId,
            @JsonKey(
                fromJson: _queueSendMessageTypeFromJson,
                toJson: _queueSendMessageTypeToJson)
            QueueSendMessageType type,
            String? messageContent,
            @ParseSfIdConverter() SfId? messagingDefinitionId,
            @ParseSfIdConverter() SfId? messagingEndUserId,
            @ParseSfIdConverter() SfId? definitionId,
            String? definitionName,
            @FileToPathConverter() List<String> filePathsToAttach)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QueueSendMessage() when $default != null:
        return $default(
            _that.workTargetId,
            _that.messageId,
            _that.type,
            _that.messageContent,
            _that.messagingDefinitionId,
            _that.messagingEndUserId,
            _that.definitionId,
            _that.definitionName,
            _that.filePathsToAttach);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @ParseSfIdConverter() SfId? workTargetId,
            String messageId,
            @JsonKey(
                fromJson: _queueSendMessageTypeFromJson,
                toJson: _queueSendMessageTypeToJson)
            QueueSendMessageType type,
            String? messageContent,
            @ParseSfIdConverter() SfId? messagingDefinitionId,
            @ParseSfIdConverter() SfId? messagingEndUserId,
            @ParseSfIdConverter() SfId? definitionId,
            String? definitionName,
            @FileToPathConverter() List<String> filePathsToAttach)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueSendMessage():
        return $default(
            _that.workTargetId,
            _that.messageId,
            _that.type,
            _that.messageContent,
            _that.messagingDefinitionId,
            _that.messagingEndUserId,
            _that.definitionId,
            _that.definitionName,
            _that.filePathsToAttach);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @ParseSfIdConverter() SfId? workTargetId,
            String messageId,
            @JsonKey(
                fromJson: _queueSendMessageTypeFromJson,
                toJson: _queueSendMessageTypeToJson)
            QueueSendMessageType type,
            String? messageContent,
            @ParseSfIdConverter() SfId? messagingDefinitionId,
            @ParseSfIdConverter() SfId? messagingEndUserId,
            @ParseSfIdConverter() SfId? definitionId,
            String? definitionName,
            @FileToPathConverter() List<String> filePathsToAttach)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QueueSendMessage() when $default != null:
        return $default(
            _that.workTargetId,
            _that.messageId,
            _that.type,
            _that.messageContent,
            _that.messagingDefinitionId,
            _that.messagingEndUserId,
            _that.definitionId,
            _that.definitionName,
            _that.filePathsToAttach);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _QueueSendMessage extends QueueSendMessage {
  const _QueueSendMessage(
      {@ParseSfIdConverter() this.workTargetId,
      required this.messageId,
      @JsonKey(
          fromJson: _queueSendMessageTypeFromJson,
          toJson: _queueSendMessageTypeToJson)
      required this.type,
      this.messageContent,
      @ParseSfIdConverter() this.messagingDefinitionId,
      @ParseSfIdConverter() this.messagingEndUserId,
      @ParseSfIdConverter() this.definitionId,
      this.definitionName,
      @FileToPathConverter()
      final List<String> filePathsToAttach = const <String>[]})
      : _filePathsToAttach = filePathsToAttach,
        super._();
  factory _QueueSendMessage.fromJson(Map<String, dynamic> json) =>
      _$QueueSendMessageFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId? workTargetId;
  @override
  final String messageId;
  @override
  @JsonKey(
      fromJson: _queueSendMessageTypeFromJson,
      toJson: _queueSendMessageTypeToJson)
  final QueueSendMessageType type;
  @override
  final String? messageContent;
  @override
  @ParseSfIdConverter()
  final SfId? messagingDefinitionId;
  @override
  @ParseSfIdConverter()
  final SfId? messagingEndUserId;
  @override
  @ParseSfIdConverter()
  final SfId? definitionId;
  @override
  final String? definitionName;
  final List<String> _filePathsToAttach;
  @override
  @JsonKey()
  @FileToPathConverter()
  List<String> get filePathsToAttach {
    if (_filePathsToAttach is EqualUnmodifiableListView)
      return _filePathsToAttach;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filePathsToAttach);
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QueueSendMessageCopyWith<_QueueSendMessage> get copyWith =>
      __$QueueSendMessageCopyWithImpl<_QueueSendMessage>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QueueSendMessageToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QueueSendMessage &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            (identical(other.messagingDefinitionId, messagingDefinitionId) ||
                other.messagingDefinitionId == messagingDefinitionId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.definitionId, definitionId) ||
                other.definitionId == definitionId) &&
            (identical(other.definitionName, definitionName) ||
                other.definitionName == definitionName) &&
            const DeepCollectionEquality()
                .equals(other._filePathsToAttach, _filePathsToAttach));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      workTargetId,
      messageId,
      type,
      messageContent,
      messagingDefinitionId,
      messagingEndUserId,
      definitionId,
      definitionName,
      const DeepCollectionEquality().hash(_filePathsToAttach));

  @override
  String toString() {
    return 'QueueSendMessage(workTargetId: $workTargetId, messageId: $messageId, type: $type, messageContent: $messageContent, messagingDefinitionId: $messagingDefinitionId, messagingEndUserId: $messagingEndUserId, definitionId: $definitionId, definitionName: $definitionName, filePathsToAttach: $filePathsToAttach)';
  }
}

/// @nodoc
abstract mixin class _$QueueSendMessageCopyWith<$Res>
    implements $QueueSendMessageCopyWith<$Res> {
  factory _$QueueSendMessageCopyWith(
          _QueueSendMessage value, $Res Function(_QueueSendMessage) _then) =
      __$QueueSendMessageCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? workTargetId,
      String messageId,
      @JsonKey(
          fromJson: _queueSendMessageTypeFromJson,
          toJson: _queueSendMessageTypeToJson)
      QueueSendMessageType type,
      String? messageContent,
      @ParseSfIdConverter() SfId? messagingDefinitionId,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      @ParseSfIdConverter() SfId? definitionId,
      String? definitionName,
      @FileToPathConverter() List<String> filePathsToAttach});

  @override
  $SfIdCopyWith<$Res>? get workTargetId;
  @override
  $SfIdCopyWith<$Res>? get messagingDefinitionId;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  @override
  $SfIdCopyWith<$Res>? get definitionId;
}

/// @nodoc
class __$QueueSendMessageCopyWithImpl<$Res>
    implements _$QueueSendMessageCopyWith<$Res> {
  __$QueueSendMessageCopyWithImpl(this._self, this._then);

  final _QueueSendMessage _self;
  final $Res Function(_QueueSendMessage) _then;

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? workTargetId = freezed,
    Object? messageId = null,
    Object? type = null,
    Object? messageContent = freezed,
    Object? messagingDefinitionId = freezed,
    Object? messagingEndUserId = freezed,
    Object? definitionId = freezed,
    Object? definitionName = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_QueueSendMessage(
      workTargetId: freezed == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as QueueSendMessageType,
      messageContent: freezed == messageContent
          ? _self.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionId: freezed == messagingDefinitionId
          ? _self.messagingDefinitionId
          : messagingDefinitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionId: freezed == definitionId
          ? _self.definitionId
          : definitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionName: freezed == definitionName
          ? _self.definitionName
          : definitionName // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _self._filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_self.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workTargetId!, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingDefinitionId {
    if (_self.messagingDefinitionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingDefinitionId!, (value) {
      return _then(_self.copyWith(messagingDefinitionId: value));
    });
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_self.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUserId!, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }

  /// Create a copy of QueueSendMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get definitionId {
    if (_self.definitionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.definitionId!, (value) {
      return _then(_self.copyWith(definitionId: value));
    });
  }
}

// dart format on

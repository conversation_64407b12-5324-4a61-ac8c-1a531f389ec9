// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'websocket_connection.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WebsocketConnection {
  WebsocketConnectionState get connectionState;

  /// Create a copy of WebsocketConnection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WebsocketConnectionCopyWith<WebsocketConnection> get copyWith =>
      _$WebsocketConnectionCopyWithImpl<WebsocketConnection>(
          this as WebsocketConnection, _$identity);

  /// Serializes this WebsocketConnection to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WebsocketConnection &&
            (identical(other.connectionState, connectionState) ||
                other.connectionState == connectionState));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, connectionState);

  @override
  String toString() {
    return 'WebsocketConnection(connectionState: $connectionState)';
  }
}

/// @nodoc
abstract mixin class $WebsocketConnectionCopyWith<$Res> {
  factory $WebsocketConnectionCopyWith(
          WebsocketConnection value, $Res Function(WebsocketConnection) _then) =
      _$WebsocketConnectionCopyWithImpl;
  @useResult
  $Res call({WebsocketConnectionState connectionState});
}

/// @nodoc
class _$WebsocketConnectionCopyWithImpl<$Res>
    implements $WebsocketConnectionCopyWith<$Res> {
  _$WebsocketConnectionCopyWithImpl(this._self, this._then);

  final WebsocketConnection _self;
  final $Res Function(WebsocketConnection) _then;

  /// Create a copy of WebsocketConnection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? connectionState = null,
  }) {
    return _then(_self.copyWith(
      connectionState: null == connectionState
          ? _self.connectionState
          : connectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnectionState,
    ));
  }
}

/// Adds pattern-matching-related methods to [WebsocketConnection].
extension WebsocketConnectionPatterns on WebsocketConnection {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WebsocketConnection value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WebsocketConnection() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WebsocketConnection value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketConnection():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WebsocketConnection value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketConnection() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(WebsocketConnectionState connectionState)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WebsocketConnection() when $default != null:
        return $default(_that.connectionState);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(WebsocketConnectionState connectionState) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketConnection():
        return $default(_that.connectionState);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(WebsocketConnectionState connectionState)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketConnection() when $default != null:
        return $default(_that.connectionState);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WebsocketConnection implements WebsocketConnection {
  const _WebsocketConnection(
      {this.connectionState = WebsocketConnectionState.disconnected});
  factory _WebsocketConnection.fromJson(Map<String, dynamic> json) =>
      _$WebsocketConnectionFromJson(json);

  @override
  @JsonKey()
  final WebsocketConnectionState connectionState;

  /// Create a copy of WebsocketConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WebsocketConnectionCopyWith<_WebsocketConnection> get copyWith =>
      __$WebsocketConnectionCopyWithImpl<_WebsocketConnection>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WebsocketConnectionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WebsocketConnection &&
            (identical(other.connectionState, connectionState) ||
                other.connectionState == connectionState));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, connectionState);

  @override
  String toString() {
    return 'WebsocketConnection(connectionState: $connectionState)';
  }
}

/// @nodoc
abstract mixin class _$WebsocketConnectionCopyWith<$Res>
    implements $WebsocketConnectionCopyWith<$Res> {
  factory _$WebsocketConnectionCopyWith(_WebsocketConnection value,
          $Res Function(_WebsocketConnection) _then) =
      __$WebsocketConnectionCopyWithImpl;
  @override
  @useResult
  $Res call({WebsocketConnectionState connectionState});
}

/// @nodoc
class __$WebsocketConnectionCopyWithImpl<$Res>
    implements _$WebsocketConnectionCopyWith<$Res> {
  __$WebsocketConnectionCopyWithImpl(this._self, this._then);

  final _WebsocketConnection _self;
  final $Res Function(_WebsocketConnection) _then;

  /// Create a copy of WebsocketConnection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? connectionState = null,
  }) {
    return _then(_WebsocketConnection(
      connectionState: null == connectionState
          ? _self.connectionState
          : connectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnectionState,
    ));
  }
}

// dart format on

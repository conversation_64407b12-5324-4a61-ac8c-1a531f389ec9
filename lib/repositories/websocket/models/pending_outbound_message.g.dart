// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pending_outbound_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PendingOutboundMessage _$PendingOutboundMessageFromJson(Map json) =>
    $checkedCreate(
      '_PendingOutboundMessage',
      json,
      ($checkedConvert) {
        final val = _PendingOutboundMessage(
          contactId: $checkedConvert('contactId', (v) => v as String?),
          messageId: $checkedConvert('messageId', (v) => v as String),
          messageContent:
              $checkedConvert('messageContent', (v) => v as String?),
          filePathsToAttach: $checkedConvert(
              'filePathsToAttach',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const <String>[]),
        );
        return val;
      },
    );

Map<String, dynamic> _$PendingOutboundMessageToJson(
        _PendingOutboundMessage instance) =>
    <String, dynamic>{
      if (instance.contactId case final value?) 'contactId': value,
      'messageId': instance.messageId,
      if (instance.messageContent case final value?) 'messageContent': value,
      'filePathsToAttach': instance.filePathsToAttach,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WebsocketParams _$WebsocketParamsFromJson(Map json) => $checkedCreate(
      '_WebsocketParams',
      json,
      ($checkedConvert) {
        final val = _WebsocketParams(
          uri: $checkedConvert(
              'uri', (v) => v == null ? null : Uri.parse(v as String)),
          headers: $checkedConvert(
              'headers',
              (v) => (v as Map?)?.map(
                    (k, e) => MapEntry(k as String, e),
                  )),
          protocols: $checkedConvert('protocols',
              (v) => (v as List<dynamic>?)?.map((e) => e as String)),
          pingInterval: $checkedConvert(
              'pingInterval',
              (v) => v == null
                  ? null
                  : Duration(microseconds: (v as num).toInt())),
          backoff: $checkedConvert(
              'backoff',
              (v) => const BackoffConverter()
                  .fromJson(v as Map<String, dynamic>?)),
          timeout: $checkedConvert(
              'timeout',
              (v) => v == null
                  ? null
                  : Duration(microseconds: (v as num).toInt())),
          binaryType: $checkedConvert('binaryType', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$WebsocketParamsToJson(_WebsocketParams instance) =>
    <String, dynamic>{
      if (instance.uri?.toString() case final value?) 'uri': value,
      if (instance.headers case final value?) 'headers': value,
      if (instance.protocols?.toList() case final value?) 'protocols': value,
      if (instance.pingInterval?.inMicroseconds case final value?)
        'pingInterval': value,
      if (const BackoffConverter().toJson(instance.backoff) case final value?)
        'backoff': value,
      if (instance.timeout?.inMicroseconds case final value?) 'timeout': value,
      if (instance.binaryType case final value?) 'binaryType': value,
    };

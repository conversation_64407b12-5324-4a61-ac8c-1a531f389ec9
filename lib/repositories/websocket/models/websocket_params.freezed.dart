// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'websocket_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WebsocketParams {
  Uri? get uri;
  Map<String, dynamic>? get headers;
  Iterable<String>? get protocols;
  Duration? get pingInterval;
  @BackoffConverter()
  Backoff? get backoff;
  Duration? get timeout;
  String? get binaryType;

  /// Create a copy of WebsocketParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WebsocketParamsCopyWith<WebsocketParams> get copyWith =>
      _$WebsocketParamsCopyWithImpl<WebsocketParams>(
          this as WebsocketParams, _$identity);

  /// Serializes this WebsocketParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WebsocketParams &&
            (identical(other.uri, uri) || other.uri == uri) &&
            const DeepCollectionEquality().equals(other.headers, headers) &&
            const DeepCollectionEquality().equals(other.protocols, protocols) &&
            (identical(other.pingInterval, pingInterval) ||
                other.pingInterval == pingInterval) &&
            (identical(other.backoff, backoff) || other.backoff == backoff) &&
            (identical(other.timeout, timeout) || other.timeout == timeout) &&
            (identical(other.binaryType, binaryType) ||
                other.binaryType == binaryType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      uri,
      const DeepCollectionEquality().hash(headers),
      const DeepCollectionEquality().hash(protocols),
      pingInterval,
      backoff,
      timeout,
      binaryType);

  @override
  String toString() {
    return 'WebsocketParams(uri: $uri, headers: $headers, protocols: $protocols, pingInterval: $pingInterval, backoff: $backoff, timeout: $timeout, binaryType: $binaryType)';
  }
}

/// @nodoc
abstract mixin class $WebsocketParamsCopyWith<$Res> {
  factory $WebsocketParamsCopyWith(
          WebsocketParams value, $Res Function(WebsocketParams) _then) =
      _$WebsocketParamsCopyWithImpl;
  @useResult
  $Res call(
      {Uri? uri,
      Map<String, dynamic>? headers,
      Iterable<String>? protocols,
      Duration? pingInterval,
      @BackoffConverter() Backoff? backoff,
      Duration? timeout,
      String? binaryType});
}

/// @nodoc
class _$WebsocketParamsCopyWithImpl<$Res>
    implements $WebsocketParamsCopyWith<$Res> {
  _$WebsocketParamsCopyWithImpl(this._self, this._then);

  final WebsocketParams _self;
  final $Res Function(WebsocketParams) _then;

  /// Create a copy of WebsocketParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uri = freezed,
    Object? headers = freezed,
    Object? protocols = freezed,
    Object? pingInterval = freezed,
    Object? backoff = freezed,
    Object? timeout = freezed,
    Object? binaryType = freezed,
  }) {
    return _then(_self.copyWith(
      uri: freezed == uri
          ? _self.uri
          : uri // ignore: cast_nullable_to_non_nullable
              as Uri?,
      headers: freezed == headers
          ? _self.headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      protocols: freezed == protocols
          ? _self.protocols
          : protocols // ignore: cast_nullable_to_non_nullable
              as Iterable<String>?,
      pingInterval: freezed == pingInterval
          ? _self.pingInterval
          : pingInterval // ignore: cast_nullable_to_non_nullable
              as Duration?,
      backoff: freezed == backoff
          ? _self.backoff
          : backoff // ignore: cast_nullable_to_non_nullable
              as Backoff?,
      timeout: freezed == timeout
          ? _self.timeout
          : timeout // ignore: cast_nullable_to_non_nullable
              as Duration?,
      binaryType: freezed == binaryType
          ? _self.binaryType
          : binaryType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [WebsocketParams].
extension WebsocketParamsPatterns on WebsocketParams {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WebsocketParams value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WebsocketParams() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WebsocketParams value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketParams():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WebsocketParams value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketParams() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Uri? uri,
            Map<String, dynamic>? headers,
            Iterable<String>? protocols,
            Duration? pingInterval,
            @BackoffConverter() Backoff? backoff,
            Duration? timeout,
            String? binaryType)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WebsocketParams() when $default != null:
        return $default(_that.uri, _that.headers, _that.protocols,
            _that.pingInterval, _that.backoff, _that.timeout, _that.binaryType);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Uri? uri,
            Map<String, dynamic>? headers,
            Iterable<String>? protocols,
            Duration? pingInterval,
            @BackoffConverter() Backoff? backoff,
            Duration? timeout,
            String? binaryType)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketParams():
        return $default(_that.uri, _that.headers, _that.protocols,
            _that.pingInterval, _that.backoff, _that.timeout, _that.binaryType);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Uri? uri,
            Map<String, dynamic>? headers,
            Iterable<String>? protocols,
            Duration? pingInterval,
            @BackoffConverter() Backoff? backoff,
            Duration? timeout,
            String? binaryType)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WebsocketParams() when $default != null:
        return $default(_that.uri, _that.headers, _that.protocols,
            _that.pingInterval, _that.backoff, _that.timeout, _that.binaryType);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WebsocketParams implements WebsocketParams {
  const _WebsocketParams(
      {this.uri,
      final Map<String, dynamic>? headers,
      this.protocols,
      this.pingInterval,
      @BackoffConverter() this.backoff,
      this.timeout,
      this.binaryType})
      : _headers = headers;
  factory _WebsocketParams.fromJson(Map<String, dynamic> json) =>
      _$WebsocketParamsFromJson(json);

  @override
  final Uri? uri;
  final Map<String, dynamic>? _headers;
  @override
  Map<String, dynamic>? get headers {
    final value = _headers;
    if (value == null) return null;
    if (_headers is EqualUnmodifiableMapView) return _headers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final Iterable<String>? protocols;
  @override
  final Duration? pingInterval;
  @override
  @BackoffConverter()
  final Backoff? backoff;
  @override
  final Duration? timeout;
  @override
  final String? binaryType;

  /// Create a copy of WebsocketParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WebsocketParamsCopyWith<_WebsocketParams> get copyWith =>
      __$WebsocketParamsCopyWithImpl<_WebsocketParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WebsocketParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WebsocketParams &&
            (identical(other.uri, uri) || other.uri == uri) &&
            const DeepCollectionEquality().equals(other._headers, _headers) &&
            const DeepCollectionEquality().equals(other.protocols, protocols) &&
            (identical(other.pingInterval, pingInterval) ||
                other.pingInterval == pingInterval) &&
            (identical(other.backoff, backoff) || other.backoff == backoff) &&
            (identical(other.timeout, timeout) || other.timeout == timeout) &&
            (identical(other.binaryType, binaryType) ||
                other.binaryType == binaryType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      uri,
      const DeepCollectionEquality().hash(_headers),
      const DeepCollectionEquality().hash(protocols),
      pingInterval,
      backoff,
      timeout,
      binaryType);

  @override
  String toString() {
    return 'WebsocketParams(uri: $uri, headers: $headers, protocols: $protocols, pingInterval: $pingInterval, backoff: $backoff, timeout: $timeout, binaryType: $binaryType)';
  }
}

/// @nodoc
abstract mixin class _$WebsocketParamsCopyWith<$Res>
    implements $WebsocketParamsCopyWith<$Res> {
  factory _$WebsocketParamsCopyWith(
          _WebsocketParams value, $Res Function(_WebsocketParams) _then) =
      __$WebsocketParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Uri? uri,
      Map<String, dynamic>? headers,
      Iterable<String>? protocols,
      Duration? pingInterval,
      @BackoffConverter() Backoff? backoff,
      Duration? timeout,
      String? binaryType});
}

/// @nodoc
class __$WebsocketParamsCopyWithImpl<$Res>
    implements _$WebsocketParamsCopyWith<$Res> {
  __$WebsocketParamsCopyWithImpl(this._self, this._then);

  final _WebsocketParams _self;
  final $Res Function(_WebsocketParams) _then;

  /// Create a copy of WebsocketParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? uri = freezed,
    Object? headers = freezed,
    Object? protocols = freezed,
    Object? pingInterval = freezed,
    Object? backoff = freezed,
    Object? timeout = freezed,
    Object? binaryType = freezed,
  }) {
    return _then(_WebsocketParams(
      uri: freezed == uri
          ? _self.uri
          : uri // ignore: cast_nullable_to_non_nullable
              as Uri?,
      headers: freezed == headers
          ? _self._headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      protocols: freezed == protocols
          ? _self.protocols
          : protocols // ignore: cast_nullable_to_non_nullable
              as Iterable<String>?,
      pingInterval: freezed == pingInterval
          ? _self.pingInterval
          : pingInterval // ignore: cast_nullable_to_non_nullable
              as Duration?,
      backoff: freezed == backoff
          ? _self.backoff
          : backoff // ignore: cast_nullable_to_non_nullable
              as Backoff?,
      timeout: freezed == timeout
          ? _self.timeout
          : timeout // ignore: cast_nullable_to_non_nullable
              as Duration?,
      binaryType: freezed == binaryType
          ? _self.binaryType
          : binaryType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

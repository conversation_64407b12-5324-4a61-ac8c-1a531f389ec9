// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_connection.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WebsocketConnection _$WebsocketConnectionFromJson(Map json) => $checkedCreate(
      '_WebsocketConnection',
      json,
      ($checkedConvert) {
        final val = _WebsocketConnection(
          connectionState: $checkedConvert(
              'connectionState',
              (v) =>
                  $enumDecodeNullable(_$WebsocketConnectionStateEnumMap, v) ??
                  WebsocketConnectionState.disconnected),
        );
        return val;
      },
    );

Map<String, dynamic> _$WebsocketConnectionToJson(
        _WebsocketConnection instance) =>
    <String, dynamic>{
      'connectionState':
          _$WebsocketConnectionStateEnumMap[instance.connectionState]!,
    };

const _$WebsocketConnectionStateEnumMap = {
  WebsocketConnectionState.connecting: 'connecting',
  WebsocketConnectionState.connected: 'connected',
  WebsocketConnectionState.reconnecting: 'reconnecting',
  WebsocketConnectionState.reconnected: 'reconnected',
  WebsocketConnectionState.disconnecting: 'disconnecting',
  WebsocketConnectionState.disconnected: 'disconnected',
};

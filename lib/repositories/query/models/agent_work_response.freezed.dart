// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'agent_work_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AgentWorkResponse {
  Attributes? get attributes;
  @JsonKey(name: 'Id')
  String? get id;
  @JsonKey(name: 'Status')
  String? get status;
  @JsonKey(name: 'LastModifiedDate')
  String? get lastModifiedDate;
  @JsonKey(name: 'WorkItemId')
  String? get workItemId;

  /// Create a copy of AgentWorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AgentWorkResponseCopyWith<AgentWorkResponse> get copyWith =>
      _$AgentWorkResponseCopyWithImpl<AgentWorkResponse>(
          this as AgentWorkResponse, _$identity);

  /// Serializes this AgentWorkResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AgentWorkResponse &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.workItemId, workItemId) ||
                other.workItemId == workItemId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, attributes, id, status, lastModifiedDate, workItemId);

  @override
  String toString() {
    return 'AgentWorkResponse(attributes: $attributes, id: $id, status: $status, lastModifiedDate: $lastModifiedDate, workItemId: $workItemId)';
  }
}

/// @nodoc
abstract mixin class $AgentWorkResponseCopyWith<$Res> {
  factory $AgentWorkResponseCopyWith(
          AgentWorkResponse value, $Res Function(AgentWorkResponse) _then) =
      _$AgentWorkResponseCopyWithImpl;
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Status') String? status,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'WorkItemId') String? workItemId});

  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class _$AgentWorkResponseCopyWithImpl<$Res>
    implements $AgentWorkResponseCopyWith<$Res> {
  _$AgentWorkResponseCopyWithImpl(this._self, this._then);

  final AgentWorkResponse _self;
  final $Res Function(AgentWorkResponse) _then;

  /// Create a copy of AgentWorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? status = freezed,
    Object? lastModifiedDate = freezed,
    Object? workItemId = freezed,
  }) {
    return _then(_self.copyWith(
      attributes: freezed == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      workItemId: freezed == workItemId
          ? _self.workItemId
          : workItemId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of AgentWorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res>? get attributes {
    if (_self.attributes == null) {
      return null;
    }

    return $AttributesCopyWith<$Res>(_self.attributes!, (value) {
      return _then(_self.copyWith(attributes: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AgentWorkResponse].
extension AgentWorkResponsePatterns on AgentWorkResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AgentWorkResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AgentWorkResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AgentWorkResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWorkResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AgentWorkResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWorkResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Attributes? attributes,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Status') String? status,
            @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
            @JsonKey(name: 'WorkItemId') String? workItemId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AgentWorkResponse() when $default != null:
        return $default(_that.attributes, _that.id, _that.status,
            _that.lastModifiedDate, _that.workItemId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Attributes? attributes,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Status') String? status,
            @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
            @JsonKey(name: 'WorkItemId') String? workItemId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWorkResponse():
        return $default(_that.attributes, _that.id, _that.status,
            _that.lastModifiedDate, _that.workItemId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Attributes? attributes,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Status') String? status,
            @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
            @JsonKey(name: 'WorkItemId') String? workItemId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AgentWorkResponse() when $default != null:
        return $default(_that.attributes, _that.id, _that.status,
            _that.lastModifiedDate, _that.workItemId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AgentWorkResponse implements AgentWorkResponse {
  const _AgentWorkResponse(
      {this.attributes,
      @JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'Status') this.status,
      @JsonKey(name: 'LastModifiedDate') this.lastModifiedDate,
      @JsonKey(name: 'WorkItemId') this.workItemId});
  factory _AgentWorkResponse.fromJson(Map<String, dynamic> json) =>
      _$AgentWorkResponseFromJson(json);

  @override
  final Attributes? attributes;
  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'Status')
  final String? status;
  @override
  @JsonKey(name: 'LastModifiedDate')
  final String? lastModifiedDate;
  @override
  @JsonKey(name: 'WorkItemId')
  final String? workItemId;

  /// Create a copy of AgentWorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AgentWorkResponseCopyWith<_AgentWorkResponse> get copyWith =>
      __$AgentWorkResponseCopyWithImpl<_AgentWorkResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AgentWorkResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AgentWorkResponse &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.workItemId, workItemId) ||
                other.workItemId == workItemId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, attributes, id, status, lastModifiedDate, workItemId);

  @override
  String toString() {
    return 'AgentWorkResponse(attributes: $attributes, id: $id, status: $status, lastModifiedDate: $lastModifiedDate, workItemId: $workItemId)';
  }
}

/// @nodoc
abstract mixin class _$AgentWorkResponseCopyWith<$Res>
    implements $AgentWorkResponseCopyWith<$Res> {
  factory _$AgentWorkResponseCopyWith(
          _AgentWorkResponse value, $Res Function(_AgentWorkResponse) _then) =
      __$AgentWorkResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Status') String? status,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'WorkItemId') String? workItemId});

  @override
  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class __$AgentWorkResponseCopyWithImpl<$Res>
    implements _$AgentWorkResponseCopyWith<$Res> {
  __$AgentWorkResponseCopyWithImpl(this._self, this._then);

  final _AgentWorkResponse _self;
  final $Res Function(_AgentWorkResponse) _then;

  /// Create a copy of AgentWorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? status = freezed,
    Object? lastModifiedDate = freezed,
    Object? workItemId = freezed,
  }) {
    return _then(_AgentWorkResponse(
      attributes: freezed == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      workItemId: freezed == workItemId
          ? _self.workItemId
          : workItemId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of AgentWorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res>? get attributes {
    if (_self.attributes == null) {
      return null;
    }

    return $AttributesCopyWith<$Res>(_self.attributes!, (value) {
      return _then(_self.copyWith(attributes: value));
    });
  }
}

// dart format on

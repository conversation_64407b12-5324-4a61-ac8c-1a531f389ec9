// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_work_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AgentWorkResponse _$AgentWorkResponseFromJson(Map json) => $checkedCreate(
      '_AgentWorkResponse',
      json,
      ($checkedConvert) {
        final val = _AgentWorkResponse(
          attributes: $checkedConvert(
              'attributes',
              (v) => v == null
                  ? null
                  : Attributes.fromJson(Map<String, dynamic>.from(v as Map))),
          id: $checkedConvert('Id', (v) => v as String?),
          status: $checkedConvert('Status', (v) => v as String?),
          lastModifiedDate:
              $checkedConvert('LastModifiedDate', (v) => v as String?),
          workItemId: $checkedConvert('WorkItemId', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'id': 'Id',
        'status': 'Status',
        'lastModifiedDate': 'LastModifiedDate',
        'workItemId': 'WorkItemId'
      },
    );

Map<String, dynamic> _$AgentWorkResponseToJson(_AgentWorkResponse instance) =>
    <String, dynamic>{
      if (instance.attributes?.toJson() case final value?) 'attributes': value,
      if (instance.id case final value?) 'Id': value,
      if (instance.status case final value?) 'Status': value,
      if (instance.lastModifiedDate case final value?)
        'LastModifiedDate': value,
      if (instance.workItemId case final value?) 'WorkItemId': value,
    };

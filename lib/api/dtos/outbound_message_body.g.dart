// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'outbound_message_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OutboundMessageBody _$OutboundMessageBodyFromJson(Map json) => $checkedCreate(
      '_OutboundMessageBody',
      json,
      ($checkedConvert) {
        final val = _OutboundMessageBody(
          messageId: $checkedConvert('messageId', (v) => v as String),
          messagingEndUserId: $checkedConvert('messagingEndUserId',
              (v) => const ParseSfIdConverter().fromJson(v)),
          messageBody: $checkedConvert('messageBody', (v) => v as String?),
          messagingDefinitionParameters: $checkedConvert(
              'messagingDefinitionParameters',
              (v) => v == null
                  ? null
                  : MessagingDefinitionParameters.fromJson(
                      Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$OutboundMessageBodyToJson(
        _OutboundMessageBody instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      if (const ParseSfIdConverter().toJson(instance.messagingEndUserId)
          case final value?)
        'messagingEndUserId': value,
      if (instance.messageBody case final value?) 'messageBody': value,
      if (instance.messagingDefinitionParameters?.toJson() case final value?)
        'messagingDefinitionParameters': value,
    };

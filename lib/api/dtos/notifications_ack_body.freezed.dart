// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notifications_ack_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$NotificationsAckBody {
  List<String> get notificationIds;

  /// Create a copy of NotificationsAckBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NotificationsAckBodyCopyWith<NotificationsAckBody> get copyWith =>
      _$NotificationsAckBodyCopyWithImpl<NotificationsAckBody>(
          this as NotificationsAckBody, _$identity);

  /// Serializes this NotificationsAckBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NotificationsAckBody &&
            const DeepCollectionEquality()
                .equals(other.notificationIds, notificationIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(notificationIds));

  @override
  String toString() {
    return 'NotificationsAckBody(notificationIds: $notificationIds)';
  }
}

/// @nodoc
abstract mixin class $NotificationsAckBodyCopyWith<$Res> {
  factory $NotificationsAckBodyCopyWith(NotificationsAckBody value,
          $Res Function(NotificationsAckBody) _then) =
      _$NotificationsAckBodyCopyWithImpl;
  @useResult
  $Res call({List<String> notificationIds});
}

/// @nodoc
class _$NotificationsAckBodyCopyWithImpl<$Res>
    implements $NotificationsAckBodyCopyWith<$Res> {
  _$NotificationsAckBodyCopyWithImpl(this._self, this._then);

  final NotificationsAckBody _self;
  final $Res Function(NotificationsAckBody) _then;

  /// Create a copy of NotificationsAckBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationIds = null,
  }) {
    return _then(_self.copyWith(
      notificationIds: null == notificationIds
          ? _self.notificationIds
          : notificationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// Adds pattern-matching-related methods to [NotificationsAckBody].
extension NotificationsAckBodyPatterns on NotificationsAckBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_NotificationsAckBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _NotificationsAckBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_NotificationsAckBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsAckBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_NotificationsAckBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsAckBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<String> notificationIds)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _NotificationsAckBody() when $default != null:
        return $default(_that.notificationIds);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<String> notificationIds) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsAckBody():
        return $default(_that.notificationIds);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<String> notificationIds)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsAckBody() when $default != null:
        return $default(_that.notificationIds);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _NotificationsAckBody implements NotificationsAckBody {
  const _NotificationsAckBody({required final List<String> notificationIds})
      : _notificationIds = notificationIds;
  factory _NotificationsAckBody.fromJson(Map<String, dynamic> json) =>
      _$NotificationsAckBodyFromJson(json);

  final List<String> _notificationIds;
  @override
  List<String> get notificationIds {
    if (_notificationIds is EqualUnmodifiableListView) return _notificationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_notificationIds);
  }

  /// Create a copy of NotificationsAckBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NotificationsAckBodyCopyWith<_NotificationsAckBody> get copyWith =>
      __$NotificationsAckBodyCopyWithImpl<_NotificationsAckBody>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$NotificationsAckBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NotificationsAckBody &&
            const DeepCollectionEquality()
                .equals(other._notificationIds, _notificationIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_notificationIds));

  @override
  String toString() {
    return 'NotificationsAckBody(notificationIds: $notificationIds)';
  }
}

/// @nodoc
abstract mixin class _$NotificationsAckBodyCopyWith<$Res>
    implements $NotificationsAckBodyCopyWith<$Res> {
  factory _$NotificationsAckBodyCopyWith(_NotificationsAckBody value,
          $Res Function(_NotificationsAckBody) _then) =
      __$NotificationsAckBodyCopyWithImpl;
  @override
  @useResult
  $Res call({List<String> notificationIds});
}

/// @nodoc
class __$NotificationsAckBodyCopyWithImpl<$Res>
    implements _$NotificationsAckBodyCopyWith<$Res> {
  __$NotificationsAckBodyCopyWithImpl(this._self, this._then);

  final _NotificationsAckBody _self;
  final $Res Function(_NotificationsAckBody) _then;

  /// Create a copy of NotificationsAckBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? notificationIds = null,
  }) {
    return _then(_NotificationsAckBody(
      notificationIds: null == notificationIds
          ? _self._notificationIds
          : notificationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_event_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LogEventBody {
  List<LogEvent> get events;

  /// Create a copy of LogEventBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LogEventBodyCopyWith<LogEventBody> get copyWith =>
      _$LogEventBodyCopyWithImpl<LogEventBody>(
          this as LogEventBody, _$identity);

  /// Serializes this LogEventBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LogEventBody &&
            const DeepCollectionEquality().equals(other.events, events));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(events));

  @override
  String toString() {
    return 'LogEventBody(events: $events)';
  }
}

/// @nodoc
abstract mixin class $LogEventBodyCopyWith<$Res> {
  factory $LogEventBodyCopyWith(
          LogEventBody value, $Res Function(LogEventBody) _then) =
      _$LogEventBodyCopyWithImpl;
  @useResult
  $Res call({List<LogEvent> events});
}

/// @nodoc
class _$LogEventBodyCopyWithImpl<$Res> implements $LogEventBodyCopyWith<$Res> {
  _$LogEventBodyCopyWithImpl(this._self, this._then);

  final LogEventBody _self;
  final $Res Function(LogEventBody) _then;

  /// Create a copy of LogEventBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? events = null,
  }) {
    return _then(_self.copyWith(
      events: null == events
          ? _self.events
          : events // ignore: cast_nullable_to_non_nullable
              as List<LogEvent>,
    ));
  }
}

/// Adds pattern-matching-related methods to [LogEventBody].
extension LogEventBodyPatterns on LogEventBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_LogEventBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogEventBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_LogEventBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_LogEventBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<LogEvent> events)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogEventBody() when $default != null:
        return $default(_that.events);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<LogEvent> events) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventBody():
        return $default(_that.events);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<LogEvent> events)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventBody() when $default != null:
        return $default(_that.events);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _LogEventBody implements LogEventBody {
  const _LogEventBody({required final List<LogEvent> events})
      : _events = events;
  factory _LogEventBody.fromJson(Map<String, dynamic> json) =>
      _$LogEventBodyFromJson(json);

  final List<LogEvent> _events;
  @override
  List<LogEvent> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  /// Create a copy of LogEventBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LogEventBodyCopyWith<_LogEventBody> get copyWith =>
      __$LogEventBodyCopyWithImpl<_LogEventBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LogEventBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LogEventBody &&
            const DeepCollectionEquality().equals(other._events, _events));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_events));

  @override
  String toString() {
    return 'LogEventBody(events: $events)';
  }
}

/// @nodoc
abstract mixin class _$LogEventBodyCopyWith<$Res>
    implements $LogEventBodyCopyWith<$Res> {
  factory _$LogEventBodyCopyWith(
          _LogEventBody value, $Res Function(_LogEventBody) _then) =
      __$LogEventBodyCopyWithImpl;
  @override
  @useResult
  $Res call({List<LogEvent> events});
}

/// @nodoc
class __$LogEventBodyCopyWithImpl<$Res>
    implements _$LogEventBodyCopyWith<$Res> {
  __$LogEventBodyCopyWithImpl(this._self, this._then);

  final _LogEventBody _self;
  final $Res Function(_LogEventBody) _then;

  /// Create a copy of LogEventBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? events = null,
  }) {
    return _then(_LogEventBody(
      events: null == events
          ? _self._events
          : events // ignore: cast_nullable_to_non_nullable
              as List<LogEvent>,
    ));
  }
}

// dart format on

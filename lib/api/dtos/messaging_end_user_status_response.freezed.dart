// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_status_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingEndUserStatusResponse {
  bool get canSendOutbound;

  /// Create a copy of MessagingEndUserStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingEndUserStatusResponseCopyWith<MessagingEndUserStatusResponse>
      get copyWith => _$MessagingEndUserStatusResponseCopyWithImpl<
              MessagingEndUserStatusResponse>(
          this as MessagingEndUserStatusResponse, _$identity);

  /// Serializes this MessagingEndUserStatusResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingEndUserStatusResponse &&
            (identical(other.canSendOutbound, canSendOutbound) ||
                other.canSendOutbound == canSendOutbound));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, canSendOutbound);

  @override
  String toString() {
    return 'MessagingEndUserStatusResponse(canSendOutbound: $canSendOutbound)';
  }
}

/// @nodoc
abstract mixin class $MessagingEndUserStatusResponseCopyWith<$Res> {
  factory $MessagingEndUserStatusResponseCopyWith(
          MessagingEndUserStatusResponse value,
          $Res Function(MessagingEndUserStatusResponse) _then) =
      _$MessagingEndUserStatusResponseCopyWithImpl;
  @useResult
  $Res call({bool canSendOutbound});
}

/// @nodoc
class _$MessagingEndUserStatusResponseCopyWithImpl<$Res>
    implements $MessagingEndUserStatusResponseCopyWith<$Res> {
  _$MessagingEndUserStatusResponseCopyWithImpl(this._self, this._then);

  final MessagingEndUserStatusResponse _self;
  final $Res Function(MessagingEndUserStatusResponse) _then;

  /// Create a copy of MessagingEndUserStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canSendOutbound = null,
  }) {
    return _then(_self.copyWith(
      canSendOutbound: null == canSendOutbound
          ? _self.canSendOutbound
          : canSendOutbound // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [MessagingEndUserStatusResponse].
extension MessagingEndUserStatusResponsePatterns
    on MessagingEndUserStatusResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingEndUserStatusResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserStatusResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingEndUserStatusResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserStatusResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingEndUserStatusResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserStatusResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool canSendOutbound)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserStatusResponse() when $default != null:
        return $default(_that.canSendOutbound);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool canSendOutbound) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserStatusResponse():
        return $default(_that.canSendOutbound);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool canSendOutbound)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserStatusResponse() when $default != null:
        return $default(_that.canSendOutbound);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingEndUserStatusResponse extends MessagingEndUserStatusResponse {
  const _MessagingEndUserStatusResponse({required this.canSendOutbound})
      : super._();
  factory _MessagingEndUserStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserStatusResponseFromJson(json);

  @override
  final bool canSendOutbound;

  /// Create a copy of MessagingEndUserStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingEndUserStatusResponseCopyWith<_MessagingEndUserStatusResponse>
      get copyWith => __$MessagingEndUserStatusResponseCopyWithImpl<
          _MessagingEndUserStatusResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingEndUserStatusResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingEndUserStatusResponse &&
            (identical(other.canSendOutbound, canSendOutbound) ||
                other.canSendOutbound == canSendOutbound));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, canSendOutbound);

  @override
  String toString() {
    return 'MessagingEndUserStatusResponse(canSendOutbound: $canSendOutbound)';
  }
}

/// @nodoc
abstract mixin class _$MessagingEndUserStatusResponseCopyWith<$Res>
    implements $MessagingEndUserStatusResponseCopyWith<$Res> {
  factory _$MessagingEndUserStatusResponseCopyWith(
          _MessagingEndUserStatusResponse value,
          $Res Function(_MessagingEndUserStatusResponse) _then) =
      __$MessagingEndUserStatusResponseCopyWithImpl;
  @override
  @useResult
  $Res call({bool canSendOutbound});
}

/// @nodoc
class __$MessagingEndUserStatusResponseCopyWithImpl<$Res>
    implements _$MessagingEndUserStatusResponseCopyWith<$Res> {
  __$MessagingEndUserStatusResponseCopyWithImpl(this._self, this._then);

  final _MessagingEndUserStatusResponse _self;
  final $Res Function(_MessagingEndUserStatusResponse) _then;

  /// Create a copy of MessagingEndUserStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? canSendOutbound = null,
  }) {
    return _then(_MessagingEndUserStatusResponse(
      canSendOutbound: null == canSendOutbound
          ? _self.canSendOutbound
          : canSendOutbound // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

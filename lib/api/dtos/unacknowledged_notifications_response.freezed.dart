// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unacknowledged_notifications_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UnacknowledgedNotificationsResponse {
  List<NotificationMessage>? get notifications;
  String? get nextToken;
  bool? get sessionActive;

  /// Create a copy of UnacknowledgedNotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnacknowledgedNotificationsResponseCopyWith<
          UnacknowledgedNotificationsResponse>
      get copyWith => _$UnacknowledgedNotificationsResponseCopyWithImpl<
              UnacknowledgedNotificationsResponse>(
          this as UnacknowledgedNotificationsResponse, _$identity);

  /// Serializes this UnacknowledgedNotificationsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnacknowledgedNotificationsResponse &&
            const DeepCollectionEquality()
                .equals(other.notifications, notifications) &&
            (identical(other.nextToken, nextToken) ||
                other.nextToken == nextToken) &&
            (identical(other.sessionActive, sessionActive) ||
                other.sessionActive == sessionActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(notifications),
      nextToken,
      sessionActive);

  @override
  String toString() {
    return 'UnacknowledgedNotificationsResponse(notifications: $notifications, nextToken: $nextToken, sessionActive: $sessionActive)';
  }
}

/// @nodoc
abstract mixin class $UnacknowledgedNotificationsResponseCopyWith<$Res> {
  factory $UnacknowledgedNotificationsResponseCopyWith(
          UnacknowledgedNotificationsResponse value,
          $Res Function(UnacknowledgedNotificationsResponse) _then) =
      _$UnacknowledgedNotificationsResponseCopyWithImpl;
  @useResult
  $Res call(
      {List<NotificationMessage>? notifications,
      String? nextToken,
      bool? sessionActive});
}

/// @nodoc
class _$UnacknowledgedNotificationsResponseCopyWithImpl<$Res>
    implements $UnacknowledgedNotificationsResponseCopyWith<$Res> {
  _$UnacknowledgedNotificationsResponseCopyWithImpl(this._self, this._then);

  final UnacknowledgedNotificationsResponse _self;
  final $Res Function(UnacknowledgedNotificationsResponse) _then;

  /// Create a copy of UnacknowledgedNotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notifications = freezed,
    Object? nextToken = freezed,
    Object? sessionActive = freezed,
  }) {
    return _then(_self.copyWith(
      notifications: freezed == notifications
          ? _self.notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationMessage>?,
      nextToken: freezed == nextToken
          ? _self.nextToken
          : nextToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionActive: freezed == sessionActive
          ? _self.sessionActive
          : sessionActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// Adds pattern-matching-related methods to [UnacknowledgedNotificationsResponse].
extension UnacknowledgedNotificationsResponsePatterns
    on UnacknowledgedNotificationsResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UnacknowledgedNotificationsResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UnacknowledgedNotificationsResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UnacknowledgedNotificationsResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<NotificationMessage>? notifications,
            String? nextToken, bool? sessionActive)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsResponse() when $default != null:
        return $default(
            _that.notifications, _that.nextToken, _that.sessionActive);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<NotificationMessage>? notifications,
            String? nextToken, bool? sessionActive)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsResponse():
        return $default(
            _that.notifications, _that.nextToken, _that.sessionActive);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<NotificationMessage>? notifications,
            String? nextToken, bool? sessionActive)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsResponse() when $default != null:
        return $default(
            _that.notifications, _that.nextToken, _that.sessionActive);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UnacknowledgedNotificationsResponse
    implements UnacknowledgedNotificationsResponse {
  const _UnacknowledgedNotificationsResponse(
      {final List<NotificationMessage>? notifications,
      this.nextToken,
      this.sessionActive})
      : _notifications = notifications;
  factory _UnacknowledgedNotificationsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$UnacknowledgedNotificationsResponseFromJson(json);

  final List<NotificationMessage>? _notifications;
  @override
  List<NotificationMessage>? get notifications {
    final value = _notifications;
    if (value == null) return null;
    if (_notifications is EqualUnmodifiableListView) return _notifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? nextToken;
  @override
  final bool? sessionActive;

  /// Create a copy of UnacknowledgedNotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UnacknowledgedNotificationsResponseCopyWith<
          _UnacknowledgedNotificationsResponse>
      get copyWith => __$UnacknowledgedNotificationsResponseCopyWithImpl<
          _UnacknowledgedNotificationsResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UnacknowledgedNotificationsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UnacknowledgedNotificationsResponse &&
            const DeepCollectionEquality()
                .equals(other._notifications, _notifications) &&
            (identical(other.nextToken, nextToken) ||
                other.nextToken == nextToken) &&
            (identical(other.sessionActive, sessionActive) ||
                other.sessionActive == sessionActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_notifications),
      nextToken,
      sessionActive);

  @override
  String toString() {
    return 'UnacknowledgedNotificationsResponse(notifications: $notifications, nextToken: $nextToken, sessionActive: $sessionActive)';
  }
}

/// @nodoc
abstract mixin class _$UnacknowledgedNotificationsResponseCopyWith<$Res>
    implements $UnacknowledgedNotificationsResponseCopyWith<$Res> {
  factory _$UnacknowledgedNotificationsResponseCopyWith(
          _UnacknowledgedNotificationsResponse value,
          $Res Function(_UnacknowledgedNotificationsResponse) _then) =
      __$UnacknowledgedNotificationsResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<NotificationMessage>? notifications,
      String? nextToken,
      bool? sessionActive});
}

/// @nodoc
class __$UnacknowledgedNotificationsResponseCopyWithImpl<$Res>
    implements _$UnacknowledgedNotificationsResponseCopyWith<$Res> {
  __$UnacknowledgedNotificationsResponseCopyWithImpl(this._self, this._then);

  final _UnacknowledgedNotificationsResponse _self;
  final $Res Function(_UnacknowledgedNotificationsResponse) _then;

  /// Create a copy of UnacknowledgedNotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? notifications = freezed,
    Object? nextToken = freezed,
    Object? sessionActive = freezed,
  }) {
    return _then(_UnacknowledgedNotificationsResponse(
      notifications: freezed == notifications
          ? _self._notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationMessage>?,
      nextToken: freezed == nextToken
          ? _self.nextToken
          : nextToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionActive: freezed == sessionActive
          ? _self.sessionActive
          : sessionActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

// dart format on

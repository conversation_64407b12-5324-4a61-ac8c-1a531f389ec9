// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'send_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SendFailure {
  String get messageId;
  String get reason;

  /// Create a copy of SendFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SendFailureCopyWith<SendFailure> get copyWith =>
      _$SendFailureCopyWithImpl<SendFailure>(this as SendFailure, _$identity);

  /// Serializes this SendFailure to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SendFailure &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, reason);

  @override
  String toString() {
    return 'SendFailure(messageId: $messageId, reason: $reason)';
  }
}

/// @nodoc
abstract mixin class $SendFailureCopyWith<$Res> {
  factory $SendFailureCopyWith(
          SendFailure value, $Res Function(SendFailure) _then) =
      _$SendFailureCopyWithImpl;
  @useResult
  $Res call({String messageId, String reason});
}

/// @nodoc
class _$SendFailureCopyWithImpl<$Res> implements $SendFailureCopyWith<$Res> {
  _$SendFailureCopyWithImpl(this._self, this._then);

  final SendFailure _self;
  final $Res Function(SendFailure) _then;

  /// Create a copy of SendFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? reason = null,
  }) {
    return _then(_self.copyWith(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _self.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [SendFailure].
extension SendFailurePatterns on SendFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SendFailure value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SendFailure() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SendFailure value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendFailure():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SendFailure value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendFailure() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String messageId, String reason)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SendFailure() when $default != null:
        return $default(_that.messageId, _that.reason);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String messageId, String reason) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendFailure():
        return $default(_that.messageId, _that.reason);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String messageId, String reason)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendFailure() when $default != null:
        return $default(_that.messageId, _that.reason);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SendFailure implements SendFailure {
  const _SendFailure({required this.messageId, required this.reason});
  factory _SendFailure.fromJson(Map<String, dynamic> json) =>
      _$SendFailureFromJson(json);

  @override
  final String messageId;
  @override
  final String reason;

  /// Create a copy of SendFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SendFailureCopyWith<_SendFailure> get copyWith =>
      __$SendFailureCopyWithImpl<_SendFailure>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SendFailureToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SendFailure &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, reason);

  @override
  String toString() {
    return 'SendFailure(messageId: $messageId, reason: $reason)';
  }
}

/// @nodoc
abstract mixin class _$SendFailureCopyWith<$Res>
    implements $SendFailureCopyWith<$Res> {
  factory _$SendFailureCopyWith(
          _SendFailure value, $Res Function(_SendFailure) _then) =
      __$SendFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String messageId, String reason});
}

/// @nodoc
class __$SendFailureCopyWithImpl<$Res> implements _$SendFailureCopyWith<$Res> {
  __$SendFailureCopyWithImpl(this._self, this._then);

  final _SendFailure _self;
  final $Res Function(_SendFailure) _then;

  /// Create a copy of SendFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageId = null,
    Object? reason = null,
  }) {
    return _then(_SendFailure(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _self.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_messages_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OutboundMessagesBody {
  List<OutboundMessageBody> get messages;

  /// Create a copy of OutboundMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OutboundMessagesBodyCopyWith<OutboundMessagesBody> get copyWith =>
      _$OutboundMessagesBodyCopyWithImpl<OutboundMessagesBody>(
          this as OutboundMessagesBody, _$identity);

  /// Serializes this OutboundMessagesBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OutboundMessagesBody &&
            const DeepCollectionEquality().equals(other.messages, messages));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(messages));

  @override
  String toString() {
    return 'OutboundMessagesBody(messages: $messages)';
  }
}

/// @nodoc
abstract mixin class $OutboundMessagesBodyCopyWith<$Res> {
  factory $OutboundMessagesBodyCopyWith(OutboundMessagesBody value,
          $Res Function(OutboundMessagesBody) _then) =
      _$OutboundMessagesBodyCopyWithImpl;
  @useResult
  $Res call({List<OutboundMessageBody> messages});
}

/// @nodoc
class _$OutboundMessagesBodyCopyWithImpl<$Res>
    implements $OutboundMessagesBodyCopyWith<$Res> {
  _$OutboundMessagesBodyCopyWithImpl(this._self, this._then);

  final OutboundMessagesBody _self;
  final $Res Function(OutboundMessagesBody) _then;

  /// Create a copy of OutboundMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
  }) {
    return _then(_self.copyWith(
      messages: null == messages
          ? _self.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<OutboundMessageBody>,
    ));
  }
}

/// Adds pattern-matching-related methods to [OutboundMessagesBody].
extension OutboundMessagesBodyPatterns on OutboundMessagesBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OutboundMessagesBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OutboundMessagesBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OutboundMessagesBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<OutboundMessageBody> messages)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesBody() when $default != null:
        return $default(_that.messages);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<OutboundMessageBody> messages) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesBody():
        return $default(_that.messages);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<OutboundMessageBody> messages)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesBody() when $default != null:
        return $default(_that.messages);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _OutboundMessagesBody implements OutboundMessagesBody {
  const _OutboundMessagesBody(
      {required final List<OutboundMessageBody> messages})
      : _messages = messages;
  factory _OutboundMessagesBody.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessagesBodyFromJson(json);

  final List<OutboundMessageBody> _messages;
  @override
  List<OutboundMessageBody> get messages {
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messages);
  }

  /// Create a copy of OutboundMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OutboundMessagesBodyCopyWith<_OutboundMessagesBody> get copyWith =>
      __$OutboundMessagesBodyCopyWithImpl<_OutboundMessagesBody>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OutboundMessagesBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OutboundMessagesBody &&
            const DeepCollectionEquality().equals(other._messages, _messages));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_messages));

  @override
  String toString() {
    return 'OutboundMessagesBody(messages: $messages)';
  }
}

/// @nodoc
abstract mixin class _$OutboundMessagesBodyCopyWith<$Res>
    implements $OutboundMessagesBodyCopyWith<$Res> {
  factory _$OutboundMessagesBodyCopyWith(_OutboundMessagesBody value,
          $Res Function(_OutboundMessagesBody) _then) =
      __$OutboundMessagesBodyCopyWithImpl;
  @override
  @useResult
  $Res call({List<OutboundMessageBody> messages});
}

/// @nodoc
class __$OutboundMessagesBodyCopyWithImpl<$Res>
    implements _$OutboundMessagesBodyCopyWith<$Res> {
  __$OutboundMessagesBodyCopyWithImpl(this._self, this._then);

  final _OutboundMessagesBody _self;
  final $Res Function(_OutboundMessagesBody) _then;

  /// Create a copy of OutboundMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messages = null,
  }) {
    return _then(_OutboundMessagesBody(
      messages: null == messages
          ? _self._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<OutboundMessageBody>,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_channels_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingChannelsResponse {
  List<MessagingChannelEntry> get channelSelectionEntries;
  List<MessagingChannelEntry> get otherChannels;

  /// Create a copy of MessagingChannelsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingChannelsResponseCopyWith<MessagingChannelsResponse> get copyWith =>
      _$MessagingChannelsResponseCopyWithImpl<MessagingChannelsResponse>(
          this as MessagingChannelsResponse, _$identity);

  /// Serializes this MessagingChannelsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingChannelsResponse &&
            const DeepCollectionEquality().equals(
                other.channelSelectionEntries, channelSelectionEntries) &&
            const DeepCollectionEquality()
                .equals(other.otherChannels, otherChannels));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(channelSelectionEntries),
      const DeepCollectionEquality().hash(otherChannels));

  @override
  String toString() {
    return 'MessagingChannelsResponse(channelSelectionEntries: $channelSelectionEntries, otherChannels: $otherChannels)';
  }
}

/// @nodoc
abstract mixin class $MessagingChannelsResponseCopyWith<$Res> {
  factory $MessagingChannelsResponseCopyWith(MessagingChannelsResponse value,
          $Res Function(MessagingChannelsResponse) _then) =
      _$MessagingChannelsResponseCopyWithImpl;
  @useResult
  $Res call(
      {List<MessagingChannelEntry> channelSelectionEntries,
      List<MessagingChannelEntry> otherChannels});
}

/// @nodoc
class _$MessagingChannelsResponseCopyWithImpl<$Res>
    implements $MessagingChannelsResponseCopyWith<$Res> {
  _$MessagingChannelsResponseCopyWithImpl(this._self, this._then);

  final MessagingChannelsResponse _self;
  final $Res Function(MessagingChannelsResponse) _then;

  /// Create a copy of MessagingChannelsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelSelectionEntries = null,
    Object? otherChannels = null,
  }) {
    return _then(_self.copyWith(
      channelSelectionEntries: null == channelSelectionEntries
          ? _self.channelSelectionEntries
          : channelSelectionEntries // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
      otherChannels: null == otherChannels
          ? _self.otherChannels
          : otherChannels // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
    ));
  }
}

/// Adds pattern-matching-related methods to [MessagingChannelsResponse].
extension MessagingChannelsResponsePatterns on MessagingChannelsResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingChannelsResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelsResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingChannelsResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelsResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingChannelsResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelsResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<MessagingChannelEntry> channelSelectionEntries,
            List<MessagingChannelEntry> otherChannels)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelsResponse() when $default != null:
        return $default(_that.channelSelectionEntries, _that.otherChannels);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<MessagingChannelEntry> channelSelectionEntries,
            List<MessagingChannelEntry> otherChannels)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelsResponse():
        return $default(_that.channelSelectionEntries, _that.otherChannels);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<MessagingChannelEntry> channelSelectionEntries,
            List<MessagingChannelEntry> otherChannels)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelsResponse() when $default != null:
        return $default(_that.channelSelectionEntries, _that.otherChannels);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingChannelsResponse extends MessagingChannelsResponse {
  const _MessagingChannelsResponse(
      {required final List<MessagingChannelEntry> channelSelectionEntries,
      required final List<MessagingChannelEntry> otherChannels})
      : _channelSelectionEntries = channelSelectionEntries,
        _otherChannels = otherChannels,
        super._();
  factory _MessagingChannelsResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingChannelsResponseFromJson(json);

  final List<MessagingChannelEntry> _channelSelectionEntries;
  @override
  List<MessagingChannelEntry> get channelSelectionEntries {
    if (_channelSelectionEntries is EqualUnmodifiableListView)
      return _channelSelectionEntries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channelSelectionEntries);
  }

  final List<MessagingChannelEntry> _otherChannels;
  @override
  List<MessagingChannelEntry> get otherChannels {
    if (_otherChannels is EqualUnmodifiableListView) return _otherChannels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_otherChannels);
  }

  /// Create a copy of MessagingChannelsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingChannelsResponseCopyWith<_MessagingChannelsResponse>
      get copyWith =>
          __$MessagingChannelsResponseCopyWithImpl<_MessagingChannelsResponse>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingChannelsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingChannelsResponse &&
            const DeepCollectionEquality().equals(
                other._channelSelectionEntries, _channelSelectionEntries) &&
            const DeepCollectionEquality()
                .equals(other._otherChannels, _otherChannels));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_channelSelectionEntries),
      const DeepCollectionEquality().hash(_otherChannels));

  @override
  String toString() {
    return 'MessagingChannelsResponse(channelSelectionEntries: $channelSelectionEntries, otherChannels: $otherChannels)';
  }
}

/// @nodoc
abstract mixin class _$MessagingChannelsResponseCopyWith<$Res>
    implements $MessagingChannelsResponseCopyWith<$Res> {
  factory _$MessagingChannelsResponseCopyWith(_MessagingChannelsResponse value,
          $Res Function(_MessagingChannelsResponse) _then) =
      __$MessagingChannelsResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<MessagingChannelEntry> channelSelectionEntries,
      List<MessagingChannelEntry> otherChannels});
}

/// @nodoc
class __$MessagingChannelsResponseCopyWithImpl<$Res>
    implements _$MessagingChannelsResponseCopyWith<$Res> {
  __$MessagingChannelsResponseCopyWithImpl(this._self, this._then);

  final _MessagingChannelsResponse _self;
  final $Res Function(_MessagingChannelsResponse) _then;

  /// Create a copy of MessagingChannelsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? channelSelectionEntries = null,
    Object? otherChannels = null,
  }) {
    return _then(_MessagingChannelsResponse(
      channelSelectionEntries: null == channelSelectionEntries
          ? _self._channelSelectionEntries
          : channelSelectionEntries // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
      otherChannels: null == otherChannels
          ? _self._otherChannels
          : otherChannels // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
    ));
  }
}

// dart format on

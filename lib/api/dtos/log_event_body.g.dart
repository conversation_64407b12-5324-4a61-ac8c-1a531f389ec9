// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_event_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LogEventBody _$LogEventBodyFromJson(Map json) => $checkedCreate(
      '_LogEventBody',
      json,
      ($checkedConvert) {
        final val = _LogEventBody(
          events: $checkedConvert(
              'events',
              (v) => (v as List<dynamic>)
                  .map((e) =>
                      LogEvent.fromJson(Map<String, dynamic>.from(e as Map)))
                  .toList()),
        );
        return val;
      },
    );

Map<String, dynamic> _$LogEventBodyToJson(_LogEventBody instance) =>
    <String, dynamic>{
      'events': instance.events.map((e) => e.toJson()).toList(),
    };

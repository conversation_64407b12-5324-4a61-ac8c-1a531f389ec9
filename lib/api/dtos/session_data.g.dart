// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SessionData _$SessionDataFromJson(Map json) => $checkedCreate(
      '_SessionData',
      json,
      ($checkedConvert) {
        final val = _SessionData(
          tenantId: $checkedConvert('tenantId', (v) => (v as num).toInt()),
          sessionId: $checkedConvert('sessionId', (v) => v as String),
          timeCreated:
              $checkedConvert('timeCreated', (v) => (v as num).toInt()),
          timeUpdated:
              $checkedConvert('timeUpdated', (v) => (v as num).toInt()),
          expiresAt: $checkedConvert('expiresAt', (v) => (v as num).toInt()),
          channelPlatformTypes: $checkedConvert('channelPlatformTypes',
              (v) => (v as List<dynamic>).map((e) => e as String).toList()),
          userId: $checkedConvert('userId', (v) => v as String),
        );
        return val;
      },
    );

Map<String, dynamic> _$SessionDataToJson(_SessionData instance) =>
    <String, dynamic>{
      'tenantId': instance.tenantId,
      'sessionId': instance.sessionId,
      'timeCreated': instance.timeCreated,
      'timeUpdated': instance.timeUpdated,
      'expiresAt': instance.expiresAt,
      'channelPlatformTypes': instance.channelPlatformTypes,
      'userId': instance.userId,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_messages_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OutboundMessagesResponse {
  List<MessageIdAndSessionId>? get messageAndSessionIds;
  List<SendFailure>? get failures;

  /// Create a copy of OutboundMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OutboundMessagesResponseCopyWith<OutboundMessagesResponse> get copyWith =>
      _$OutboundMessagesResponseCopyWithImpl<OutboundMessagesResponse>(
          this as OutboundMessagesResponse, _$identity);

  /// Serializes this OutboundMessagesResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OutboundMessagesResponse &&
            const DeepCollectionEquality()
                .equals(other.messageAndSessionIds, messageAndSessionIds) &&
            const DeepCollectionEquality().equals(other.failures, failures));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(messageAndSessionIds),
      const DeepCollectionEquality().hash(failures));

  @override
  String toString() {
    return 'OutboundMessagesResponse(messageAndSessionIds: $messageAndSessionIds, failures: $failures)';
  }
}

/// @nodoc
abstract mixin class $OutboundMessagesResponseCopyWith<$Res> {
  factory $OutboundMessagesResponseCopyWith(OutboundMessagesResponse value,
          $Res Function(OutboundMessagesResponse) _then) =
      _$OutboundMessagesResponseCopyWithImpl;
  @useResult
  $Res call(
      {List<MessageIdAndSessionId>? messageAndSessionIds,
      List<SendFailure>? failures});
}

/// @nodoc
class _$OutboundMessagesResponseCopyWithImpl<$Res>
    implements $OutboundMessagesResponseCopyWith<$Res> {
  _$OutboundMessagesResponseCopyWithImpl(this._self, this._then);

  final OutboundMessagesResponse _self;
  final $Res Function(OutboundMessagesResponse) _then;

  /// Create a copy of OutboundMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageAndSessionIds = freezed,
    Object? failures = freezed,
  }) {
    return _then(_self.copyWith(
      messageAndSessionIds: freezed == messageAndSessionIds
          ? _self.messageAndSessionIds
          : messageAndSessionIds // ignore: cast_nullable_to_non_nullable
              as List<MessageIdAndSessionId>?,
      failures: freezed == failures
          ? _self.failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [OutboundMessagesResponse].
extension OutboundMessagesResponsePatterns on OutboundMessagesResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OutboundMessagesResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OutboundMessagesResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OutboundMessagesResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<MessageIdAndSessionId>? messageAndSessionIds,
            List<SendFailure>? failures)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesResponse() when $default != null:
        return $default(_that.messageAndSessionIds, _that.failures);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<MessageIdAndSessionId>? messageAndSessionIds,
            List<SendFailure>? failures)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesResponse():
        return $default(_that.messageAndSessionIds, _that.failures);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<MessageIdAndSessionId>? messageAndSessionIds,
            List<SendFailure>? failures)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessagesResponse() when $default != null:
        return $default(_that.messageAndSessionIds, _that.failures);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _OutboundMessagesResponse implements OutboundMessagesResponse {
  const _OutboundMessagesResponse(
      {final List<MessageIdAndSessionId>? messageAndSessionIds,
      final List<SendFailure>? failures})
      : _messageAndSessionIds = messageAndSessionIds,
        _failures = failures;
  factory _OutboundMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessagesResponseFromJson(json);

  final List<MessageIdAndSessionId>? _messageAndSessionIds;
  @override
  List<MessageIdAndSessionId>? get messageAndSessionIds {
    final value = _messageAndSessionIds;
    if (value == null) return null;
    if (_messageAndSessionIds is EqualUnmodifiableListView)
      return _messageAndSessionIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<SendFailure>? _failures;
  @override
  List<SendFailure>? get failures {
    final value = _failures;
    if (value == null) return null;
    if (_failures is EqualUnmodifiableListView) return _failures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of OutboundMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OutboundMessagesResponseCopyWith<_OutboundMessagesResponse> get copyWith =>
      __$OutboundMessagesResponseCopyWithImpl<_OutboundMessagesResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OutboundMessagesResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OutboundMessagesResponse &&
            const DeepCollectionEquality()
                .equals(other._messageAndSessionIds, _messageAndSessionIds) &&
            const DeepCollectionEquality().equals(other._failures, _failures));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_messageAndSessionIds),
      const DeepCollectionEquality().hash(_failures));

  @override
  String toString() {
    return 'OutboundMessagesResponse(messageAndSessionIds: $messageAndSessionIds, failures: $failures)';
  }
}

/// @nodoc
abstract mixin class _$OutboundMessagesResponseCopyWith<$Res>
    implements $OutboundMessagesResponseCopyWith<$Res> {
  factory _$OutboundMessagesResponseCopyWith(_OutboundMessagesResponse value,
          $Res Function(_OutboundMessagesResponse) _then) =
      __$OutboundMessagesResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<MessageIdAndSessionId>? messageAndSessionIds,
      List<SendFailure>? failures});
}

/// @nodoc
class __$OutboundMessagesResponseCopyWithImpl<$Res>
    implements _$OutboundMessagesResponseCopyWith<$Res> {
  __$OutboundMessagesResponseCopyWithImpl(this._self, this._then);

  final _OutboundMessagesResponse _self;
  final $Res Function(_OutboundMessagesResponse) _then;

  /// Create a copy of OutboundMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageAndSessionIds = freezed,
    Object? failures = freezed,
  }) {
    return _then(_OutboundMessagesResponse(
      messageAndSessionIds: freezed == messageAndSessionIds
          ? _self._messageAndSessionIds
          : messageAndSessionIds // ignore: cast_nullable_to_non_nullable
              as List<MessageIdAndSessionId>?,
      failures: freezed == failures
          ? _self._failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkBody {
  String get requestId;
  @ParseSfIdConverter()
  SfId get workId;
  @ParseSfIdConverter()
  SfId get workTargetId;

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkBodyCopyWith<WorkBody> get copyWith =>
      _$WorkBodyCopyWithImpl<WorkBody>(this as WorkBody, _$identity);

  /// Serializes this WorkBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkBody &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, requestId, workId, workTargetId);

  @override
  String toString() {
    return 'WorkBody(requestId: $requestId, workId: $workId, workTargetId: $workTargetId)';
  }
}

/// @nodoc
abstract mixin class $WorkBodyCopyWith<$Res> {
  factory $WorkBodyCopyWith(WorkBody value, $Res Function(WorkBody) _then) =
      _$WorkBodyCopyWithImpl;
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId});

  $SfIdCopyWith<$Res> get workId;
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$WorkBodyCopyWithImpl<$Res> implements $WorkBodyCopyWith<$Res> {
  _$WorkBodyCopyWithImpl(this._self, this._then);

  final WorkBody _self;
  final $Res Function(WorkBody) _then;

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
  }) {
    return _then(_self.copyWith(
      requestId: null == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _self.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workId {
    return $SfIdCopyWith<$Res>(_self.workId, (value) {
      return _then(_self.copyWith(workId: value));
    });
  }

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [WorkBody].
extension WorkBodyPatterns on WorkBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WorkBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WorkBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WorkBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String requestId, @ParseSfIdConverter() SfId workId,
            @ParseSfIdConverter() SfId workTargetId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkBody() when $default != null:
        return $default(_that.requestId, _that.workId, _that.workTargetId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String requestId, @ParseSfIdConverter() SfId workId,
            @ParseSfIdConverter() SfId workTargetId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkBody():
        return $default(_that.requestId, _that.workId, _that.workTargetId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String requestId, @ParseSfIdConverter() SfId workId,
            @ParseSfIdConverter() SfId workTargetId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkBody() when $default != null:
        return $default(_that.requestId, _that.workId, _that.workTargetId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WorkBody implements WorkBody {
  const _WorkBody(
      {required this.requestId,
      @ParseSfIdConverter() required this.workId,
      @ParseSfIdConverter() required this.workTargetId});
  factory _WorkBody.fromJson(Map<String, dynamic> json) =>
      _$WorkBodyFromJson(json);

  @override
  final String requestId;
  @override
  @ParseSfIdConverter()
  final SfId workId;
  @override
  @ParseSfIdConverter()
  final SfId workTargetId;

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkBodyCopyWith<_WorkBody> get copyWith =>
      __$WorkBodyCopyWithImpl<_WorkBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkBody &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, requestId, workId, workTargetId);

  @override
  String toString() {
    return 'WorkBody(requestId: $requestId, workId: $workId, workTargetId: $workTargetId)';
  }
}

/// @nodoc
abstract mixin class _$WorkBodyCopyWith<$Res>
    implements $WorkBodyCopyWith<$Res> {
  factory _$WorkBodyCopyWith(_WorkBody value, $Res Function(_WorkBody) _then) =
      __$WorkBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId});

  @override
  $SfIdCopyWith<$Res> get workId;
  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$WorkBodyCopyWithImpl<$Res> implements _$WorkBodyCopyWith<$Res> {
  __$WorkBodyCopyWithImpl(this._self, this._then);

  final _WorkBody _self;
  final $Res Function(_WorkBody) _then;

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
  }) {
    return _then(_WorkBody(
      requestId: null == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _self.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workId {
    return $SfIdCopyWith<$Res>(_self.workId, (value) {
      return _then(_self.copyWith(workId: value));
    });
  }

  /// Create a copy of WorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

// dart format on

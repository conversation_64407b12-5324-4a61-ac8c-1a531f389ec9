// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_work_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CloseWorkBody _$CloseWorkBodyFromJson(Map json) => $checkedCreate(
      '_CloseWorkBody',
      json,
      ($checkedConvert) {
        final val = _CloseWorkBody(
          requestId: $checkedConvert('requestId', (v) => v as String),
          endConversation:
              $checkedConvert('endConversation', (v) => v as bool?),
          workTargetId: $checkedConvert(
              'workTargetId', (v) => const ParseSfIdConverter().fromJson(v)),
        );
        return val;
      },
    );

Map<String, dynamic> _$CloseWorkBodyToJson(_CloseWorkBody instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      if (instance.endConversation case final value?) 'endConversation': value,
      if (const ParseSfIdConverter().toJson(instance.workTargetId)
          case final value?)
        'workTargetId': value,
    };

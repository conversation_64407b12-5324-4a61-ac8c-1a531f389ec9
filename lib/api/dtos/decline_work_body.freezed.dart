// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'decline_work_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeclineWorkBody {
  String get requestId;
  @ParseSfIdConverter()
  SfId get workId;
  @ParseSfIdConverter()
  SfId get workTargetId;
  String? get declineReason;

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeclineWorkBodyCopyWith<DeclineWorkBody> get copyWith =>
      _$DeclineWorkBodyCopyWithImpl<DeclineWorkBody>(
          this as DeclineWorkBody, _$identity);

  /// Serializes this DeclineWorkBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeclineWorkBody &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.declineReason, declineReason) ||
                other.declineReason == declineReason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, requestId, workId, workTargetId, declineReason);

  @override
  String toString() {
    return 'DeclineWorkBody(requestId: $requestId, workId: $workId, workTargetId: $workTargetId, declineReason: $declineReason)';
  }
}

/// @nodoc
abstract mixin class $DeclineWorkBodyCopyWith<$Res> {
  factory $DeclineWorkBodyCopyWith(
          DeclineWorkBody value, $Res Function(DeclineWorkBody) _then) =
      _$DeclineWorkBodyCopyWithImpl;
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId,
      String? declineReason});

  $SfIdCopyWith<$Res> get workId;
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$DeclineWorkBodyCopyWithImpl<$Res>
    implements $DeclineWorkBodyCopyWith<$Res> {
  _$DeclineWorkBodyCopyWithImpl(this._self, this._then);

  final DeclineWorkBody _self;
  final $Res Function(DeclineWorkBody) _then;

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
    Object? declineReason = freezed,
  }) {
    return _then(_self.copyWith(
      requestId: null == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _self.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
      declineReason: freezed == declineReason
          ? _self.declineReason
          : declineReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workId {
    return $SfIdCopyWith<$Res>(_self.workId, (value) {
      return _then(_self.copyWith(workId: value));
    });
  }

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [DeclineWorkBody].
extension DeclineWorkBodyPatterns on DeclineWorkBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DeclineWorkBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeclineWorkBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DeclineWorkBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeclineWorkBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DeclineWorkBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeclineWorkBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String requestId, @ParseSfIdConverter() SfId workId,
            @ParseSfIdConverter() SfId workTargetId, String? declineReason)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeclineWorkBody() when $default != null:
        return $default(_that.requestId, _that.workId, _that.workTargetId,
            _that.declineReason);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String requestId, @ParseSfIdConverter() SfId workId,
            @ParseSfIdConverter() SfId workTargetId, String? declineReason)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeclineWorkBody():
        return $default(_that.requestId, _that.workId, _that.workTargetId,
            _that.declineReason);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String requestId, @ParseSfIdConverter() SfId workId,
            @ParseSfIdConverter() SfId workTargetId, String? declineReason)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeclineWorkBody() when $default != null:
        return $default(_that.requestId, _that.workId, _that.workTargetId,
            _that.declineReason);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DeclineWorkBody implements DeclineWorkBody {
  const _DeclineWorkBody(
      {required this.requestId,
      @ParseSfIdConverter() required this.workId,
      @ParseSfIdConverter() required this.workTargetId,
      this.declineReason});
  factory _DeclineWorkBody.fromJson(Map<String, dynamic> json) =>
      _$DeclineWorkBodyFromJson(json);

  @override
  final String requestId;
  @override
  @ParseSfIdConverter()
  final SfId workId;
  @override
  @ParseSfIdConverter()
  final SfId workTargetId;
  @override
  final String? declineReason;

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeclineWorkBodyCopyWith<_DeclineWorkBody> get copyWith =>
      __$DeclineWorkBodyCopyWithImpl<_DeclineWorkBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DeclineWorkBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeclineWorkBody &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.declineReason, declineReason) ||
                other.declineReason == declineReason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, requestId, workId, workTargetId, declineReason);

  @override
  String toString() {
    return 'DeclineWorkBody(requestId: $requestId, workId: $workId, workTargetId: $workTargetId, declineReason: $declineReason)';
  }
}

/// @nodoc
abstract mixin class _$DeclineWorkBodyCopyWith<$Res>
    implements $DeclineWorkBodyCopyWith<$Res> {
  factory _$DeclineWorkBodyCopyWith(
          _DeclineWorkBody value, $Res Function(_DeclineWorkBody) _then) =
      __$DeclineWorkBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId,
      String? declineReason});

  @override
  $SfIdCopyWith<$Res> get workId;
  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$DeclineWorkBodyCopyWithImpl<$Res>
    implements _$DeclineWorkBodyCopyWith<$Res> {
  __$DeclineWorkBodyCopyWithImpl(this._self, this._then);

  final _DeclineWorkBody _self;
  final $Res Function(_DeclineWorkBody) _then;

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
    Object? declineReason = freezed,
  }) {
    return _then(_DeclineWorkBody(
      requestId: null == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _self.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
      declineReason: freezed == declineReason
          ? _self.declineReason
          : declineReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workId {
    return $SfIdCopyWith<$Res>(_self.workId, (value) {
      return _then(_self.copyWith(workId: value));
    });
  }

  /// Create a copy of DeclineWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

// dart format on

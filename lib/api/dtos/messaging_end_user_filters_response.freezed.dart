// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_filters_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingEndUserFiltersResponse {
  List<MessagingEndUserFilter> get filters;

  /// Create a copy of MessagingEndUserFiltersResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingEndUserFiltersResponseCopyWith<MessagingEndUserFiltersResponse>
      get copyWith => _$MessagingEndUserFiltersResponseCopyWithImpl<
              MessagingEndUserFiltersResponse>(
          this as MessagingEndUserFiltersResponse, _$identity);

  /// Serializes this MessagingEndUserFiltersResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingEndUserFiltersResponse &&
            const DeepCollectionEquality().equals(other.filters, filters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(filters));

  @override
  String toString() {
    return 'MessagingEndUserFiltersResponse(filters: $filters)';
  }
}

/// @nodoc
abstract mixin class $MessagingEndUserFiltersResponseCopyWith<$Res> {
  factory $MessagingEndUserFiltersResponseCopyWith(
          MessagingEndUserFiltersResponse value,
          $Res Function(MessagingEndUserFiltersResponse) _then) =
      _$MessagingEndUserFiltersResponseCopyWithImpl;
  @useResult
  $Res call({List<MessagingEndUserFilter> filters});
}

/// @nodoc
class _$MessagingEndUserFiltersResponseCopyWithImpl<$Res>
    implements $MessagingEndUserFiltersResponseCopyWith<$Res> {
  _$MessagingEndUserFiltersResponseCopyWithImpl(this._self, this._then);

  final MessagingEndUserFiltersResponse _self;
  final $Res Function(MessagingEndUserFiltersResponse) _then;

  /// Create a copy of MessagingEndUserFiltersResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = null,
  }) {
    return _then(_self.copyWith(
      filters: null == filters
          ? _self.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as List<MessagingEndUserFilter>,
    ));
  }
}

/// Adds pattern-matching-related methods to [MessagingEndUserFiltersResponse].
extension MessagingEndUserFiltersResponsePatterns
    on MessagingEndUserFiltersResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingEndUserFiltersResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFiltersResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingEndUserFiltersResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFiltersResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingEndUserFiltersResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFiltersResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<MessagingEndUserFilter> filters)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFiltersResponse() when $default != null:
        return $default(_that.filters);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<MessagingEndUserFilter> filters) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFiltersResponse():
        return $default(_that.filters);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<MessagingEndUserFilter> filters)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFiltersResponse() when $default != null:
        return $default(_that.filters);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingEndUserFiltersResponse
    implements MessagingEndUserFiltersResponse {
  const _MessagingEndUserFiltersResponse(
      {required final List<MessagingEndUserFilter> filters})
      : _filters = filters;
  factory _MessagingEndUserFiltersResponse.fromJson(
          Map<String, dynamic> json) =>
      _$MessagingEndUserFiltersResponseFromJson(json);

  final List<MessagingEndUserFilter> _filters;
  @override
  List<MessagingEndUserFilter> get filters {
    if (_filters is EqualUnmodifiableListView) return _filters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filters);
  }

  /// Create a copy of MessagingEndUserFiltersResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingEndUserFiltersResponseCopyWith<_MessagingEndUserFiltersResponse>
      get copyWith => __$MessagingEndUserFiltersResponseCopyWithImpl<
          _MessagingEndUserFiltersResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingEndUserFiltersResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingEndUserFiltersResponse &&
            const DeepCollectionEquality().equals(other._filters, _filters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_filters));

  @override
  String toString() {
    return 'MessagingEndUserFiltersResponse(filters: $filters)';
  }
}

/// @nodoc
abstract mixin class _$MessagingEndUserFiltersResponseCopyWith<$Res>
    implements $MessagingEndUserFiltersResponseCopyWith<$Res> {
  factory _$MessagingEndUserFiltersResponseCopyWith(
          _MessagingEndUserFiltersResponse value,
          $Res Function(_MessagingEndUserFiltersResponse) _then) =
      __$MessagingEndUserFiltersResponseCopyWithImpl;
  @override
  @useResult
  $Res call({List<MessagingEndUserFilter> filters});
}

/// @nodoc
class __$MessagingEndUserFiltersResponseCopyWithImpl<$Res>
    implements _$MessagingEndUserFiltersResponseCopyWith<$Res> {
  __$MessagingEndUserFiltersResponseCopyWithImpl(this._self, this._then);

  final _MessagingEndUserFiltersResponse _self;
  final $Res Function(_MessagingEndUserFiltersResponse) _then;

  /// Create a copy of MessagingEndUserFiltersResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? filters = null,
  }) {
    return _then(_MessagingEndUserFiltersResponse(
      filters: null == filters
          ? _self._filters
          : filters // ignore: cast_nullable_to_non_nullable
              as List<MessagingEndUserFilter>,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'keep_session_alive_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$KeepSessionAliveResponse {
  int get expirationTime;

  /// Create a copy of KeepSessionAliveResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $KeepSessionAliveResponseCopyWith<KeepSessionAliveResponse> get copyWith =>
      _$KeepSessionAliveResponseCopyWithImpl<KeepSessionAliveResponse>(
          this as KeepSessionAliveResponse, _$identity);

  /// Serializes this KeepSessionAliveResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is KeepSessionAliveResponse &&
            (identical(other.expirationTime, expirationTime) ||
                other.expirationTime == expirationTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, expirationTime);

  @override
  String toString() {
    return 'KeepSessionAliveResponse(expirationTime: $expirationTime)';
  }
}

/// @nodoc
abstract mixin class $KeepSessionAliveResponseCopyWith<$Res> {
  factory $KeepSessionAliveResponseCopyWith(KeepSessionAliveResponse value,
          $Res Function(KeepSessionAliveResponse) _then) =
      _$KeepSessionAliveResponseCopyWithImpl;
  @useResult
  $Res call({int expirationTime});
}

/// @nodoc
class _$KeepSessionAliveResponseCopyWithImpl<$Res>
    implements $KeepSessionAliveResponseCopyWith<$Res> {
  _$KeepSessionAliveResponseCopyWithImpl(this._self, this._then);

  final KeepSessionAliveResponse _self;
  final $Res Function(KeepSessionAliveResponse) _then;

  /// Create a copy of KeepSessionAliveResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expirationTime = null,
  }) {
    return _then(_self.copyWith(
      expirationTime: null == expirationTime
          ? _self.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// Adds pattern-matching-related methods to [KeepSessionAliveResponse].
extension KeepSessionAliveResponsePatterns on KeepSessionAliveResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_KeepSessionAliveResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KeepSessionAliveResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_KeepSessionAliveResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KeepSessionAliveResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_KeepSessionAliveResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KeepSessionAliveResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(int expirationTime)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KeepSessionAliveResponse() when $default != null:
        return $default(_that.expirationTime);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(int expirationTime) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KeepSessionAliveResponse():
        return $default(_that.expirationTime);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(int expirationTime)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KeepSessionAliveResponse() when $default != null:
        return $default(_that.expirationTime);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _KeepSessionAliveResponse implements KeepSessionAliveResponse {
  const _KeepSessionAliveResponse({required this.expirationTime});
  factory _KeepSessionAliveResponse.fromJson(Map<String, dynamic> json) =>
      _$KeepSessionAliveResponseFromJson(json);

  @override
  final int expirationTime;

  /// Create a copy of KeepSessionAliveResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$KeepSessionAliveResponseCopyWith<_KeepSessionAliveResponse> get copyWith =>
      __$KeepSessionAliveResponseCopyWithImpl<_KeepSessionAliveResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$KeepSessionAliveResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _KeepSessionAliveResponse &&
            (identical(other.expirationTime, expirationTime) ||
                other.expirationTime == expirationTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, expirationTime);

  @override
  String toString() {
    return 'KeepSessionAliveResponse(expirationTime: $expirationTime)';
  }
}

/// @nodoc
abstract mixin class _$KeepSessionAliveResponseCopyWith<$Res>
    implements $KeepSessionAliveResponseCopyWith<$Res> {
  factory _$KeepSessionAliveResponseCopyWith(_KeepSessionAliveResponse value,
          $Res Function(_KeepSessionAliveResponse) _then) =
      __$KeepSessionAliveResponseCopyWithImpl;
  @override
  @useResult
  $Res call({int expirationTime});
}

/// @nodoc
class __$KeepSessionAliveResponseCopyWithImpl<$Res>
    implements _$KeepSessionAliveResponseCopyWith<$Res> {
  __$KeepSessionAliveResponseCopyWithImpl(this._self, this._then);

  final _KeepSessionAliveResponse _self;
  final $Res Function(_KeepSessionAliveResponse) _then;

  /// Create a copy of KeepSessionAliveResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? expirationTime = null,
  }) {
    return _then(_KeepSessionAliveResponse(
      expirationTime: null == expirationTime
          ? _self.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'close_work_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CloseWorkBody {
  String get requestId;
  bool? get endConversation;
  @ParseSfIdConverter()
  SfId get workTargetId;

  /// Create a copy of CloseWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CloseWorkBodyCopyWith<CloseWorkBody> get copyWith =>
      _$CloseWorkBodyCopyWithImpl<CloseWorkBody>(
          this as CloseWorkBody, _$identity);

  /// Serializes this CloseWorkBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CloseWorkBody &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.endConversation, endConversation) ||
                other.endConversation == endConversation) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, requestId, endConversation, workTargetId);

  @override
  String toString() {
    return 'CloseWorkBody(requestId: $requestId, endConversation: $endConversation, workTargetId: $workTargetId)';
  }
}

/// @nodoc
abstract mixin class $CloseWorkBodyCopyWith<$Res> {
  factory $CloseWorkBodyCopyWith(
          CloseWorkBody value, $Res Function(CloseWorkBody) _then) =
      _$CloseWorkBodyCopyWithImpl;
  @useResult
  $Res call(
      {String requestId,
      bool? endConversation,
      @ParseSfIdConverter() SfId workTargetId});

  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$CloseWorkBodyCopyWithImpl<$Res>
    implements $CloseWorkBodyCopyWith<$Res> {
  _$CloseWorkBodyCopyWithImpl(this._self, this._then);

  final CloseWorkBody _self;
  final $Res Function(CloseWorkBody) _then;

  /// Create a copy of CloseWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? endConversation = freezed,
    Object? workTargetId = null,
  }) {
    return _then(_self.copyWith(
      requestId: null == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      endConversation: freezed == endConversation
          ? _self.endConversation
          : endConversation // ignore: cast_nullable_to_non_nullable
              as bool?,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of CloseWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [CloseWorkBody].
extension CloseWorkBodyPatterns on CloseWorkBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CloseWorkBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CloseWorkBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CloseWorkBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CloseWorkBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CloseWorkBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CloseWorkBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String requestId, bool? endConversation,
            @ParseSfIdConverter() SfId workTargetId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CloseWorkBody() when $default != null:
        return $default(
            _that.requestId, _that.endConversation, _that.workTargetId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String requestId, bool? endConversation,
            @ParseSfIdConverter() SfId workTargetId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CloseWorkBody():
        return $default(
            _that.requestId, _that.endConversation, _that.workTargetId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String requestId, bool? endConversation,
            @ParseSfIdConverter() SfId workTargetId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CloseWorkBody() when $default != null:
        return $default(
            _that.requestId, _that.endConversation, _that.workTargetId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CloseWorkBody implements CloseWorkBody {
  const _CloseWorkBody(
      {required this.requestId,
      this.endConversation,
      @ParseSfIdConverter() required this.workTargetId});
  factory _CloseWorkBody.fromJson(Map<String, dynamic> json) =>
      _$CloseWorkBodyFromJson(json);

  @override
  final String requestId;
  @override
  final bool? endConversation;
  @override
  @ParseSfIdConverter()
  final SfId workTargetId;

  /// Create a copy of CloseWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CloseWorkBodyCopyWith<_CloseWorkBody> get copyWith =>
      __$CloseWorkBodyCopyWithImpl<_CloseWorkBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CloseWorkBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CloseWorkBody &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.endConversation, endConversation) ||
                other.endConversation == endConversation) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, requestId, endConversation, workTargetId);

  @override
  String toString() {
    return 'CloseWorkBody(requestId: $requestId, endConversation: $endConversation, workTargetId: $workTargetId)';
  }
}

/// @nodoc
abstract mixin class _$CloseWorkBodyCopyWith<$Res>
    implements $CloseWorkBodyCopyWith<$Res> {
  factory _$CloseWorkBodyCopyWith(
          _CloseWorkBody value, $Res Function(_CloseWorkBody) _then) =
      __$CloseWorkBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String requestId,
      bool? endConversation,
      @ParseSfIdConverter() SfId workTargetId});

  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$CloseWorkBodyCopyWithImpl<$Res>
    implements _$CloseWorkBodyCopyWith<$Res> {
  __$CloseWorkBodyCopyWithImpl(this._self, this._then);

  final _CloseWorkBody _self;
  final $Res Function(_CloseWorkBody) _then;

  /// Create a copy of CloseWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? requestId = null,
    Object? endConversation = freezed,
    Object? workTargetId = null,
  }) {
    return _then(_CloseWorkBody(
      requestId: null == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      endConversation: freezed == endConversation
          ? _self.endConversation
          : endConversation // ignore: cast_nullable_to_non_nullable
              as bool?,
      workTargetId: null == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of CloseWorkBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_self.workTargetId, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

// dart format on

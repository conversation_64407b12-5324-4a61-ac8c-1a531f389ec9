// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_message_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OutboundMessageBody {
  String get messageId;
  @ParseSfIdConverter()
  SfId get messagingEndUserId;
  String?
      get messageBody; // Message Body MUST NOT be set if we pass in a messagingDefinitionParameters
  MessagingDefinitionParameters? get messagingDefinitionParameters;

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OutboundMessageBodyCopyWith<OutboundMessageBody> get copyWith =>
      _$OutboundMessageBodyCopyWithImpl<OutboundMessageBody>(
          this as OutboundMessageBody, _$identity);

  /// Serializes this OutboundMessageBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OutboundMessageBody &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messageBody, messageBody) ||
                other.messageBody == messageBody) &&
            (identical(other.messagingDefinitionParameters,
                    messagingDefinitionParameters) ||
                other.messagingDefinitionParameters ==
                    messagingDefinitionParameters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messagingEndUserId,
      messageBody, messagingDefinitionParameters);

  @override
  String toString() {
    return 'OutboundMessageBody(messageId: $messageId, messagingEndUserId: $messagingEndUserId, messageBody: $messageBody, messagingDefinitionParameters: $messagingDefinitionParameters)';
  }
}

/// @nodoc
abstract mixin class $OutboundMessageBodyCopyWith<$Res> {
  factory $OutboundMessageBodyCopyWith(
          OutboundMessageBody value, $Res Function(OutboundMessageBody) _then) =
      _$OutboundMessageBodyCopyWithImpl;
  @useResult
  $Res call(
      {String messageId,
      @ParseSfIdConverter() SfId messagingEndUserId,
      String? messageBody,
      MessagingDefinitionParameters? messagingDefinitionParameters});

  $SfIdCopyWith<$Res> get messagingEndUserId;
  $MessagingDefinitionParametersCopyWith<$Res>?
      get messagingDefinitionParameters;
}

/// @nodoc
class _$OutboundMessageBodyCopyWithImpl<$Res>
    implements $OutboundMessageBodyCopyWith<$Res> {
  _$OutboundMessageBodyCopyWithImpl(this._self, this._then);

  final OutboundMessageBody _self;
  final $Res Function(OutboundMessageBody) _then;

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messageBody = freezed,
    Object? messagingDefinitionParameters = freezed,
  }) {
    return _then(_self.copyWith(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageBody: freezed == messageBody
          ? _self.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionParameters: freezed == messagingDefinitionParameters
          ? _self.messagingDefinitionParameters
          : messagingDefinitionParameters // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionParameters?,
    ));
  }

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingEndUserId {
    return $SfIdCopyWith<$Res>(_self.messagingEndUserId, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionParametersCopyWith<$Res>?
      get messagingDefinitionParameters {
    if (_self.messagingDefinitionParameters == null) {
      return null;
    }

    return $MessagingDefinitionParametersCopyWith<$Res>(
        _self.messagingDefinitionParameters!, (value) {
      return _then(_self.copyWith(messagingDefinitionParameters: value));
    });
  }
}

/// Adds pattern-matching-related methods to [OutboundMessageBody].
extension OutboundMessageBodyPatterns on OutboundMessageBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OutboundMessageBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OutboundMessageBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OutboundMessageBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String messageId,
            @ParseSfIdConverter() SfId messagingEndUserId,
            String? messageBody,
            MessagingDefinitionParameters? messagingDefinitionParameters)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageBody() when $default != null:
        return $default(_that.messageId, _that.messagingEndUserId,
            _that.messageBody, _that.messagingDefinitionParameters);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String messageId,
            @ParseSfIdConverter() SfId messagingEndUserId,
            String? messageBody,
            MessagingDefinitionParameters? messagingDefinitionParameters)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageBody():
        return $default(_that.messageId, _that.messagingEndUserId,
            _that.messageBody, _that.messagingDefinitionParameters);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String messageId,
            @ParseSfIdConverter() SfId messagingEndUserId,
            String? messageBody,
            MessagingDefinitionParameters? messagingDefinitionParameters)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageBody() when $default != null:
        return $default(_that.messageId, _that.messagingEndUserId,
            _that.messageBody, _that.messagingDefinitionParameters);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _OutboundMessageBody implements OutboundMessageBody {
  const _OutboundMessageBody(
      {required this.messageId,
      @ParseSfIdConverter() required this.messagingEndUserId,
      this.messageBody,
      this.messagingDefinitionParameters});
  factory _OutboundMessageBody.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessageBodyFromJson(json);

  @override
  final String messageId;
  @override
  @ParseSfIdConverter()
  final SfId messagingEndUserId;
  @override
  final String? messageBody;
// Message Body MUST NOT be set if we pass in a messagingDefinitionParameters
  @override
  final MessagingDefinitionParameters? messagingDefinitionParameters;

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OutboundMessageBodyCopyWith<_OutboundMessageBody> get copyWith =>
      __$OutboundMessageBodyCopyWithImpl<_OutboundMessageBody>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OutboundMessageBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OutboundMessageBody &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messageBody, messageBody) ||
                other.messageBody == messageBody) &&
            (identical(other.messagingDefinitionParameters,
                    messagingDefinitionParameters) ||
                other.messagingDefinitionParameters ==
                    messagingDefinitionParameters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messagingEndUserId,
      messageBody, messagingDefinitionParameters);

  @override
  String toString() {
    return 'OutboundMessageBody(messageId: $messageId, messagingEndUserId: $messagingEndUserId, messageBody: $messageBody, messagingDefinitionParameters: $messagingDefinitionParameters)';
  }
}

/// @nodoc
abstract mixin class _$OutboundMessageBodyCopyWith<$Res>
    implements $OutboundMessageBodyCopyWith<$Res> {
  factory _$OutboundMessageBodyCopyWith(_OutboundMessageBody value,
          $Res Function(_OutboundMessageBody) _then) =
      __$OutboundMessageBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String messageId,
      @ParseSfIdConverter() SfId messagingEndUserId,
      String? messageBody,
      MessagingDefinitionParameters? messagingDefinitionParameters});

  @override
  $SfIdCopyWith<$Res> get messagingEndUserId;
  @override
  $MessagingDefinitionParametersCopyWith<$Res>?
      get messagingDefinitionParameters;
}

/// @nodoc
class __$OutboundMessageBodyCopyWithImpl<$Res>
    implements _$OutboundMessageBodyCopyWith<$Res> {
  __$OutboundMessageBodyCopyWithImpl(this._self, this._then);

  final _OutboundMessageBody _self;
  final $Res Function(_OutboundMessageBody) _then;

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messageBody = freezed,
    Object? messagingDefinitionParameters = freezed,
  }) {
    return _then(_OutboundMessageBody(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageBody: freezed == messageBody
          ? _self.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionParameters: freezed == messagingDefinitionParameters
          ? _self.messagingDefinitionParameters
          : messagingDefinitionParameters // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionParameters?,
    ));
  }

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingEndUserId {
    return $SfIdCopyWith<$Res>(_self.messagingEndUserId, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }

  /// Create a copy of OutboundMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionParametersCopyWith<$Res>?
      get messagingDefinitionParameters {
    if (_self.messagingDefinitionParameters == null) {
      return null;
    }

    return $MessagingDefinitionParametersCopyWith<$Res>(
        _self.messagingDefinitionParameters!, (value) {
      return _then(_self.copyWith(messagingDefinitionParameters: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sessions_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$<PERSON>ody {
  String get userId;
  String get instanceUrl;
  String? get deviceToken;
  String get accessToken;
  List<String> get channelPlatformTypes;
  @JsonKey(name: 'localeCode')
  String get locale;
  String? get deviceType;
  int? get expirationSeconds;
  Map<String, dynamic>? get metadata;

  /// Create a copy of SessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SessionsBodyCopyWith<SessionsBody> get copyWith =>
      _$SessionsBodyCopyWithImpl<SessionsBody>(
          this as SessionsBody, _$identity);

  /// Serializes this SessionsBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SessionsBody &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.deviceToken, deviceToken) ||
                other.deviceToken == deviceToken) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            const DeepCollectionEquality()
                .equals(other.channelPlatformTypes, channelPlatformTypes) &&
            (identical(other.locale, locale) || other.locale == locale) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.expirationSeconds, expirationSeconds) ||
                other.expirationSeconds == expirationSeconds) &&
            const DeepCollectionEquality().equals(other.metadata, metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      instanceUrl,
      deviceToken,
      accessToken,
      const DeepCollectionEquality().hash(channelPlatformTypes),
      locale,
      deviceType,
      expirationSeconds,
      const DeepCollectionEquality().hash(metadata));

  @override
  String toString() {
    return 'SessionsBody(userId: $userId, instanceUrl: $instanceUrl, deviceToken: $deviceToken, accessToken: $accessToken, channelPlatformTypes: $channelPlatformTypes, locale: $locale, deviceType: $deviceType, expirationSeconds: $expirationSeconds, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class $SessionsBodyCopyWith<$Res> {
  factory $SessionsBodyCopyWith(
          SessionsBody value, $Res Function(SessionsBody) _then) =
      _$SessionsBodyCopyWithImpl;
  @useResult
  $Res call(
      {String userId,
      String instanceUrl,
      String? deviceToken,
      String accessToken,
      List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') String locale,
      String? deviceType,
      int? expirationSeconds,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$SessionsBodyCopyWithImpl<$Res> implements $SessionsBodyCopyWith<$Res> {
  _$SessionsBodyCopyWithImpl(this._self, this._then);

  final SessionsBody _self;
  final $Res Function(SessionsBody) _then;

  /// Create a copy of SessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? instanceUrl = null,
    Object? deviceToken = freezed,
    Object? accessToken = null,
    Object? channelPlatformTypes = null,
    Object? locale = null,
    Object? deviceType = freezed,
    Object? expirationSeconds = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_self.copyWith(
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      deviceToken: freezed == deviceToken
          ? _self.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: null == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      channelPlatformTypes: null == channelPlatformTypes
          ? _self.channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      locale: null == locale
          ? _self.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: freezed == deviceType
          ? _self.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationSeconds: freezed == expirationSeconds
          ? _self.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      metadata: freezed == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SessionsBody].
extension SessionsBodyPatterns on SessionsBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SessionsBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SessionsBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SessionsBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SessionsBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String userId,
            String instanceUrl,
            String? deviceToken,
            String accessToken,
            List<String> channelPlatformTypes,
            @JsonKey(name: 'localeCode') String locale,
            String? deviceType,
            int? expirationSeconds,
            Map<String, dynamic>? metadata)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SessionsBody() when $default != null:
        return $default(
            _that.userId,
            _that.instanceUrl,
            _that.deviceToken,
            _that.accessToken,
            _that.channelPlatformTypes,
            _that.locale,
            _that.deviceType,
            _that.expirationSeconds,
            _that.metadata);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String userId,
            String instanceUrl,
            String? deviceToken,
            String accessToken,
            List<String> channelPlatformTypes,
            @JsonKey(name: 'localeCode') String locale,
            String? deviceType,
            int? expirationSeconds,
            Map<String, dynamic>? metadata)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsBody():
        return $default(
            _that.userId,
            _that.instanceUrl,
            _that.deviceToken,
            _that.accessToken,
            _that.channelPlatformTypes,
            _that.locale,
            _that.deviceType,
            _that.expirationSeconds,
            _that.metadata);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String userId,
            String instanceUrl,
            String? deviceToken,
            String accessToken,
            List<String> channelPlatformTypes,
            @JsonKey(name: 'localeCode') String locale,
            String? deviceType,
            int? expirationSeconds,
            Map<String, dynamic>? metadata)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsBody() when $default != null:
        return $default(
            _that.userId,
            _that.instanceUrl,
            _that.deviceToken,
            _that.accessToken,
            _that.channelPlatformTypes,
            _that.locale,
            _that.deviceType,
            _that.expirationSeconds,
            _that.metadata);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SessionsBody implements SessionsBody {
  const _SessionsBody(
      {required this.userId,
      required this.instanceUrl,
      required this.deviceToken,
      required this.accessToken,
      required final List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') required this.locale,
      this.deviceType,
      this.expirationSeconds,
      final Map<String, dynamic>? metadata})
      : _channelPlatformTypes = channelPlatformTypes,
        _metadata = metadata;
  factory _SessionsBody.fromJson(Map<String, dynamic> json) =>
      _$SessionsBodyFromJson(json);

  @override
  final String userId;
  @override
  final String instanceUrl;
  @override
  final String? deviceToken;
  @override
  final String accessToken;
  final List<String> _channelPlatformTypes;
  @override
  List<String> get channelPlatformTypes {
    if (_channelPlatformTypes is EqualUnmodifiableListView)
      return _channelPlatformTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channelPlatformTypes);
  }

  @override
  @JsonKey(name: 'localeCode')
  final String locale;
  @override
  final String? deviceType;
  @override
  final int? expirationSeconds;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of SessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SessionsBodyCopyWith<_SessionsBody> get copyWith =>
      __$SessionsBodyCopyWithImpl<_SessionsBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SessionsBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SessionsBody &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.deviceToken, deviceToken) ||
                other.deviceToken == deviceToken) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            const DeepCollectionEquality()
                .equals(other._channelPlatformTypes, _channelPlatformTypes) &&
            (identical(other.locale, locale) || other.locale == locale) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.expirationSeconds, expirationSeconds) ||
                other.expirationSeconds == expirationSeconds) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      instanceUrl,
      deviceToken,
      accessToken,
      const DeepCollectionEquality().hash(_channelPlatformTypes),
      locale,
      deviceType,
      expirationSeconds,
      const DeepCollectionEquality().hash(_metadata));

  @override
  String toString() {
    return 'SessionsBody(userId: $userId, instanceUrl: $instanceUrl, deviceToken: $deviceToken, accessToken: $accessToken, channelPlatformTypes: $channelPlatformTypes, locale: $locale, deviceType: $deviceType, expirationSeconds: $expirationSeconds, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class _$SessionsBodyCopyWith<$Res>
    implements $SessionsBodyCopyWith<$Res> {
  factory _$SessionsBodyCopyWith(
          _SessionsBody value, $Res Function(_SessionsBody) _then) =
      __$SessionsBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String userId,
      String instanceUrl,
      String? deviceToken,
      String accessToken,
      List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') String locale,
      String? deviceType,
      int? expirationSeconds,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$SessionsBodyCopyWithImpl<$Res>
    implements _$SessionsBodyCopyWith<$Res> {
  __$SessionsBodyCopyWithImpl(this._self, this._then);

  final _SessionsBody _self;
  final $Res Function(_SessionsBody) _then;

  /// Create a copy of SessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = null,
    Object? instanceUrl = null,
    Object? deviceToken = freezed,
    Object? accessToken = null,
    Object? channelPlatformTypes = null,
    Object? locale = null,
    Object? deviceType = freezed,
    Object? expirationSeconds = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_SessionsBody(
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      deviceToken: freezed == deviceToken
          ? _self.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: null == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      channelPlatformTypes: null == channelPlatformTypes
          ? _self._channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      locale: null == locale
          ? _self.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: freezed == deviceType
          ? _self.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationSeconds: freezed == expirationSeconds
          ? _self.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      metadata: freezed == metadata
          ? _self._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_message_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OutboundMessageResponse {
  String get messagingSessionId;

  /// Create a copy of OutboundMessageResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OutboundMessageResponseCopyWith<OutboundMessageResponse> get copyWith =>
      _$OutboundMessageResponseCopyWithImpl<OutboundMessageResponse>(
          this as OutboundMessageResponse, _$identity);

  /// Serializes this OutboundMessageResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OutboundMessageResponse &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messagingSessionId);

  @override
  String toString() {
    return 'OutboundMessageResponse(messagingSessionId: $messagingSessionId)';
  }
}

/// @nodoc
abstract mixin class $OutboundMessageResponseCopyWith<$Res> {
  factory $OutboundMessageResponseCopyWith(OutboundMessageResponse value,
          $Res Function(OutboundMessageResponse) _then) =
      _$OutboundMessageResponseCopyWithImpl;
  @useResult
  $Res call({String messagingSessionId});
}

/// @nodoc
class _$OutboundMessageResponseCopyWithImpl<$Res>
    implements $OutboundMessageResponseCopyWith<$Res> {
  _$OutboundMessageResponseCopyWithImpl(this._self, this._then);

  final OutboundMessageResponse _self;
  final $Res Function(OutboundMessageResponse) _then;

  /// Create a copy of OutboundMessageResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingSessionId = null,
  }) {
    return _then(_self.copyWith(
      messagingSessionId: null == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [OutboundMessageResponse].
extension OutboundMessageResponsePatterns on OutboundMessageResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OutboundMessageResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OutboundMessageResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OutboundMessageResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String messagingSessionId)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageResponse() when $default != null:
        return $default(_that.messagingSessionId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String messagingSessionId) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageResponse():
        return $default(_that.messagingSessionId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String messagingSessionId)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundMessageResponse() when $default != null:
        return $default(_that.messagingSessionId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _OutboundMessageResponse implements OutboundMessageResponse {
  const _OutboundMessageResponse({required this.messagingSessionId});
  factory _OutboundMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessageResponseFromJson(json);

  @override
  final String messagingSessionId;

  /// Create a copy of OutboundMessageResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OutboundMessageResponseCopyWith<_OutboundMessageResponse> get copyWith =>
      __$OutboundMessageResponseCopyWithImpl<_OutboundMessageResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OutboundMessageResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OutboundMessageResponse &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messagingSessionId);

  @override
  String toString() {
    return 'OutboundMessageResponse(messagingSessionId: $messagingSessionId)';
  }
}

/// @nodoc
abstract mixin class _$OutboundMessageResponseCopyWith<$Res>
    implements $OutboundMessageResponseCopyWith<$Res> {
  factory _$OutboundMessageResponseCopyWith(_OutboundMessageResponse value,
          $Res Function(_OutboundMessageResponse) _then) =
      __$OutboundMessageResponseCopyWithImpl;
  @override
  @useResult
  $Res call({String messagingSessionId});
}

/// @nodoc
class __$OutboundMessageResponseCopyWithImpl<$Res>
    implements _$OutboundMessageResponseCopyWith<$Res> {
  __$OutboundMessageResponseCopyWithImpl(this._self, this._then);

  final _OutboundMessageResponse _self;
  final $Res Function(_OutboundMessageResponse) _then;

  /// Create a copy of OutboundMessageResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messagingSessionId = null,
  }) {
    return _then(_OutboundMessageResponse(
      messagingSessionId: null == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

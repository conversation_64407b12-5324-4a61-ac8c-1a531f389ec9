// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$NotificationMessage {
  @TimestampIntConverter()
  int get timestamp;
  String get notificationId;
  String get sessionId;
  String get messageCategory;
  Map<String, dynamic> get payload;
  bool get sentToWebSocket;
  bool get pushed;

  /// Create a copy of NotificationMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<NotificationMessage> get copyWith =>
      _$NotificationMessageCopyWithImpl<NotificationMessage>(
          this as NotificationMessage, _$identity);

  /// Serializes this NotificationMessage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NotificationMessage &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            const DeepCollectionEquality().equals(other.payload, payload) &&
            (identical(other.sentToWebSocket, sentToWebSocket) ||
                other.sentToWebSocket == sentToWebSocket) &&
            (identical(other.pushed, pushed) || other.pushed == pushed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      sessionId,
      messageCategory,
      const DeepCollectionEquality().hash(payload),
      sentToWebSocket,
      pushed);

  @override
  String toString() {
    return 'NotificationMessage(timestamp: $timestamp, notificationId: $notificationId, sessionId: $sessionId, messageCategory: $messageCategory, payload: $payload, sentToWebSocket: $sentToWebSocket, pushed: $pushed)';
  }
}

/// @nodoc
abstract mixin class $NotificationMessageCopyWith<$Res> {
  factory $NotificationMessageCopyWith(
          NotificationMessage value, $Res Function(NotificationMessage) _then) =
      _$NotificationMessageCopyWithImpl;
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String sessionId,
      String messageCategory,
      Map<String, dynamic> payload,
      bool sentToWebSocket,
      bool pushed});
}

/// @nodoc
class _$NotificationMessageCopyWithImpl<$Res>
    implements $NotificationMessageCopyWith<$Res> {
  _$NotificationMessageCopyWithImpl(this._self, this._then);

  final NotificationMessage _self;
  final $Res Function(NotificationMessage) _then;

  /// Create a copy of NotificationMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? sessionId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sentToWebSocket = null,
    Object? pushed = null,
  }) {
    return _then(_self.copyWith(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _self.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _self.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      sentToWebSocket: null == sentToWebSocket
          ? _self.sentToWebSocket
          : sentToWebSocket // ignore: cast_nullable_to_non_nullable
              as bool,
      pushed: null == pushed
          ? _self.pushed
          : pushed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [NotificationMessage].
extension NotificationMessagePatterns on NotificationMessage {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_NotificationMessage value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _NotificationMessage() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_NotificationMessage value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationMessage():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_NotificationMessage value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationMessage() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @TimestampIntConverter() int timestamp,
            String notificationId,
            String sessionId,
            String messageCategory,
            Map<String, dynamic> payload,
            bool sentToWebSocket,
            bool pushed)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _NotificationMessage() when $default != null:
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.sessionId,
            _that.messageCategory,
            _that.payload,
            _that.sentToWebSocket,
            _that.pushed);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @TimestampIntConverter() int timestamp,
            String notificationId,
            String sessionId,
            String messageCategory,
            Map<String, dynamic> payload,
            bool sentToWebSocket,
            bool pushed)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationMessage():
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.sessionId,
            _that.messageCategory,
            _that.payload,
            _that.sentToWebSocket,
            _that.pushed);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @TimestampIntConverter() int timestamp,
            String notificationId,
            String sessionId,
            String messageCategory,
            Map<String, dynamic> payload,
            bool sentToWebSocket,
            bool pushed)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationMessage() when $default != null:
        return $default(
            _that.timestamp,
            _that.notificationId,
            _that.sessionId,
            _that.messageCategory,
            _that.payload,
            _that.sentToWebSocket,
            _that.pushed);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _NotificationMessage implements NotificationMessage {
  const _NotificationMessage(
      {@TimestampIntConverter() required this.timestamp,
      required this.notificationId,
      required this.sessionId,
      required this.messageCategory,
      required final Map<String, dynamic> payload,
      required this.sentToWebSocket,
      required this.pushed})
      : _payload = payload;
  factory _NotificationMessage.fromJson(Map<String, dynamic> json) =>
      _$NotificationMessageFromJson(json);

  @override
  @TimestampIntConverter()
  final int timestamp;
  @override
  final String notificationId;
  @override
  final String sessionId;
  @override
  final String messageCategory;
  final Map<String, dynamic> _payload;
  @override
  Map<String, dynamic> get payload {
    if (_payload is EqualUnmodifiableMapView) return _payload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_payload);
  }

  @override
  final bool sentToWebSocket;
  @override
  final bool pushed;

  /// Create a copy of NotificationMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NotificationMessageCopyWith<_NotificationMessage> get copyWith =>
      __$NotificationMessageCopyWithImpl<_NotificationMessage>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$NotificationMessageToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NotificationMessage &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            const DeepCollectionEquality().equals(other._payload, _payload) &&
            (identical(other.sentToWebSocket, sentToWebSocket) ||
                other.sentToWebSocket == sentToWebSocket) &&
            (identical(other.pushed, pushed) || other.pushed == pushed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      sessionId,
      messageCategory,
      const DeepCollectionEquality().hash(_payload),
      sentToWebSocket,
      pushed);

  @override
  String toString() {
    return 'NotificationMessage(timestamp: $timestamp, notificationId: $notificationId, sessionId: $sessionId, messageCategory: $messageCategory, payload: $payload, sentToWebSocket: $sentToWebSocket, pushed: $pushed)';
  }
}

/// @nodoc
abstract mixin class _$NotificationMessageCopyWith<$Res>
    implements $NotificationMessageCopyWith<$Res> {
  factory _$NotificationMessageCopyWith(_NotificationMessage value,
          $Res Function(_NotificationMessage) _then) =
      __$NotificationMessageCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String sessionId,
      String messageCategory,
      Map<String, dynamic> payload,
      bool sentToWebSocket,
      bool pushed});
}

/// @nodoc
class __$NotificationMessageCopyWithImpl<$Res>
    implements _$NotificationMessageCopyWith<$Res> {
  __$NotificationMessageCopyWithImpl(this._self, this._then);

  final _NotificationMessage _self;
  final $Res Function(_NotificationMessage) _then;

  /// Create a copy of NotificationMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? sessionId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sentToWebSocket = null,
    Object? pushed = null,
  }) {
    return _then(_NotificationMessage(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _self.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _self._payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      sentToWebSocket: null == sentToWebSocket
          ? _self.sentToWebSocket
          : sentToWebSocket // ignore: cast_nullable_to_non_nullable
              as bool,
      pushed: null == pushed
          ? _self.pushed
          : pushed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

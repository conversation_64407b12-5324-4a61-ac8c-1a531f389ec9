// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_session_success_ids_update.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OutboundSessionSuccessIdsUpdate {
  String get messageId;
  String get messagingEndUserId;
  String get messagingSessionId;

  /// Create a copy of OutboundSessionSuccessIdsUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OutboundSessionSuccessIdsUpdateCopyWith<OutboundSessionSuccessIdsUpdate>
      get copyWith => _$OutboundSessionSuccessIdsUpdateCopyWithImpl<
              OutboundSessionSuccessIdsUpdate>(
          this as OutboundSessionSuccessIdsUpdate, _$identity);

  /// Serializes this OutboundSessionSuccessIdsUpdate to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OutboundSessionSuccessIdsUpdate &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, messageId, messagingEndUserId, messagingSessionId);

  @override
  String toString() {
    return 'OutboundSessionSuccessIdsUpdate(messageId: $messageId, messagingEndUserId: $messagingEndUserId, messagingSessionId: $messagingSessionId)';
  }
}

/// @nodoc
abstract mixin class $OutboundSessionSuccessIdsUpdateCopyWith<$Res> {
  factory $OutboundSessionSuccessIdsUpdateCopyWith(
          OutboundSessionSuccessIdsUpdate value,
          $Res Function(OutboundSessionSuccessIdsUpdate) _then) =
      _$OutboundSessionSuccessIdsUpdateCopyWithImpl;
  @useResult
  $Res call(
      {String messageId, String messagingEndUserId, String messagingSessionId});
}

/// @nodoc
class _$OutboundSessionSuccessIdsUpdateCopyWithImpl<$Res>
    implements $OutboundSessionSuccessIdsUpdateCopyWith<$Res> {
  _$OutboundSessionSuccessIdsUpdateCopyWithImpl(this._self, this._then);

  final OutboundSessionSuccessIdsUpdate _self;
  final $Res Function(OutboundSessionSuccessIdsUpdate) _then;

  /// Create a copy of OutboundSessionSuccessIdsUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_self.copyWith(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingSessionId: null == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [OutboundSessionSuccessIdsUpdate].
extension OutboundSessionSuccessIdsUpdatePatterns
    on OutboundSessionSuccessIdsUpdate {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OutboundSessionSuccessIdsUpdate value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundSessionSuccessIdsUpdate() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OutboundSessionSuccessIdsUpdate value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundSessionSuccessIdsUpdate():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OutboundSessionSuccessIdsUpdate value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundSessionSuccessIdsUpdate() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String messageId, String messagingEndUserId,
            String messagingSessionId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OutboundSessionSuccessIdsUpdate() when $default != null:
        return $default(_that.messageId, _that.messagingEndUserId,
            _that.messagingSessionId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String messageId, String messagingEndUserId,
            String messagingSessionId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundSessionSuccessIdsUpdate():
        return $default(_that.messageId, _that.messagingEndUserId,
            _that.messagingSessionId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String messageId, String messagingEndUserId,
            String messagingSessionId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OutboundSessionSuccessIdsUpdate() when $default != null:
        return $default(_that.messageId, _that.messagingEndUserId,
            _that.messagingSessionId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _OutboundSessionSuccessIdsUpdate
    implements OutboundSessionSuccessIdsUpdate {
  const _OutboundSessionSuccessIdsUpdate(
      {required this.messageId,
      required this.messagingEndUserId,
      required this.messagingSessionId});
  factory _OutboundSessionSuccessIdsUpdate.fromJson(
          Map<String, dynamic> json) =>
      _$OutboundSessionSuccessIdsUpdateFromJson(json);

  @override
  final String messageId;
  @override
  final String messagingEndUserId;
  @override
  final String messagingSessionId;

  /// Create a copy of OutboundSessionSuccessIdsUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OutboundSessionSuccessIdsUpdateCopyWith<_OutboundSessionSuccessIdsUpdate>
      get copyWith => __$OutboundSessionSuccessIdsUpdateCopyWithImpl<
          _OutboundSessionSuccessIdsUpdate>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OutboundSessionSuccessIdsUpdateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OutboundSessionSuccessIdsUpdate &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, messageId, messagingEndUserId, messagingSessionId);

  @override
  String toString() {
    return 'OutboundSessionSuccessIdsUpdate(messageId: $messageId, messagingEndUserId: $messagingEndUserId, messagingSessionId: $messagingSessionId)';
  }
}

/// @nodoc
abstract mixin class _$OutboundSessionSuccessIdsUpdateCopyWith<$Res>
    implements $OutboundSessionSuccessIdsUpdateCopyWith<$Res> {
  factory _$OutboundSessionSuccessIdsUpdateCopyWith(
          _OutboundSessionSuccessIdsUpdate value,
          $Res Function(_OutboundSessionSuccessIdsUpdate) _then) =
      __$OutboundSessionSuccessIdsUpdateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String messageId, String messagingEndUserId, String messagingSessionId});
}

/// @nodoc
class __$OutboundSessionSuccessIdsUpdateCopyWithImpl<$Res>
    implements _$OutboundSessionSuccessIdsUpdateCopyWith<$Res> {
  __$OutboundSessionSuccessIdsUpdateCopyWithImpl(this._self, this._then);

  final _OutboundSessionSuccessIdsUpdate _self;
  final $Res Function(_OutboundSessionSuccessIdsUpdate) _then;

  /// Create a copy of OutboundSessionSuccessIdsUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_OutboundSessionSuccessIdsUpdate(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingSessionId: null == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_sessions_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ActiveSessionsBody {
  int get tenantId;
  int get limit;
  String get key;

  /// Create a copy of ActiveSessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ActiveSessionsBodyCopyWith<ActiveSessionsBody> get copyWith =>
      _$ActiveSessionsBodyCopyWithImpl<ActiveSessionsBody>(
          this as ActiveSessionsBody, _$identity);

  /// Serializes this ActiveSessionsBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ActiveSessionsBody &&
            (identical(other.tenantId, tenantId) ||
                other.tenantId == tenantId) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, tenantId, limit, key);

  @override
  String toString() {
    return 'ActiveSessionsBody(tenantId: $tenantId, limit: $limit, key: $key)';
  }
}

/// @nodoc
abstract mixin class $ActiveSessionsBodyCopyWith<$Res> {
  factory $ActiveSessionsBodyCopyWith(
          ActiveSessionsBody value, $Res Function(ActiveSessionsBody) _then) =
      _$ActiveSessionsBodyCopyWithImpl;
  @useResult
  $Res call({int tenantId, int limit, String key});
}

/// @nodoc
class _$ActiveSessionsBodyCopyWithImpl<$Res>
    implements $ActiveSessionsBodyCopyWith<$Res> {
  _$ActiveSessionsBodyCopyWithImpl(this._self, this._then);

  final ActiveSessionsBody _self;
  final $Res Function(ActiveSessionsBody) _then;

  /// Create a copy of ActiveSessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tenantId = null,
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_self.copyWith(
      tenantId: null == tenantId
          ? _self.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ActiveSessionsBody].
extension ActiveSessionsBodyPatterns on ActiveSessionsBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ActiveSessionsBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ActiveSessionsBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ActiveSessionsBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(int tenantId, int limit, String key)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsBody() when $default != null:
        return $default(_that.tenantId, _that.limit, _that.key);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(int tenantId, int limit, String key) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsBody():
        return $default(_that.tenantId, _that.limit, _that.key);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(int tenantId, int limit, String key)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsBody() when $default != null:
        return $default(_that.tenantId, _that.limit, _that.key);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ActiveSessionsBody implements ActiveSessionsBody {
  const _ActiveSessionsBody(
      {required this.tenantId, required this.limit, required this.key});
  factory _ActiveSessionsBody.fromJson(Map<String, dynamic> json) =>
      _$ActiveSessionsBodyFromJson(json);

  @override
  final int tenantId;
  @override
  final int limit;
  @override
  final String key;

  /// Create a copy of ActiveSessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ActiveSessionsBodyCopyWith<_ActiveSessionsBody> get copyWith =>
      __$ActiveSessionsBodyCopyWithImpl<_ActiveSessionsBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ActiveSessionsBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ActiveSessionsBody &&
            (identical(other.tenantId, tenantId) ||
                other.tenantId == tenantId) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, tenantId, limit, key);

  @override
  String toString() {
    return 'ActiveSessionsBody(tenantId: $tenantId, limit: $limit, key: $key)';
  }
}

/// @nodoc
abstract mixin class _$ActiveSessionsBodyCopyWith<$Res>
    implements $ActiveSessionsBodyCopyWith<$Res> {
  factory _$ActiveSessionsBodyCopyWith(
          _ActiveSessionsBody value, $Res Function(_ActiveSessionsBody) _then) =
      __$ActiveSessionsBodyCopyWithImpl;
  @override
  @useResult
  $Res call({int tenantId, int limit, String key});
}

/// @nodoc
class __$ActiveSessionsBodyCopyWithImpl<$Res>
    implements _$ActiveSessionsBodyCopyWith<$Res> {
  __$ActiveSessionsBodyCopyWithImpl(this._self, this._then);

  final _ActiveSessionsBody _self;
  final $Res Function(_ActiveSessionsBody) _then;

  /// Create a copy of ActiveSessionsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? tenantId = null,
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_ActiveSessionsBody(
      tenantId: null == tenantId
          ? _self.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

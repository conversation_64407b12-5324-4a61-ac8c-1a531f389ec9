// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_status_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PresenceStatusResponse {
  PresenceConfiguration? get presenceConfiguration;
  String? get scrtAccessToken;

  /// Create a copy of PresenceStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PresenceStatusResponseCopyWith<PresenceStatusResponse> get copyWith =>
      _$PresenceStatusResponseCopyWithImpl<PresenceStatusResponse>(
          this as PresenceStatusResponse, _$identity);

  /// Serializes this PresenceStatusResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PresenceStatusResponse &&
            (identical(other.presenceConfiguration, presenceConfiguration) ||
                other.presenceConfiguration == presenceConfiguration) &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, presenceConfiguration, scrtAccessToken);

  @override
  String toString() {
    return 'PresenceStatusResponse(presenceConfiguration: $presenceConfiguration, scrtAccessToken: $scrtAccessToken)';
  }
}

/// @nodoc
abstract mixin class $PresenceStatusResponseCopyWith<$Res> {
  factory $PresenceStatusResponseCopyWith(PresenceStatusResponse value,
          $Res Function(PresenceStatusResponse) _then) =
      _$PresenceStatusResponseCopyWithImpl;
  @useResult
  $Res call(
      {PresenceConfiguration? presenceConfiguration, String? scrtAccessToken});

  $PresenceConfigurationCopyWith<$Res>? get presenceConfiguration;
}

/// @nodoc
class _$PresenceStatusResponseCopyWithImpl<$Res>
    implements $PresenceStatusResponseCopyWith<$Res> {
  _$PresenceStatusResponseCopyWithImpl(this._self, this._then);

  final PresenceStatusResponse _self;
  final $Res Function(PresenceStatusResponse) _then;

  /// Create a copy of PresenceStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presenceConfiguration = freezed,
    Object? scrtAccessToken = freezed,
  }) {
    return _then(_self.copyWith(
      presenceConfiguration: freezed == presenceConfiguration
          ? _self.presenceConfiguration
          : presenceConfiguration // ignore: cast_nullable_to_non_nullable
              as PresenceConfiguration?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _self.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of PresenceStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PresenceConfigurationCopyWith<$Res>? get presenceConfiguration {
    if (_self.presenceConfiguration == null) {
      return null;
    }

    return $PresenceConfigurationCopyWith<$Res>(_self.presenceConfiguration!,
        (value) {
      return _then(_self.copyWith(presenceConfiguration: value));
    });
  }
}

/// Adds pattern-matching-related methods to [PresenceStatusResponse].
extension PresenceStatusResponsePatterns on PresenceStatusResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PresenceStatusResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PresenceStatusResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PresenceStatusResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(PresenceConfiguration? presenceConfiguration,
            String? scrtAccessToken)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusResponse() when $default != null:
        return $default(_that.presenceConfiguration, _that.scrtAccessToken);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(PresenceConfiguration? presenceConfiguration,
            String? scrtAccessToken)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusResponse():
        return $default(_that.presenceConfiguration, _that.scrtAccessToken);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(PresenceConfiguration? presenceConfiguration,
            String? scrtAccessToken)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusResponse() when $default != null:
        return $default(_that.presenceConfiguration, _that.scrtAccessToken);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PresenceStatusResponse implements PresenceStatusResponse {
  const _PresenceStatusResponse(
      {this.presenceConfiguration, this.scrtAccessToken});
  factory _PresenceStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$PresenceStatusResponseFromJson(json);

  @override
  final PresenceConfiguration? presenceConfiguration;
  @override
  final String? scrtAccessToken;

  /// Create a copy of PresenceStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PresenceStatusResponseCopyWith<_PresenceStatusResponse> get copyWith =>
      __$PresenceStatusResponseCopyWithImpl<_PresenceStatusResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PresenceStatusResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PresenceStatusResponse &&
            (identical(other.presenceConfiguration, presenceConfiguration) ||
                other.presenceConfiguration == presenceConfiguration) &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, presenceConfiguration, scrtAccessToken);

  @override
  String toString() {
    return 'PresenceStatusResponse(presenceConfiguration: $presenceConfiguration, scrtAccessToken: $scrtAccessToken)';
  }
}

/// @nodoc
abstract mixin class _$PresenceStatusResponseCopyWith<$Res>
    implements $PresenceStatusResponseCopyWith<$Res> {
  factory _$PresenceStatusResponseCopyWith(_PresenceStatusResponse value,
          $Res Function(_PresenceStatusResponse) _then) =
      __$PresenceStatusResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {PresenceConfiguration? presenceConfiguration, String? scrtAccessToken});

  @override
  $PresenceConfigurationCopyWith<$Res>? get presenceConfiguration;
}

/// @nodoc
class __$PresenceStatusResponseCopyWithImpl<$Res>
    implements _$PresenceStatusResponseCopyWith<$Res> {
  __$PresenceStatusResponseCopyWithImpl(this._self, this._then);

  final _PresenceStatusResponse _self;
  final $Res Function(_PresenceStatusResponse) _then;

  /// Create a copy of PresenceStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? presenceConfiguration = freezed,
    Object? scrtAccessToken = freezed,
  }) {
    return _then(_PresenceStatusResponse(
      presenceConfiguration: freezed == presenceConfiguration
          ? _self.presenceConfiguration
          : presenceConfiguration // ignore: cast_nullable_to_non_nullable
              as PresenceConfiguration?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _self.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of PresenceStatusResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PresenceConfigurationCopyWith<$Res>? get presenceConfiguration {
    if (_self.presenceConfiguration == null) {
      return null;
    }

    return $PresenceConfigurationCopyWith<$Res>(_self.presenceConfiguration!,
        (value) {
      return _then(_self.copyWith(presenceConfiguration: value));
    });
  }
}

// dart format on

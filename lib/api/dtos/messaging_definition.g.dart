// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_definition.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingDefinition _$MessagingDefinitionFromJson(Map json) => $checkedCreate(
      '_MessagingDefinition',
      json,
      ($checkedConvert) {
        final val = _MessagingDefinition(
          id: $checkedConvert(
              'id', (v) => const ParseSfIdConverter().fromJson(v)),
          name: $checkedConvert('name', (v) => v as String?),
          internalName: $checkedConvert('internalName', (v) => v as String?),
          description:
              $checkedConvert('description', (v) => v as String? ?? ''),
          hasRequiredParameters:
              $checkedConvert('hasRequiredParameters', (v) => v as bool?),
        );
        return val;
      },
    );

Map<String, dynamic> _$MessagingDefinitionToJson(
        _MessagingDefinition instance) =>
    <String, dynamic>{
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.id, const ParseSfIdConverter().toJson)
          case final value?)
        'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.internalName case final value?) 'internalName': value,
      if (instance.description case final value?) 'description': value,
      if (instance.hasRequiredParameters case final value?)
        'hasRequiredParameters': value,
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);

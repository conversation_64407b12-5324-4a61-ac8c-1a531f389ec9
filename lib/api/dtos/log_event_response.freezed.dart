// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_event_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LogEventResponse {
  bool get success;
  int? get tooNewStartIndex;
  int? get tooOldEndIndex;

  /// Create a copy of LogEventResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LogEventResponseCopyWith<LogEventResponse> get copyWith =>
      _$LogEventResponseCopyWithImpl<LogEventResponse>(
          this as LogEventResponse, _$identity);

  /// Serializes this LogEventResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LogEventResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.tooNewStartIndex, tooNewStartIndex) ||
                other.tooNewStartIndex == tooNewStartIndex) &&
            (identical(other.tooOldEndIndex, tooOldEndIndex) ||
                other.tooOldEndIndex == tooOldEndIndex));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, tooNewStartIndex, tooOldEndIndex);

  @override
  String toString() {
    return 'LogEventResponse(success: $success, tooNewStartIndex: $tooNewStartIndex, tooOldEndIndex: $tooOldEndIndex)';
  }
}

/// @nodoc
abstract mixin class $LogEventResponseCopyWith<$Res> {
  factory $LogEventResponseCopyWith(
          LogEventResponse value, $Res Function(LogEventResponse) _then) =
      _$LogEventResponseCopyWithImpl;
  @useResult
  $Res call({bool success, int? tooNewStartIndex, int? tooOldEndIndex});
}

/// @nodoc
class _$LogEventResponseCopyWithImpl<$Res>
    implements $LogEventResponseCopyWith<$Res> {
  _$LogEventResponseCopyWithImpl(this._self, this._then);

  final LogEventResponse _self;
  final $Res Function(LogEventResponse) _then;

  /// Create a copy of LogEventResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? tooNewStartIndex = freezed,
    Object? tooOldEndIndex = freezed,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      tooNewStartIndex: freezed == tooNewStartIndex
          ? _self.tooNewStartIndex
          : tooNewStartIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      tooOldEndIndex: freezed == tooOldEndIndex
          ? _self.tooOldEndIndex
          : tooOldEndIndex // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// Adds pattern-matching-related methods to [LogEventResponse].
extension LogEventResponsePatterns on LogEventResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_LogEventResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogEventResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_LogEventResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_LogEventResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool success, int? tooNewStartIndex, int? tooOldEndIndex)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogEventResponse() when $default != null:
        return $default(
            _that.success, _that.tooNewStartIndex, _that.tooOldEndIndex);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool success, int? tooNewStartIndex, int? tooOldEndIndex)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventResponse():
        return $default(
            _that.success, _that.tooNewStartIndex, _that.tooOldEndIndex);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool success, int? tooNewStartIndex, int? tooOldEndIndex)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEventResponse() when $default != null:
        return $default(
            _that.success, _that.tooNewStartIndex, _that.tooOldEndIndex);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _LogEventResponse implements LogEventResponse {
  const _LogEventResponse(
      {required this.success, this.tooNewStartIndex, this.tooOldEndIndex});
  factory _LogEventResponse.fromJson(Map<String, dynamic> json) =>
      _$LogEventResponseFromJson(json);

  @override
  final bool success;
  @override
  final int? tooNewStartIndex;
  @override
  final int? tooOldEndIndex;

  /// Create a copy of LogEventResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LogEventResponseCopyWith<_LogEventResponse> get copyWith =>
      __$LogEventResponseCopyWithImpl<_LogEventResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LogEventResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LogEventResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.tooNewStartIndex, tooNewStartIndex) ||
                other.tooNewStartIndex == tooNewStartIndex) &&
            (identical(other.tooOldEndIndex, tooOldEndIndex) ||
                other.tooOldEndIndex == tooOldEndIndex));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, tooNewStartIndex, tooOldEndIndex);

  @override
  String toString() {
    return 'LogEventResponse(success: $success, tooNewStartIndex: $tooNewStartIndex, tooOldEndIndex: $tooOldEndIndex)';
  }
}

/// @nodoc
abstract mixin class _$LogEventResponseCopyWith<$Res>
    implements $LogEventResponseCopyWith<$Res> {
  factory _$LogEventResponseCopyWith(
          _LogEventResponse value, $Res Function(_LogEventResponse) _then) =
      __$LogEventResponseCopyWithImpl;
  @override
  @useResult
  $Res call({bool success, int? tooNewStartIndex, int? tooOldEndIndex});
}

/// @nodoc
class __$LogEventResponseCopyWithImpl<$Res>
    implements _$LogEventResponseCopyWith<$Res> {
  __$LogEventResponseCopyWithImpl(this._self, this._then);

  final _LogEventResponse _self;
  final $Res Function(_LogEventResponse) _then;

  /// Create a copy of LogEventResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? tooNewStartIndex = freezed,
    Object? tooOldEndIndex = freezed,
  }) {
    return _then(_LogEventResponse(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      tooNewStartIndex: freezed == tooNewStartIndex
          ? _self.tooNewStartIndex
          : tooNewStartIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      tooOldEndIndex: freezed == tooOldEndIndex
          ? _self.tooOldEndIndex
          : tooOldEndIndex // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on

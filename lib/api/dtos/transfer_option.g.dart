// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_option.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransferOption _$TransferOptionFromJson(Map json) => $checkedCreate(
      '_TransferOption',
      json,
      ($checkedConvert) {
        final val = _TransferOption(
          destinationType: $checkedConvert('destinationType',
              (v) => $enumDecode(_$TransferDestinationTypeEnumMap, v)),
          destinations: $checkedConvert(
              'destinations',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => TransferDestination.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <TransferDestination>[]),
        );
        return val;
      },
    );

Map<String, dynamic> _$TransferOptionToJson(_TransferOption instance) =>
    <String, dynamic>{
      'destinationType':
          _$TransferDestinationTypeEnumMap[instance.destinationType]!,
      'destinations': instance.destinations.map((e) => e.toJson()).toList(),
    };

const _$TransferDestinationTypeEnumMap = {
  TransferDestinationType.queue: 'queue',
  TransferDestinationType.agent: 'agent',
  TransferDestinationType.flow: 'flow',
};

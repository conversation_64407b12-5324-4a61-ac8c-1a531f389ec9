// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unacknowledged_notifications_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UnacknowledgedNotificationsBody {
  int get limit;
  String get key;

  /// Create a copy of UnacknowledgedNotificationsBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnacknowledgedNotificationsBodyCopyWith<UnacknowledgedNotificationsBody>
      get copyWith => _$UnacknowledgedNotificationsBodyCopyWithImpl<
              UnacknowledgedNotificationsBody>(
          this as UnacknowledgedNotificationsBody, _$identity);

  /// Serializes this UnacknowledgedNotificationsBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnacknowledgedNotificationsBody &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, limit, key);

  @override
  String toString() {
    return 'UnacknowledgedNotificationsBody(limit: $limit, key: $key)';
  }
}

/// @nodoc
abstract mixin class $UnacknowledgedNotificationsBodyCopyWith<$Res> {
  factory $UnacknowledgedNotificationsBodyCopyWith(
          UnacknowledgedNotificationsBody value,
          $Res Function(UnacknowledgedNotificationsBody) _then) =
      _$UnacknowledgedNotificationsBodyCopyWithImpl;
  @useResult
  $Res call({int limit, String key});
}

/// @nodoc
class _$UnacknowledgedNotificationsBodyCopyWithImpl<$Res>
    implements $UnacknowledgedNotificationsBodyCopyWith<$Res> {
  _$UnacknowledgedNotificationsBodyCopyWithImpl(this._self, this._then);

  final UnacknowledgedNotificationsBody _self;
  final $Res Function(UnacknowledgedNotificationsBody) _then;

  /// Create a copy of UnacknowledgedNotificationsBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_self.copyWith(
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [UnacknowledgedNotificationsBody].
extension UnacknowledgedNotificationsBodyPatterns
    on UnacknowledgedNotificationsBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UnacknowledgedNotificationsBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UnacknowledgedNotificationsBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UnacknowledgedNotificationsBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(int limit, String key)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsBody() when $default != null:
        return $default(_that.limit, _that.key);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(int limit, String key) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsBody():
        return $default(_that.limit, _that.key);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(int limit, String key)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UnacknowledgedNotificationsBody() when $default != null:
        return $default(_that.limit, _that.key);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UnacknowledgedNotificationsBody
    implements UnacknowledgedNotificationsBody {
  const _UnacknowledgedNotificationsBody(
      {required this.limit, required this.key});
  factory _UnacknowledgedNotificationsBody.fromJson(
          Map<String, dynamic> json) =>
      _$UnacknowledgedNotificationsBodyFromJson(json);

  @override
  final int limit;
  @override
  final String key;

  /// Create a copy of UnacknowledgedNotificationsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UnacknowledgedNotificationsBodyCopyWith<_UnacknowledgedNotificationsBody>
      get copyWith => __$UnacknowledgedNotificationsBodyCopyWithImpl<
          _UnacknowledgedNotificationsBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UnacknowledgedNotificationsBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UnacknowledgedNotificationsBody &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, limit, key);

  @override
  String toString() {
    return 'UnacknowledgedNotificationsBody(limit: $limit, key: $key)';
  }
}

/// @nodoc
abstract mixin class _$UnacknowledgedNotificationsBodyCopyWith<$Res>
    implements $UnacknowledgedNotificationsBodyCopyWith<$Res> {
  factory _$UnacknowledgedNotificationsBodyCopyWith(
          _UnacknowledgedNotificationsBody value,
          $Res Function(_UnacknowledgedNotificationsBody) _then) =
      __$UnacknowledgedNotificationsBodyCopyWithImpl;
  @override
  @useResult
  $Res call({int limit, String key});
}

/// @nodoc
class __$UnacknowledgedNotificationsBodyCopyWithImpl<$Res>
    implements _$UnacknowledgedNotificationsBodyCopyWith<$Res> {
  __$UnacknowledgedNotificationsBodyCopyWithImpl(this._self, this._then);

  final _UnacknowledgedNotificationsBody _self;
  final $Res Function(_UnacknowledgedNotificationsBody) _then;

  /// Create a copy of UnacknowledgedNotificationsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_UnacknowledgedNotificationsBody(
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

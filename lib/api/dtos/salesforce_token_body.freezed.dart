// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesforceTokenBody {
  String get orgId;
  String get accessToken;
  int? get expirationSeconds;

  /// Create a copy of SalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SalesforceTokenBodyCopyWith<SalesforceTokenBody> get copyWith =>
      _$SalesforceTokenBodyCopyWithImpl<SalesforceTokenBody>(
          this as SalesforceTokenBody, _$identity);

  /// Serializes this SalesforceTokenBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SalesforceTokenBody &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.expirationSeconds, expirationSeconds) ||
                other.expirationSeconds == expirationSeconds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, orgId, accessToken, expirationSeconds);

  @override
  String toString() {
    return 'SalesforceTokenBody(orgId: $orgId, accessToken: $accessToken, expirationSeconds: $expirationSeconds)';
  }
}

/// @nodoc
abstract mixin class $SalesforceTokenBodyCopyWith<$Res> {
  factory $SalesforceTokenBodyCopyWith(
          SalesforceTokenBody value, $Res Function(SalesforceTokenBody) _then) =
      _$SalesforceTokenBodyCopyWithImpl;
  @useResult
  $Res call({String orgId, String accessToken, int? expirationSeconds});
}

/// @nodoc
class _$SalesforceTokenBodyCopyWithImpl<$Res>
    implements $SalesforceTokenBodyCopyWith<$Res> {
  _$SalesforceTokenBodyCopyWithImpl(this._self, this._then);

  final SalesforceTokenBody _self;
  final $Res Function(SalesforceTokenBody) _then;

  /// Create a copy of SalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orgId = null,
    Object? accessToken = null,
    Object? expirationSeconds = freezed,
  }) {
    return _then(_self.copyWith(
      orgId: null == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      expirationSeconds: freezed == expirationSeconds
          ? _self.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SalesforceTokenBody].
extension SalesforceTokenBodyPatterns on SalesforceTokenBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SalesforceTokenBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SalesforceTokenBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SalesforceTokenBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String orgId, String accessToken, int? expirationSeconds)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenBody() when $default != null:
        return $default(
            _that.orgId, _that.accessToken, _that.expirationSeconds);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String orgId, String accessToken, int? expirationSeconds)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenBody():
        return $default(
            _that.orgId, _that.accessToken, _that.expirationSeconds);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String orgId, String accessToken, int? expirationSeconds)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenBody() when $default != null:
        return $default(
            _that.orgId, _that.accessToken, _that.expirationSeconds);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SalesforceTokenBody implements SalesforceTokenBody {
  const _SalesforceTokenBody(
      {required this.orgId, required this.accessToken, this.expirationSeconds});
  factory _SalesforceTokenBody.fromJson(Map<String, dynamic> json) =>
      _$SalesforceTokenBodyFromJson(json);

  @override
  final String orgId;
  @override
  final String accessToken;
  @override
  final int? expirationSeconds;

  /// Create a copy of SalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SalesforceTokenBodyCopyWith<_SalesforceTokenBody> get copyWith =>
      __$SalesforceTokenBodyCopyWithImpl<_SalesforceTokenBody>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SalesforceTokenBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SalesforceTokenBody &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.expirationSeconds, expirationSeconds) ||
                other.expirationSeconds == expirationSeconds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, orgId, accessToken, expirationSeconds);

  @override
  String toString() {
    return 'SalesforceTokenBody(orgId: $orgId, accessToken: $accessToken, expirationSeconds: $expirationSeconds)';
  }
}

/// @nodoc
abstract mixin class _$SalesforceTokenBodyCopyWith<$Res>
    implements $SalesforceTokenBodyCopyWith<$Res> {
  factory _$SalesforceTokenBodyCopyWith(_SalesforceTokenBody value,
          $Res Function(_SalesforceTokenBody) _then) =
      __$SalesforceTokenBodyCopyWithImpl;
  @override
  @useResult
  $Res call({String orgId, String accessToken, int? expirationSeconds});
}

/// @nodoc
class __$SalesforceTokenBodyCopyWithImpl<$Res>
    implements _$SalesforceTokenBodyCopyWith<$Res> {
  __$SalesforceTokenBodyCopyWithImpl(this._self, this._then);

  final _SalesforceTokenBody _self;
  final $Res Function(_SalesforceTokenBody) _then;

  /// Create a copy of SalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? orgId = null,
    Object? accessToken = null,
    Object? expirationSeconds = freezed,
  }) {
    return _then(_SalesforceTokenBody(
      orgId: null == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      expirationSeconds: freezed == expirationSeconds
          ? _self.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_messages_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkMessagesBody {
  List<WorkMessageBody> get messages;

  /// Create a copy of WorkMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkMessagesBodyCopyWith<WorkMessagesBody> get copyWith =>
      _$WorkMessagesBodyCopyWithImpl<WorkMessagesBody>(
          this as WorkMessagesBody, _$identity);

  /// Serializes this WorkMessagesBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkMessagesBody &&
            const DeepCollectionEquality().equals(other.messages, messages));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(messages));

  @override
  String toString() {
    return 'WorkMessagesBody(messages: $messages)';
  }
}

/// @nodoc
abstract mixin class $WorkMessagesBodyCopyWith<$Res> {
  factory $WorkMessagesBodyCopyWith(
          WorkMessagesBody value, $Res Function(WorkMessagesBody) _then) =
      _$WorkMessagesBodyCopyWithImpl;
  @useResult
  $Res call({List<WorkMessageBody> messages});
}

/// @nodoc
class _$WorkMessagesBodyCopyWithImpl<$Res>
    implements $WorkMessagesBodyCopyWith<$Res> {
  _$WorkMessagesBodyCopyWithImpl(this._self, this._then);

  final WorkMessagesBody _self;
  final $Res Function(WorkMessagesBody) _then;

  /// Create a copy of WorkMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
  }) {
    return _then(_self.copyWith(
      messages: null == messages
          ? _self.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<WorkMessageBody>,
    ));
  }
}

/// Adds pattern-matching-related methods to [WorkMessagesBody].
extension WorkMessagesBodyPatterns on WorkMessagesBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WorkMessagesBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WorkMessagesBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WorkMessagesBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<WorkMessageBody> messages)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesBody() when $default != null:
        return $default(_that.messages);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<WorkMessageBody> messages) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesBody():
        return $default(_that.messages);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<WorkMessageBody> messages)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesBody() when $default != null:
        return $default(_that.messages);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WorkMessagesBody implements WorkMessagesBody {
  const _WorkMessagesBody({required final List<WorkMessageBody> messages})
      : _messages = messages;
  factory _WorkMessagesBody.fromJson(Map<String, dynamic> json) =>
      _$WorkMessagesBodyFromJson(json);

  final List<WorkMessageBody> _messages;
  @override
  List<WorkMessageBody> get messages {
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messages);
  }

  /// Create a copy of WorkMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkMessagesBodyCopyWith<_WorkMessagesBody> get copyWith =>
      __$WorkMessagesBodyCopyWithImpl<_WorkMessagesBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkMessagesBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkMessagesBody &&
            const DeepCollectionEquality().equals(other._messages, _messages));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_messages));

  @override
  String toString() {
    return 'WorkMessagesBody(messages: $messages)';
  }
}

/// @nodoc
abstract mixin class _$WorkMessagesBodyCopyWith<$Res>
    implements $WorkMessagesBodyCopyWith<$Res> {
  factory _$WorkMessagesBodyCopyWith(
          _WorkMessagesBody value, $Res Function(_WorkMessagesBody) _then) =
      __$WorkMessagesBodyCopyWithImpl;
  @override
  @useResult
  $Res call({List<WorkMessageBody> messages});
}

/// @nodoc
class __$WorkMessagesBodyCopyWithImpl<$Res>
    implements _$WorkMessagesBodyCopyWith<$Res> {
  __$WorkMessagesBodyCopyWithImpl(this._self, this._then);

  final _WorkMessagesBody _self;
  final $Res Function(_WorkMessagesBody) _then;

  /// Create a copy of WorkMessagesBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messages = null,
  }) {
    return _then(_WorkMessagesBody(
      messages: null == messages
          ? _self._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<WorkMessageBody>,
    ));
  }
}

// dart format on

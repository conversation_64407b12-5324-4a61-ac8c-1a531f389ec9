// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'channel_messaging_definitions_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChannelMessagingDefinitionsResponse {
  List<MessagingDefinition> get definitions;

  /// Create a copy of ChannelMessagingDefinitionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChannelMessagingDefinitionsResponseCopyWith<
          ChannelMessagingDefinitionsResponse>
      get copyWith => _$ChannelMessagingDefinitionsResponseCopyWithImpl<
              ChannelMessagingDefinitionsResponse>(
          this as ChannelMessagingDefinitionsResponse, _$identity);

  /// Serializes this ChannelMessagingDefinitionsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChannelMessagingDefinitionsResponse &&
            const DeepCollectionEquality()
                .equals(other.definitions, definitions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(definitions));

  @override
  String toString() {
    return 'ChannelMessagingDefinitionsResponse(definitions: $definitions)';
  }
}

/// @nodoc
abstract mixin class $ChannelMessagingDefinitionsResponseCopyWith<$Res> {
  factory $ChannelMessagingDefinitionsResponseCopyWith(
          ChannelMessagingDefinitionsResponse value,
          $Res Function(ChannelMessagingDefinitionsResponse) _then) =
      _$ChannelMessagingDefinitionsResponseCopyWithImpl;
  @useResult
  $Res call({List<MessagingDefinition> definitions});
}

/// @nodoc
class _$ChannelMessagingDefinitionsResponseCopyWithImpl<$Res>
    implements $ChannelMessagingDefinitionsResponseCopyWith<$Res> {
  _$ChannelMessagingDefinitionsResponseCopyWithImpl(this._self, this._then);

  final ChannelMessagingDefinitionsResponse _self;
  final $Res Function(ChannelMessagingDefinitionsResponse) _then;

  /// Create a copy of ChannelMessagingDefinitionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? definitions = null,
  }) {
    return _then(_self.copyWith(
      definitions: null == definitions
          ? _self.definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>,
    ));
  }
}

/// Adds pattern-matching-related methods to [ChannelMessagingDefinitionsResponse].
extension ChannelMessagingDefinitionsResponsePatterns
    on ChannelMessagingDefinitionsResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ChannelMessagingDefinitionsResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChannelMessagingDefinitionsResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ChannelMessagingDefinitionsResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelMessagingDefinitionsResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ChannelMessagingDefinitionsResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelMessagingDefinitionsResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<MessagingDefinition> definitions)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChannelMessagingDefinitionsResponse() when $default != null:
        return $default(_that.definitions);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<MessagingDefinition> definitions) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelMessagingDefinitionsResponse():
        return $default(_that.definitions);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<MessagingDefinition> definitions)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelMessagingDefinitionsResponse() when $default != null:
        return $default(_that.definitions);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ChannelMessagingDefinitionsResponse
    implements ChannelMessagingDefinitionsResponse {
  const _ChannelMessagingDefinitionsResponse(
      {required final List<MessagingDefinition> definitions})
      : _definitions = definitions;
  factory _ChannelMessagingDefinitionsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$ChannelMessagingDefinitionsResponseFromJson(json);

  final List<MessagingDefinition> _definitions;
  @override
  List<MessagingDefinition> get definitions {
    if (_definitions is EqualUnmodifiableListView) return _definitions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_definitions);
  }

  /// Create a copy of ChannelMessagingDefinitionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ChannelMessagingDefinitionsResponseCopyWith<
          _ChannelMessagingDefinitionsResponse>
      get copyWith => __$ChannelMessagingDefinitionsResponseCopyWithImpl<
          _ChannelMessagingDefinitionsResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ChannelMessagingDefinitionsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChannelMessagingDefinitionsResponse &&
            const DeepCollectionEquality()
                .equals(other._definitions, _definitions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_definitions));

  @override
  String toString() {
    return 'ChannelMessagingDefinitionsResponse(definitions: $definitions)';
  }
}

/// @nodoc
abstract mixin class _$ChannelMessagingDefinitionsResponseCopyWith<$Res>
    implements $ChannelMessagingDefinitionsResponseCopyWith<$Res> {
  factory _$ChannelMessagingDefinitionsResponseCopyWith(
          _ChannelMessagingDefinitionsResponse value,
          $Res Function(_ChannelMessagingDefinitionsResponse) _then) =
      __$ChannelMessagingDefinitionsResponseCopyWithImpl;
  @override
  @useResult
  $Res call({List<MessagingDefinition> definitions});
}

/// @nodoc
class __$ChannelMessagingDefinitionsResponseCopyWithImpl<$Res>
    implements _$ChannelMessagingDefinitionsResponseCopyWith<$Res> {
  __$ChannelMessagingDefinitionsResponseCopyWithImpl(this._self, this._then);

  final _ChannelMessagingDefinitionsResponse _self;
  final $Res Function(_ChannelMessagingDefinitionsResponse) _then;

  /// Create a copy of ChannelMessagingDefinitionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? definitions = null,
  }) {
    return _then(_ChannelMessagingDefinitionsResponse(
      definitions: null == definitions
          ? _self._definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>,
    ));
  }
}

// dart format on

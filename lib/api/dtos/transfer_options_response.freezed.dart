// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_options_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TransferOptionsResponse {
  List<TransferOption> get options;

  /// Create a copy of TransferOptionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TransferOptionsResponseCopyWith<TransferOptionsResponse> get copyWith =>
      _$TransferOptionsResponseCopyWithImpl<TransferOptionsResponse>(
          this as TransferOptionsResponse, _$identity);

  /// Serializes this TransferOptionsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TransferOptionsResponse &&
            const DeepCollectionEquality().equals(other.options, options));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(options));

  @override
  String toString() {
    return 'TransferOptionsResponse(options: $options)';
  }
}

/// @nodoc
abstract mixin class $TransferOptionsResponseCopyWith<$Res> {
  factory $TransferOptionsResponseCopyWith(TransferOptionsResponse value,
          $Res Function(TransferOptionsResponse) _then) =
      _$TransferOptionsResponseCopyWithImpl;
  @useResult
  $Res call({List<TransferOption> options});
}

/// @nodoc
class _$TransferOptionsResponseCopyWithImpl<$Res>
    implements $TransferOptionsResponseCopyWith<$Res> {
  _$TransferOptionsResponseCopyWithImpl(this._self, this._then);

  final TransferOptionsResponse _self;
  final $Res Function(TransferOptionsResponse) _then;

  /// Create a copy of TransferOptionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? options = null,
  }) {
    return _then(_self.copyWith(
      options: null == options
          ? _self.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
    ));
  }
}

/// Adds pattern-matching-related methods to [TransferOptionsResponse].
extension TransferOptionsResponsePatterns on TransferOptionsResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_TransferOptionsResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferOptionsResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_TransferOptionsResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOptionsResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_TransferOptionsResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOptionsResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<TransferOption> options)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferOptionsResponse() when $default != null:
        return $default(_that.options);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<TransferOption> options) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOptionsResponse():
        return $default(_that.options);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<TransferOption> options)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOptionsResponse() when $default != null:
        return $default(_that.options);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _TransferOptionsResponse implements TransferOptionsResponse {
  const _TransferOptionsResponse({required final List<TransferOption> options})
      : _options = options;
  factory _TransferOptionsResponse.fromJson(Map<String, dynamic> json) =>
      _$TransferOptionsResponseFromJson(json);

  final List<TransferOption> _options;
  @override
  List<TransferOption> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  /// Create a copy of TransferOptionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TransferOptionsResponseCopyWith<_TransferOptionsResponse> get copyWith =>
      __$TransferOptionsResponseCopyWithImpl<_TransferOptionsResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TransferOptionsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TransferOptionsResponse &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_options));

  @override
  String toString() {
    return 'TransferOptionsResponse(options: $options)';
  }
}

/// @nodoc
abstract mixin class _$TransferOptionsResponseCopyWith<$Res>
    implements $TransferOptionsResponseCopyWith<$Res> {
  factory _$TransferOptionsResponseCopyWith(_TransferOptionsResponse value,
          $Res Function(_TransferOptionsResponse) _then) =
      __$TransferOptionsResponseCopyWithImpl;
  @override
  @useResult
  $Res call({List<TransferOption> options});
}

/// @nodoc
class __$TransferOptionsResponseCopyWithImpl<$Res>
    implements _$TransferOptionsResponseCopyWith<$Res> {
  __$TransferOptionsResponseCopyWithImpl(this._self, this._then);

  final _TransferOptionsResponse _self;
  final $Res Function(_TransferOptionsResponse) _then;

  /// Create a copy of TransferOptionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? options = null,
  }) {
    return _then(_TransferOptionsResponse(
      options: null == options
          ? _self._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
    ));
  }
}

// dart format on

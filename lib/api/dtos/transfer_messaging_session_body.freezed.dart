// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_messaging_session_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TransferMessagingSessionBody {
  TransferDestinationType get destinationType;
  @ParseSfIdConverter()
  SfId get destinationId;

  /// Create a copy of TransferMessagingSessionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TransferMessagingSessionBodyCopyWith<TransferMessagingSessionBody>
      get copyWith => _$TransferMessagingSessionBodyCopyWithImpl<
              TransferMessagingSessionBody>(
          this as TransferMessagingSessionBody, _$identity);

  /// Serializes this TransferMessagingSessionBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TransferMessagingSessionBody &&
            (identical(other.destinationType, destinationType) ||
                other.destinationType == destinationType) &&
            (identical(other.destinationId, destinationId) ||
                other.destinationId == destinationId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, destinationType, destinationId);

  @override
  String toString() {
    return 'TransferMessagingSessionBody(destinationType: $destinationType, destinationId: $destinationId)';
  }
}

/// @nodoc
abstract mixin class $TransferMessagingSessionBodyCopyWith<$Res> {
  factory $TransferMessagingSessionBodyCopyWith(
          TransferMessagingSessionBody value,
          $Res Function(TransferMessagingSessionBody) _then) =
      _$TransferMessagingSessionBodyCopyWithImpl;
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      @ParseSfIdConverter() SfId destinationId});

  $SfIdCopyWith<$Res> get destinationId;
}

/// @nodoc
class _$TransferMessagingSessionBodyCopyWithImpl<$Res>
    implements $TransferMessagingSessionBodyCopyWith<$Res> {
  _$TransferMessagingSessionBodyCopyWithImpl(this._self, this._then);

  final TransferMessagingSessionBody _self;
  final $Res Function(TransferMessagingSessionBody) _then;

  /// Create a copy of TransferMessagingSessionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destinationType = null,
    Object? destinationId = null,
  }) {
    return _then(_self.copyWith(
      destinationType: null == destinationType
          ? _self.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinationId: null == destinationId
          ? _self.destinationId
          : destinationId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of TransferMessagingSessionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get destinationId {
    return $SfIdCopyWith<$Res>(_self.destinationId, (value) {
      return _then(_self.copyWith(destinationId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [TransferMessagingSessionBody].
extension TransferMessagingSessionBodyPatterns on TransferMessagingSessionBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_TransferMessagingSessionBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferMessagingSessionBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_TransferMessagingSessionBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferMessagingSessionBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_TransferMessagingSessionBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferMessagingSessionBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(TransferDestinationType destinationType,
            @ParseSfIdConverter() SfId destinationId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferMessagingSessionBody() when $default != null:
        return $default(_that.destinationType, _that.destinationId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(TransferDestinationType destinationType,
            @ParseSfIdConverter() SfId destinationId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferMessagingSessionBody():
        return $default(_that.destinationType, _that.destinationId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(TransferDestinationType destinationType,
            @ParseSfIdConverter() SfId destinationId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferMessagingSessionBody() when $default != null:
        return $default(_that.destinationType, _that.destinationId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _TransferMessagingSessionBody extends TransferMessagingSessionBody {
  const _TransferMessagingSessionBody(
      {required this.destinationType,
      @ParseSfIdConverter() required this.destinationId})
      : super._();
  factory _TransferMessagingSessionBody.fromJson(Map<String, dynamic> json) =>
      _$TransferMessagingSessionBodyFromJson(json);

  @override
  final TransferDestinationType destinationType;
  @override
  @ParseSfIdConverter()
  final SfId destinationId;

  /// Create a copy of TransferMessagingSessionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TransferMessagingSessionBodyCopyWith<_TransferMessagingSessionBody>
      get copyWith => __$TransferMessagingSessionBodyCopyWithImpl<
          _TransferMessagingSessionBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TransferMessagingSessionBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TransferMessagingSessionBody &&
            (identical(other.destinationType, destinationType) ||
                other.destinationType == destinationType) &&
            (identical(other.destinationId, destinationId) ||
                other.destinationId == destinationId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, destinationType, destinationId);

  @override
  String toString() {
    return 'TransferMessagingSessionBody(destinationType: $destinationType, destinationId: $destinationId)';
  }
}

/// @nodoc
abstract mixin class _$TransferMessagingSessionBodyCopyWith<$Res>
    implements $TransferMessagingSessionBodyCopyWith<$Res> {
  factory _$TransferMessagingSessionBodyCopyWith(
          _TransferMessagingSessionBody value,
          $Res Function(_TransferMessagingSessionBody) _then) =
      __$TransferMessagingSessionBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      @ParseSfIdConverter() SfId destinationId});

  @override
  $SfIdCopyWith<$Res> get destinationId;
}

/// @nodoc
class __$TransferMessagingSessionBodyCopyWithImpl<$Res>
    implements _$TransferMessagingSessionBodyCopyWith<$Res> {
  __$TransferMessagingSessionBodyCopyWithImpl(this._self, this._then);

  final _TransferMessagingSessionBody _self;
  final $Res Function(_TransferMessagingSessionBody) _then;

  /// Create a copy of TransferMessagingSessionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? destinationType = null,
    Object? destinationId = null,
  }) {
    return _then(_TransferMessagingSessionBody(
      destinationType: null == destinationType
          ? _self.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinationId: null == destinationId
          ? _self.destinationId
          : destinationId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of TransferMessagingSessionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get destinationId {
    return $SfIdCopyWith<$Res>(_self.destinationId, (value) {
      return _then(_self.copyWith(destinationId: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_status_options.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PresenceStatusOptions {
  List<PresenceStatus> get statusOptions;

  /// Create a copy of PresenceStatusOptions
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PresenceStatusOptionsCopyWith<PresenceStatusOptions> get copyWith =>
      _$PresenceStatusOptionsCopyWithImpl<PresenceStatusOptions>(
          this as PresenceStatusOptions, _$identity);

  /// Serializes this PresenceStatusOptions to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PresenceStatusOptions &&
            const DeepCollectionEquality()
                .equals(other.statusOptions, statusOptions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(statusOptions));

  @override
  String toString() {
    return 'PresenceStatusOptions(statusOptions: $statusOptions)';
  }
}

/// @nodoc
abstract mixin class $PresenceStatusOptionsCopyWith<$Res> {
  factory $PresenceStatusOptionsCopyWith(PresenceStatusOptions value,
          $Res Function(PresenceStatusOptions) _then) =
      _$PresenceStatusOptionsCopyWithImpl;
  @useResult
  $Res call({List<PresenceStatus> statusOptions});
}

/// @nodoc
class _$PresenceStatusOptionsCopyWithImpl<$Res>
    implements $PresenceStatusOptionsCopyWith<$Res> {
  _$PresenceStatusOptionsCopyWithImpl(this._self, this._then);

  final PresenceStatusOptions _self;
  final $Res Function(PresenceStatusOptions) _then;

  /// Create a copy of PresenceStatusOptions
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusOptions = null,
  }) {
    return _then(_self.copyWith(
      statusOptions: null == statusOptions
          ? _self.statusOptions
          : statusOptions // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>,
    ));
  }
}

/// Adds pattern-matching-related methods to [PresenceStatusOptions].
extension PresenceStatusOptionsPatterns on PresenceStatusOptions {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PresenceStatusOptions value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusOptions() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PresenceStatusOptions value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusOptions():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PresenceStatusOptions value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusOptions() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<PresenceStatus> statusOptions)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusOptions() when $default != null:
        return $default(_that.statusOptions);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<PresenceStatus> statusOptions) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusOptions():
        return $default(_that.statusOptions);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<PresenceStatus> statusOptions)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatusOptions() when $default != null:
        return $default(_that.statusOptions);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PresenceStatusOptions implements PresenceStatusOptions {
  const _PresenceStatusOptions(
      {required final List<PresenceStatus> statusOptions})
      : _statusOptions = statusOptions;
  factory _PresenceStatusOptions.fromJson(Map<String, dynamic> json) =>
      _$PresenceStatusOptionsFromJson(json);

  final List<PresenceStatus> _statusOptions;
  @override
  List<PresenceStatus> get statusOptions {
    if (_statusOptions is EqualUnmodifiableListView) return _statusOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_statusOptions);
  }

  /// Create a copy of PresenceStatusOptions
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PresenceStatusOptionsCopyWith<_PresenceStatusOptions> get copyWith =>
      __$PresenceStatusOptionsCopyWithImpl<_PresenceStatusOptions>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PresenceStatusOptionsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PresenceStatusOptions &&
            const DeepCollectionEquality()
                .equals(other._statusOptions, _statusOptions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_statusOptions));

  @override
  String toString() {
    return 'PresenceStatusOptions(statusOptions: $statusOptions)';
  }
}

/// @nodoc
abstract mixin class _$PresenceStatusOptionsCopyWith<$Res>
    implements $PresenceStatusOptionsCopyWith<$Res> {
  factory _$PresenceStatusOptionsCopyWith(_PresenceStatusOptions value,
          $Res Function(_PresenceStatusOptions) _then) =
      __$PresenceStatusOptionsCopyWithImpl;
  @override
  @useResult
  $Res call({List<PresenceStatus> statusOptions});
}

/// @nodoc
class __$PresenceStatusOptionsCopyWithImpl<$Res>
    implements _$PresenceStatusOptionsCopyWith<$Res> {
  __$PresenceStatusOptionsCopyWithImpl(this._self, this._then);

  final _PresenceStatusOptions _self;
  final $Res Function(_PresenceStatusOptions) _then;

  /// Create a copy of PresenceStatusOptions
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? statusOptions = null,
  }) {
    return _then(_PresenceStatusOptions(
      statusOptions: null == statusOptions
          ? _self._statusOptions
          : statusOptions // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SessionData {
  int get tenantId;
  String get sessionId;
  int get timeCreated;
  int get timeUpdated;
  int get expiresAt;
  List<String> get channelPlatformTypes;
  String get userId;

  /// Create a copy of SessionData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SessionDataCopyWith<SessionData> get copyWith =>
      _$SessionDataCopyWithImpl<SessionData>(this as SessionData, _$identity);

  /// Serializes this SessionData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SessionData &&
            (identical(other.tenantId, tenantId) ||
                other.tenantId == tenantId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.timeCreated, timeCreated) ||
                other.timeCreated == timeCreated) &&
            (identical(other.timeUpdated, timeUpdated) ||
                other.timeUpdated == timeUpdated) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            const DeepCollectionEquality()
                .equals(other.channelPlatformTypes, channelPlatformTypes) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      tenantId,
      sessionId,
      timeCreated,
      timeUpdated,
      expiresAt,
      const DeepCollectionEquality().hash(channelPlatformTypes),
      userId);

  @override
  String toString() {
    return 'SessionData(tenantId: $tenantId, sessionId: $sessionId, timeCreated: $timeCreated, timeUpdated: $timeUpdated, expiresAt: $expiresAt, channelPlatformTypes: $channelPlatformTypes, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class $SessionDataCopyWith<$Res> {
  factory $SessionDataCopyWith(
          SessionData value, $Res Function(SessionData) _then) =
      _$SessionDataCopyWithImpl;
  @useResult
  $Res call(
      {int tenantId,
      String sessionId,
      int timeCreated,
      int timeUpdated,
      int expiresAt,
      List<String> channelPlatformTypes,
      String userId});
}

/// @nodoc
class _$SessionDataCopyWithImpl<$Res> implements $SessionDataCopyWith<$Res> {
  _$SessionDataCopyWithImpl(this._self, this._then);

  final SessionData _self;
  final $Res Function(SessionData) _then;

  /// Create a copy of SessionData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tenantId = null,
    Object? sessionId = null,
    Object? timeCreated = null,
    Object? timeUpdated = null,
    Object? expiresAt = null,
    Object? channelPlatformTypes = null,
    Object? userId = null,
  }) {
    return _then(_self.copyWith(
      tenantId: null == tenantId
          ? _self.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      timeCreated: null == timeCreated
          ? _self.timeCreated
          : timeCreated // ignore: cast_nullable_to_non_nullable
              as int,
      timeUpdated: null == timeUpdated
          ? _self.timeUpdated
          : timeUpdated // ignore: cast_nullable_to_non_nullable
              as int,
      expiresAt: null == expiresAt
          ? _self.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int,
      channelPlatformTypes: null == channelPlatformTypes
          ? _self.channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [SessionData].
extension SessionDataPatterns on SessionData {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SessionData value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SessionData() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SessionData value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionData():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SessionData value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionData() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int tenantId,
            String sessionId,
            int timeCreated,
            int timeUpdated,
            int expiresAt,
            List<String> channelPlatformTypes,
            String userId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SessionData() when $default != null:
        return $default(
            _that.tenantId,
            _that.sessionId,
            _that.timeCreated,
            _that.timeUpdated,
            _that.expiresAt,
            _that.channelPlatformTypes,
            _that.userId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int tenantId,
            String sessionId,
            int timeCreated,
            int timeUpdated,
            int expiresAt,
            List<String> channelPlatformTypes,
            String userId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionData():
        return $default(
            _that.tenantId,
            _that.sessionId,
            _that.timeCreated,
            _that.timeUpdated,
            _that.expiresAt,
            _that.channelPlatformTypes,
            _that.userId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int tenantId,
            String sessionId,
            int timeCreated,
            int timeUpdated,
            int expiresAt,
            List<String> channelPlatformTypes,
            String userId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionData() when $default != null:
        return $default(
            _that.tenantId,
            _that.sessionId,
            _that.timeCreated,
            _that.timeUpdated,
            _that.expiresAt,
            _that.channelPlatformTypes,
            _that.userId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SessionData implements SessionData {
  const _SessionData(
      {required this.tenantId,
      required this.sessionId,
      required this.timeCreated,
      required this.timeUpdated,
      required this.expiresAt,
      required final List<String> channelPlatformTypes,
      required this.userId})
      : _channelPlatformTypes = channelPlatformTypes;
  factory _SessionData.fromJson(Map<String, dynamic> json) =>
      _$SessionDataFromJson(json);

  @override
  final int tenantId;
  @override
  final String sessionId;
  @override
  final int timeCreated;
  @override
  final int timeUpdated;
  @override
  final int expiresAt;
  final List<String> _channelPlatformTypes;
  @override
  List<String> get channelPlatformTypes {
    if (_channelPlatformTypes is EqualUnmodifiableListView)
      return _channelPlatformTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channelPlatformTypes);
  }

  @override
  final String userId;

  /// Create a copy of SessionData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SessionDataCopyWith<_SessionData> get copyWith =>
      __$SessionDataCopyWithImpl<_SessionData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SessionDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SessionData &&
            (identical(other.tenantId, tenantId) ||
                other.tenantId == tenantId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.timeCreated, timeCreated) ||
                other.timeCreated == timeCreated) &&
            (identical(other.timeUpdated, timeUpdated) ||
                other.timeUpdated == timeUpdated) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            const DeepCollectionEquality()
                .equals(other._channelPlatformTypes, _channelPlatformTypes) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      tenantId,
      sessionId,
      timeCreated,
      timeUpdated,
      expiresAt,
      const DeepCollectionEquality().hash(_channelPlatformTypes),
      userId);

  @override
  String toString() {
    return 'SessionData(tenantId: $tenantId, sessionId: $sessionId, timeCreated: $timeCreated, timeUpdated: $timeUpdated, expiresAt: $expiresAt, channelPlatformTypes: $channelPlatformTypes, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class _$SessionDataCopyWith<$Res>
    implements $SessionDataCopyWith<$Res> {
  factory _$SessionDataCopyWith(
          _SessionData value, $Res Function(_SessionData) _then) =
      __$SessionDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int tenantId,
      String sessionId,
      int timeCreated,
      int timeUpdated,
      int expiresAt,
      List<String> channelPlatformTypes,
      String userId});
}

/// @nodoc
class __$SessionDataCopyWithImpl<$Res> implements _$SessionDataCopyWith<$Res> {
  __$SessionDataCopyWithImpl(this._self, this._then);

  final _SessionData _self;
  final $Res Function(_SessionData) _then;

  /// Create a copy of SessionData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? tenantId = null,
    Object? sessionId = null,
    Object? timeCreated = null,
    Object? timeUpdated = null,
    Object? expiresAt = null,
    Object? channelPlatformTypes = null,
    Object? userId = null,
  }) {
    return _then(_SessionData(
      tenantId: null == tenantId
          ? _self.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      timeCreated: null == timeCreated
          ? _self.timeCreated
          : timeCreated // ignore: cast_nullable_to_non_nullable
              as int,
      timeUpdated: null == timeUpdated
          ? _self.timeUpdated
          : timeUpdated // ignore: cast_nullable_to_non_nullable
              as int,
      expiresAt: null == expiresAt
          ? _self.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int,
      channelPlatformTypes: null == channelPlatformTypes
          ? _self._channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

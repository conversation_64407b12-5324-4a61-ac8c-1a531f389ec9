// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_token_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesforceTokenResponse {
  String? get accessToken;
  int? get statusCode;
  int? get expiresAt;

  /// Create a copy of SalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SalesforceTokenResponseCopyWith<SalesforceTokenResponse> get copyWith =>
      _$SalesforceTokenResponseCopyWithImpl<SalesforceTokenResponse>(
          this as SalesforceTokenResponse, _$identity);

  /// Serializes this SalesforceTokenResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SalesforceTokenResponse &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, accessToken, statusCode, expiresAt);

  @override
  String toString() {
    return 'SalesforceTokenResponse(accessToken: $accessToken, statusCode: $statusCode, expiresAt: $expiresAt)';
  }
}

/// @nodoc
abstract mixin class $SalesforceTokenResponseCopyWith<$Res> {
  factory $SalesforceTokenResponseCopyWith(SalesforceTokenResponse value,
          $Res Function(SalesforceTokenResponse) _then) =
      _$SalesforceTokenResponseCopyWithImpl;
  @useResult
  $Res call({String? accessToken, int? statusCode, int? expiresAt});
}

/// @nodoc
class _$SalesforceTokenResponseCopyWithImpl<$Res>
    implements $SalesforceTokenResponseCopyWith<$Res> {
  _$SalesforceTokenResponseCopyWithImpl(this._self, this._then);

  final SalesforceTokenResponse _self;
  final $Res Function(SalesforceTokenResponse) _then;

  /// Create a copy of SalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? statusCode = freezed,
    Object? expiresAt = freezed,
  }) {
    return _then(_self.copyWith(
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _self.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      expiresAt: freezed == expiresAt
          ? _self.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SalesforceTokenResponse].
extension SalesforceTokenResponsePatterns on SalesforceTokenResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SalesforceTokenResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SalesforceTokenResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SalesforceTokenResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? accessToken, int? statusCode, int? expiresAt)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenResponse() when $default != null:
        return $default(_that.accessToken, _that.statusCode, _that.expiresAt);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? accessToken, int? statusCode, int? expiresAt)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenResponse():
        return $default(_that.accessToken, _that.statusCode, _that.expiresAt);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? accessToken, int? statusCode, int? expiresAt)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceTokenResponse() when $default != null:
        return $default(_that.accessToken, _that.statusCode, _that.expiresAt);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SalesforceTokenResponse implements SalesforceTokenResponse {
  const _SalesforceTokenResponse(
      {this.accessToken, this.statusCode, this.expiresAt});
  factory _SalesforceTokenResponse.fromJson(Map<String, dynamic> json) =>
      _$SalesforceTokenResponseFromJson(json);

  @override
  final String? accessToken;
  @override
  final int? statusCode;
  @override
  final int? expiresAt;

  /// Create a copy of SalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SalesforceTokenResponseCopyWith<_SalesforceTokenResponse> get copyWith =>
      __$SalesforceTokenResponseCopyWithImpl<_SalesforceTokenResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SalesforceTokenResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SalesforceTokenResponse &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, accessToken, statusCode, expiresAt);

  @override
  String toString() {
    return 'SalesforceTokenResponse(accessToken: $accessToken, statusCode: $statusCode, expiresAt: $expiresAt)';
  }
}

/// @nodoc
abstract mixin class _$SalesforceTokenResponseCopyWith<$Res>
    implements $SalesforceTokenResponseCopyWith<$Res> {
  factory _$SalesforceTokenResponseCopyWith(_SalesforceTokenResponse value,
          $Res Function(_SalesforceTokenResponse) _then) =
      __$SalesforceTokenResponseCopyWithImpl;
  @override
  @useResult
  $Res call({String? accessToken, int? statusCode, int? expiresAt});
}

/// @nodoc
class __$SalesforceTokenResponseCopyWithImpl<$Res>
    implements _$SalesforceTokenResponseCopyWith<$Res> {
  __$SalesforceTokenResponseCopyWithImpl(this._self, this._then);

  final _SalesforceTokenResponse _self;
  final $Res Function(_SalesforceTokenResponse) _then;

  /// Create a copy of SalesforceTokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? accessToken = freezed,
    Object? statusCode = freezed,
    Object? expiresAt = freezed,
  }) {
    return _then(_SalesforceTokenResponse(
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _self.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      expiresAt: freezed == expiresAt
          ? _self.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on

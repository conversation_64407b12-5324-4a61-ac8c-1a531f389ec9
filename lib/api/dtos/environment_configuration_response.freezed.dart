// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'environment_configuration_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EnvironmentConfigurationResponse {
  Map<String, dynamic> get config;

  /// Create a copy of EnvironmentConfigurationResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EnvironmentConfigurationResponseCopyWith<EnvironmentConfigurationResponse>
      get copyWith => _$EnvironmentConfigurationResponseCopyWithImpl<
              EnvironmentConfigurationResponse>(
          this as EnvironmentConfigurationResponse, _$identity);

  /// Serializes this EnvironmentConfigurationResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EnvironmentConfigurationResponse &&
            const DeepCollectionEquality().equals(other.config, config));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(config));

  @override
  String toString() {
    return 'EnvironmentConfigurationResponse(config: $config)';
  }
}

/// @nodoc
abstract mixin class $EnvironmentConfigurationResponseCopyWith<$Res> {
  factory $EnvironmentConfigurationResponseCopyWith(
          EnvironmentConfigurationResponse value,
          $Res Function(EnvironmentConfigurationResponse) _then) =
      _$EnvironmentConfigurationResponseCopyWithImpl;
  @useResult
  $Res call({Map<String, dynamic> config});
}

/// @nodoc
class _$EnvironmentConfigurationResponseCopyWithImpl<$Res>
    implements $EnvironmentConfigurationResponseCopyWith<$Res> {
  _$EnvironmentConfigurationResponseCopyWithImpl(this._self, this._then);

  final EnvironmentConfigurationResponse _self;
  final $Res Function(EnvironmentConfigurationResponse) _then;

  /// Create a copy of EnvironmentConfigurationResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = null,
  }) {
    return _then(_self.copyWith(
      config: null == config
          ? _self.config
          : config // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// Adds pattern-matching-related methods to [EnvironmentConfigurationResponse].
extension EnvironmentConfigurationResponsePatterns
    on EnvironmentConfigurationResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_EnvironmentConfigurationResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _EnvironmentConfigurationResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_EnvironmentConfigurationResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EnvironmentConfigurationResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_EnvironmentConfigurationResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EnvironmentConfigurationResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(Map<String, dynamic> config)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _EnvironmentConfigurationResponse() when $default != null:
        return $default(_that.config);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(Map<String, dynamic> config) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EnvironmentConfigurationResponse():
        return $default(_that.config);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(Map<String, dynamic> config)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EnvironmentConfigurationResponse() when $default != null:
        return $default(_that.config);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _EnvironmentConfigurationResponse
    implements EnvironmentConfigurationResponse {
  const _EnvironmentConfigurationResponse(
      {required final Map<String, dynamic> config})
      : _config = config;
  factory _EnvironmentConfigurationResponse.fromJson(
          Map<String, dynamic> json) =>
      _$EnvironmentConfigurationResponseFromJson(json);

  final Map<String, dynamic> _config;
  @override
  Map<String, dynamic> get config {
    if (_config is EqualUnmodifiableMapView) return _config;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_config);
  }

  /// Create a copy of EnvironmentConfigurationResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EnvironmentConfigurationResponseCopyWith<_EnvironmentConfigurationResponse>
      get copyWith => __$EnvironmentConfigurationResponseCopyWithImpl<
          _EnvironmentConfigurationResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EnvironmentConfigurationResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EnvironmentConfigurationResponse &&
            const DeepCollectionEquality().equals(other._config, _config));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_config));

  @override
  String toString() {
    return 'EnvironmentConfigurationResponse(config: $config)';
  }
}

/// @nodoc
abstract mixin class _$EnvironmentConfigurationResponseCopyWith<$Res>
    implements $EnvironmentConfigurationResponseCopyWith<$Res> {
  factory _$EnvironmentConfigurationResponseCopyWith(
          _EnvironmentConfigurationResponse value,
          $Res Function(_EnvironmentConfigurationResponse) _then) =
      __$EnvironmentConfigurationResponseCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, dynamic> config});
}

/// @nodoc
class __$EnvironmentConfigurationResponseCopyWithImpl<$Res>
    implements _$EnvironmentConfigurationResponseCopyWith<$Res> {
  __$EnvironmentConfigurationResponseCopyWithImpl(this._self, this._then);

  final _EnvironmentConfigurationResponse _self;
  final $Res Function(_EnvironmentConfigurationResponse) _then;

  /// Create a copy of EnvironmentConfigurationResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? config = null,
  }) {
    return _then(_EnvironmentConfigurationResponse(
      config: null == config
          ? _self._config
          : config // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on

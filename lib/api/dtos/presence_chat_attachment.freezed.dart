// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_chat_attachment.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PresenceChatAttachment {
  String get contentVersionId;

  /// Create a copy of PresenceChatAttachment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PresenceChatAttachmentCopyWith<PresenceChatAttachment> get copyWith =>
      _$PresenceChatAttachmentCopyWithImpl<PresenceChatAttachment>(
          this as PresenceChatAttachment, _$identity);

  /// Serializes this PresenceChatAttachment to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PresenceChatAttachment &&
            (identical(other.contentVersionId, contentVersionId) ||
                other.contentVersionId == contentVersionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contentVersionId);

  @override
  String toString() {
    return 'PresenceChatAttachment(contentVersionId: $contentVersionId)';
  }
}

/// @nodoc
abstract mixin class $PresenceChatAttachmentCopyWith<$Res> {
  factory $PresenceChatAttachmentCopyWith(PresenceChatAttachment value,
          $Res Function(PresenceChatAttachment) _then) =
      _$PresenceChatAttachmentCopyWithImpl;
  @useResult
  $Res call({String contentVersionId});
}

/// @nodoc
class _$PresenceChatAttachmentCopyWithImpl<$Res>
    implements $PresenceChatAttachmentCopyWith<$Res> {
  _$PresenceChatAttachmentCopyWithImpl(this._self, this._then);

  final PresenceChatAttachment _self;
  final $Res Function(PresenceChatAttachment) _then;

  /// Create a copy of PresenceChatAttachment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contentVersionId = null,
  }) {
    return _then(_self.copyWith(
      contentVersionId: null == contentVersionId
          ? _self.contentVersionId
          : contentVersionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [PresenceChatAttachment].
extension PresenceChatAttachmentPatterns on PresenceChatAttachment {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PresenceChatAttachment value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceChatAttachment() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PresenceChatAttachment value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatAttachment():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PresenceChatAttachment value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatAttachment() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String contentVersionId)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceChatAttachment() when $default != null:
        return $default(_that.contentVersionId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String contentVersionId) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatAttachment():
        return $default(_that.contentVersionId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String contentVersionId)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatAttachment() when $default != null:
        return $default(_that.contentVersionId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PresenceChatAttachment implements PresenceChatAttachment {
  const _PresenceChatAttachment({required this.contentVersionId});
  factory _PresenceChatAttachment.fromJson(Map<String, dynamic> json) =>
      _$PresenceChatAttachmentFromJson(json);

  @override
  final String contentVersionId;

  /// Create a copy of PresenceChatAttachment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PresenceChatAttachmentCopyWith<_PresenceChatAttachment> get copyWith =>
      __$PresenceChatAttachmentCopyWithImpl<_PresenceChatAttachment>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PresenceChatAttachmentToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PresenceChatAttachment &&
            (identical(other.contentVersionId, contentVersionId) ||
                other.contentVersionId == contentVersionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contentVersionId);

  @override
  String toString() {
    return 'PresenceChatAttachment(contentVersionId: $contentVersionId)';
  }
}

/// @nodoc
abstract mixin class _$PresenceChatAttachmentCopyWith<$Res>
    implements $PresenceChatAttachmentCopyWith<$Res> {
  factory _$PresenceChatAttachmentCopyWith(_PresenceChatAttachment value,
          $Res Function(_PresenceChatAttachment) _then) =
      __$PresenceChatAttachmentCopyWithImpl;
  @override
  @useResult
  $Res call({String contentVersionId});
}

/// @nodoc
class __$PresenceChatAttachmentCopyWithImpl<$Res>
    implements _$PresenceChatAttachmentCopyWith<$Res> {
  __$PresenceChatAttachmentCopyWithImpl(this._self, this._then);

  final _PresenceChatAttachment _self;
  final $Res Function(_PresenceChatAttachment) _then;

  /// Create a copy of PresenceChatAttachment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contentVersionId = null,
  }) {
    return _then(_PresenceChatAttachment(
      contentVersionId: null == contentVersionId
          ? _self.contentVersionId
          : contentVersionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

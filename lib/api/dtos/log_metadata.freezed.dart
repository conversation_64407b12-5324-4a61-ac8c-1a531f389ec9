// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_metadata.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LogMetadata {
  String? get buildNumber;
  String? get versionNumber;
  String? get osVersion;
  @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson)
  OSName? get osName;
  String? get manufacturer;
  String? get model;
  @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
  DeviceType? get deviceType;
  String? get carrier;
  int? get batteryLevel;
  String? get userId;
  String? get sessionId;
  bool? get debugMode;
  bool? get profileMode;
  bool? get releaseMode;
  String? get env;

  /// Create a copy of LogMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LogMetadataCopyWith<LogMetadata> get copyWith =>
      _$LogMetadataCopyWithImpl<LogMetadata>(this as LogMetadata, _$identity);

  /// Serializes this LogMetadata to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LogMetadata &&
            (identical(other.buildNumber, buildNumber) ||
                other.buildNumber == buildNumber) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.osVersion, osVersion) ||
                other.osVersion == osVersion) &&
            (identical(other.osName, osName) || other.osName == osName) &&
            (identical(other.manufacturer, manufacturer) ||
                other.manufacturer == manufacturer) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.carrier, carrier) || other.carrier == carrier) &&
            (identical(other.batteryLevel, batteryLevel) ||
                other.batteryLevel == batteryLevel) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.debugMode, debugMode) ||
                other.debugMode == debugMode) &&
            (identical(other.profileMode, profileMode) ||
                other.profileMode == profileMode) &&
            (identical(other.releaseMode, releaseMode) ||
                other.releaseMode == releaseMode) &&
            (identical(other.env, env) || other.env == env));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      buildNumber,
      versionNumber,
      osVersion,
      osName,
      manufacturer,
      model,
      deviceType,
      carrier,
      batteryLevel,
      userId,
      sessionId,
      debugMode,
      profileMode,
      releaseMode,
      env);

  @override
  String toString() {
    return 'LogMetadata(buildNumber: $buildNumber, versionNumber: $versionNumber, osVersion: $osVersion, osName: $osName, manufacturer: $manufacturer, model: $model, deviceType: $deviceType, carrier: $carrier, batteryLevel: $batteryLevel, userId: $userId, sessionId: $sessionId, debugMode: $debugMode, profileMode: $profileMode, releaseMode: $releaseMode, env: $env)';
  }
}

/// @nodoc
abstract mixin class $LogMetadataCopyWith<$Res> {
  factory $LogMetadataCopyWith(
          LogMetadata value, $Res Function(LogMetadata) _then) =
      _$LogMetadataCopyWithImpl;
  @useResult
  $Res call(
      {String? buildNumber,
      String? versionNumber,
      String? osVersion,
      @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson) OSName? osName,
      String? manufacturer,
      String? model,
      @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
      DeviceType? deviceType,
      String? carrier,
      int? batteryLevel,
      String? userId,
      String? sessionId,
      bool? debugMode,
      bool? profileMode,
      bool? releaseMode,
      String? env});
}

/// @nodoc
class _$LogMetadataCopyWithImpl<$Res> implements $LogMetadataCopyWith<$Res> {
  _$LogMetadataCopyWithImpl(this._self, this._then);

  final LogMetadata _self;
  final $Res Function(LogMetadata) _then;

  /// Create a copy of LogMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buildNumber = freezed,
    Object? versionNumber = freezed,
    Object? osVersion = freezed,
    Object? osName = freezed,
    Object? manufacturer = freezed,
    Object? model = freezed,
    Object? deviceType = freezed,
    Object? carrier = freezed,
    Object? batteryLevel = freezed,
    Object? userId = freezed,
    Object? sessionId = freezed,
    Object? debugMode = freezed,
    Object? profileMode = freezed,
    Object? releaseMode = freezed,
    Object? env = freezed,
  }) {
    return _then(_self.copyWith(
      buildNumber: freezed == buildNumber
          ? _self.buildNumber
          : buildNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _self.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      osVersion: freezed == osVersion
          ? _self.osVersion
          : osVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      osName: freezed == osName
          ? _self.osName
          : osName // ignore: cast_nullable_to_non_nullable
              as OSName?,
      manufacturer: freezed == manufacturer
          ? _self.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      model: freezed == model
          ? _self.model
          : model // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _self.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as DeviceType?,
      carrier: freezed == carrier
          ? _self.carrier
          : carrier // ignore: cast_nullable_to_non_nullable
              as String?,
      batteryLevel: freezed == batteryLevel
          ? _self.batteryLevel
          : batteryLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      debugMode: freezed == debugMode
          ? _self.debugMode
          : debugMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      profileMode: freezed == profileMode
          ? _self.profileMode
          : profileMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      releaseMode: freezed == releaseMode
          ? _self.releaseMode
          : releaseMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      env: freezed == env
          ? _self.env
          : env // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [LogMetadata].
extension LogMetadataPatterns on LogMetadata {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_LogMetadata value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogMetadata() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_LogMetadata value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogMetadata():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_LogMetadata value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogMetadata() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? buildNumber,
            String? versionNumber,
            String? osVersion,
            @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson)
            OSName? osName,
            String? manufacturer,
            String? model,
            @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
            DeviceType? deviceType,
            String? carrier,
            int? batteryLevel,
            String? userId,
            String? sessionId,
            bool? debugMode,
            bool? profileMode,
            bool? releaseMode,
            String? env)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogMetadata() when $default != null:
        return $default(
            _that.buildNumber,
            _that.versionNumber,
            _that.osVersion,
            _that.osName,
            _that.manufacturer,
            _that.model,
            _that.deviceType,
            _that.carrier,
            _that.batteryLevel,
            _that.userId,
            _that.sessionId,
            _that.debugMode,
            _that.profileMode,
            _that.releaseMode,
            _that.env);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? buildNumber,
            String? versionNumber,
            String? osVersion,
            @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson)
            OSName? osName,
            String? manufacturer,
            String? model,
            @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
            DeviceType? deviceType,
            String? carrier,
            int? batteryLevel,
            String? userId,
            String? sessionId,
            bool? debugMode,
            bool? profileMode,
            bool? releaseMode,
            String? env)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogMetadata():
        return $default(
            _that.buildNumber,
            _that.versionNumber,
            _that.osVersion,
            _that.osName,
            _that.manufacturer,
            _that.model,
            _that.deviceType,
            _that.carrier,
            _that.batteryLevel,
            _that.userId,
            _that.sessionId,
            _that.debugMode,
            _that.profileMode,
            _that.releaseMode,
            _that.env);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? buildNumber,
            String? versionNumber,
            String? osVersion,
            @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson)
            OSName? osName,
            String? manufacturer,
            String? model,
            @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
            DeviceType? deviceType,
            String? carrier,
            int? batteryLevel,
            String? userId,
            String? sessionId,
            bool? debugMode,
            bool? profileMode,
            bool? releaseMode,
            String? env)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogMetadata() when $default != null:
        return $default(
            _that.buildNumber,
            _that.versionNumber,
            _that.osVersion,
            _that.osName,
            _that.manufacturer,
            _that.model,
            _that.deviceType,
            _that.carrier,
            _that.batteryLevel,
            _that.userId,
            _that.sessionId,
            _that.debugMode,
            _that.profileMode,
            _that.releaseMode,
            _that.env);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _LogMetadata extends LogMetadata {
  const _LogMetadata(
      {this.buildNumber,
      this.versionNumber,
      this.osVersion,
      @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson) this.osName,
      this.manufacturer,
      this.model,
      @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
      this.deviceType,
      this.carrier,
      this.batteryLevel,
      this.userId,
      this.sessionId,
      this.debugMode,
      this.profileMode,
      this.releaseMode,
      this.env})
      : super._();
  factory _LogMetadata.fromJson(Map<String, dynamic> json) =>
      _$LogMetadataFromJson(json);

  @override
  final String? buildNumber;
  @override
  final String? versionNumber;
  @override
  final String? osVersion;
  @override
  @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson)
  final OSName? osName;
  @override
  final String? manufacturer;
  @override
  final String? model;
  @override
  @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
  final DeviceType? deviceType;
  @override
  final String? carrier;
  @override
  final int? batteryLevel;
  @override
  final String? userId;
  @override
  final String? sessionId;
  @override
  final bool? debugMode;
  @override
  final bool? profileMode;
  @override
  final bool? releaseMode;
  @override
  final String? env;

  /// Create a copy of LogMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LogMetadataCopyWith<_LogMetadata> get copyWith =>
      __$LogMetadataCopyWithImpl<_LogMetadata>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LogMetadataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LogMetadata &&
            (identical(other.buildNumber, buildNumber) ||
                other.buildNumber == buildNumber) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.osVersion, osVersion) ||
                other.osVersion == osVersion) &&
            (identical(other.osName, osName) || other.osName == osName) &&
            (identical(other.manufacturer, manufacturer) ||
                other.manufacturer == manufacturer) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.carrier, carrier) || other.carrier == carrier) &&
            (identical(other.batteryLevel, batteryLevel) ||
                other.batteryLevel == batteryLevel) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.debugMode, debugMode) ||
                other.debugMode == debugMode) &&
            (identical(other.profileMode, profileMode) ||
                other.profileMode == profileMode) &&
            (identical(other.releaseMode, releaseMode) ||
                other.releaseMode == releaseMode) &&
            (identical(other.env, env) || other.env == env));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      buildNumber,
      versionNumber,
      osVersion,
      osName,
      manufacturer,
      model,
      deviceType,
      carrier,
      batteryLevel,
      userId,
      sessionId,
      debugMode,
      profileMode,
      releaseMode,
      env);

  @override
  String toString() {
    return 'LogMetadata(buildNumber: $buildNumber, versionNumber: $versionNumber, osVersion: $osVersion, osName: $osName, manufacturer: $manufacturer, model: $model, deviceType: $deviceType, carrier: $carrier, batteryLevel: $batteryLevel, userId: $userId, sessionId: $sessionId, debugMode: $debugMode, profileMode: $profileMode, releaseMode: $releaseMode, env: $env)';
  }
}

/// @nodoc
abstract mixin class _$LogMetadataCopyWith<$Res>
    implements $LogMetadataCopyWith<$Res> {
  factory _$LogMetadataCopyWith(
          _LogMetadata value, $Res Function(_LogMetadata) _then) =
      __$LogMetadataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? buildNumber,
      String? versionNumber,
      String? osVersion,
      @JsonKey(fromJson: _osNameFromJson, toJson: _osNameToJson) OSName? osName,
      String? manufacturer,
      String? model,
      @JsonKey(fromJson: _deviceTypeFromJson, toJson: _deviceTypeToJson)
      DeviceType? deviceType,
      String? carrier,
      int? batteryLevel,
      String? userId,
      String? sessionId,
      bool? debugMode,
      bool? profileMode,
      bool? releaseMode,
      String? env});
}

/// @nodoc
class __$LogMetadataCopyWithImpl<$Res> implements _$LogMetadataCopyWith<$Res> {
  __$LogMetadataCopyWithImpl(this._self, this._then);

  final _LogMetadata _self;
  final $Res Function(_LogMetadata) _then;

  /// Create a copy of LogMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? buildNumber = freezed,
    Object? versionNumber = freezed,
    Object? osVersion = freezed,
    Object? osName = freezed,
    Object? manufacturer = freezed,
    Object? model = freezed,
    Object? deviceType = freezed,
    Object? carrier = freezed,
    Object? batteryLevel = freezed,
    Object? userId = freezed,
    Object? sessionId = freezed,
    Object? debugMode = freezed,
    Object? profileMode = freezed,
    Object? releaseMode = freezed,
    Object? env = freezed,
  }) {
    return _then(_LogMetadata(
      buildNumber: freezed == buildNumber
          ? _self.buildNumber
          : buildNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _self.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      osVersion: freezed == osVersion
          ? _self.osVersion
          : osVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      osName: freezed == osName
          ? _self.osName
          : osName // ignore: cast_nullable_to_non_nullable
              as OSName?,
      manufacturer: freezed == manufacturer
          ? _self.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      model: freezed == model
          ? _self.model
          : model // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _self.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as DeviceType?,
      carrier: freezed == carrier
          ? _self.carrier
          : carrier // ignore: cast_nullable_to_non_nullable
              as String?,
      batteryLevel: freezed == batteryLevel
          ? _self.batteryLevel
          : batteryLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      debugMode: freezed == debugMode
          ? _self.debugMode
          : debugMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      profileMode: freezed == profileMode
          ? _self.profileMode
          : profileMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      releaseMode: freezed == releaseMode
          ? _self.releaseMode
          : releaseMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      env: freezed == env
          ? _self.env
          : env // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

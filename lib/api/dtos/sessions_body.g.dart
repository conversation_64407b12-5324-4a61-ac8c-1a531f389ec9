// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sessions_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SessionsBody _$SessionsBodyFromJson(Map json) => $checkedCreate(
      '_SessionsBody',
      json,
      ($checkedConvert) {
        final val = _SessionsBody(
          userId: $checkedConvert('userId', (v) => v as String),
          instanceUrl: $checkedConvert('instanceUrl', (v) => v as String),
          deviceToken: $checkedConvert('deviceToken', (v) => v as String?),
          accessToken: $checkedConvert('accessToken', (v) => v as String),
          channelPlatformTypes: $checkedConvert('channelPlatformTypes',
              (v) => (v as List<dynamic>).map((e) => e as String).toList()),
          locale: $checkedConvert('localeCode', (v) => v as String),
          deviceType: $checkedConvert('deviceType', (v) => v as String?),
          expirationSeconds:
              $checkedConvert('expirationSeconds', (v) => (v as num?)?.toInt()),
          metadata: $checkedConvert(
              'metadata',
              (v) => (v as Map?)?.map(
                    (k, e) => MapEntry(k as String, e),
                  )),
        );
        return val;
      },
      fieldKeyMap: const {'locale': 'localeCode'},
    );

Map<String, dynamic> _$SessionsBodyToJson(_SessionsBody instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'instanceUrl': instance.instanceUrl,
      if (instance.deviceToken case final value?) 'deviceToken': value,
      'accessToken': instance.accessToken,
      'channelPlatformTypes': instance.channelPlatformTypes,
      'localeCode': instance.locale,
      if (instance.deviceType case final value?) 'deviceType': value,
      if (instance.expirationSeconds case final value?)
        'expirationSeconds': value,
      if (instance.metadata case final value?) 'metadata': value,
    };

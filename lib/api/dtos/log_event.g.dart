// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LogEvent _$LogEventFromJson(Map json) => $checkedCreate(
      '_LogEvent',
      json,
      ($checkedConvert) {
        final val = _LogEvent(
          timestamp: $checkedConvert('timestamp', (v) => (v as num).toInt()),
          packageName: $checkedConvert('packageName', (v) => v as String),
          level: $checkedConvert('level', (v) => v as String),
          metadata: $checkedConvert('metadata',
              (v) => LogMetadata.fromJson(Map<String, dynamic>.from(v as Map))),
          message: $checkedConvert('message', (v) => v as String),
        );
        return val;
      },
    );

Map<String, dynamic> _$LogEventToJson(_LogEvent instance) => <String, dynamic>{
      'timestamp': instance.timestamp,
      'packageName': instance.packageName,
      'level': instance.level,
      'metadata': instance.metadata.toJson(),
      'message': instance.message,
    };

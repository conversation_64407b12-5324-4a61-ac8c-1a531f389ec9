// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_destination.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransferDestination _$TransferDestinationFromJson(Map json) => $checkedCreate(
      '_TransferDestination',
      json,
      ($checkedConvert) {
        final val = _TransferDestination(
          id: $checkedConvert(
              'id', (v) => const ParseSfIdConverter().fromJson(v)),
          label: $checkedConvert('label', (v) => v as String),
          smallPhotoUrl:
              $checkedConvert('smallPhotoUrl', (v) => v as String? ?? null),
          estimatedWait: $checkedConvert(
              'estimatedWait', (v) => (v as num?)?.toInt() ?? null),
        );
        return val;
      },
    );

Map<String, dynamic> _$TransferDestinationToJson(
        _TransferDestination instance) =>
    <String, dynamic>{
      if (const ParseSfIdConverter().toJson(instance.id) case final value?)
        'id': value,
      'label': instance.label,
      if (instance.smallPhotoUrl case final value?) 'smallPhotoUrl': value,
      if (instance.estimatedWait case final value?) 'estimatedWait': value,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_sessions_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ActiveSessionsResponse {
  List<SessionData>? get sessions;
  String get key;

  /// Create a copy of ActiveSessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ActiveSessionsResponseCopyWith<ActiveSessionsResponse> get copyWith =>
      _$ActiveSessionsResponseCopyWithImpl<ActiveSessionsResponse>(
          this as ActiveSessionsResponse, _$identity);

  /// Serializes this ActiveSessionsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ActiveSessionsResponse &&
            const DeepCollectionEquality().equals(other.sessions, sessions) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(sessions), key);

  @override
  String toString() {
    return 'ActiveSessionsResponse(sessions: $sessions, key: $key)';
  }
}

/// @nodoc
abstract mixin class $ActiveSessionsResponseCopyWith<$Res> {
  factory $ActiveSessionsResponseCopyWith(ActiveSessionsResponse value,
          $Res Function(ActiveSessionsResponse) _then) =
      _$ActiveSessionsResponseCopyWithImpl;
  @useResult
  $Res call({List<SessionData>? sessions, String key});
}

/// @nodoc
class _$ActiveSessionsResponseCopyWithImpl<$Res>
    implements $ActiveSessionsResponseCopyWith<$Res> {
  _$ActiveSessionsResponseCopyWithImpl(this._self, this._then);

  final ActiveSessionsResponse _self;
  final $Res Function(ActiveSessionsResponse) _then;

  /// Create a copy of ActiveSessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessions = freezed,
    Object? key = null,
  }) {
    return _then(_self.copyWith(
      sessions: freezed == sessions
          ? _self.sessions
          : sessions // ignore: cast_nullable_to_non_nullable
              as List<SessionData>?,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ActiveSessionsResponse].
extension ActiveSessionsResponsePatterns on ActiveSessionsResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ActiveSessionsResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ActiveSessionsResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ActiveSessionsResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<SessionData>? sessions, String key)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsResponse() when $default != null:
        return $default(_that.sessions, _that.key);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<SessionData>? sessions, String key) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsResponse():
        return $default(_that.sessions, _that.key);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<SessionData>? sessions, String key)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActiveSessionsResponse() when $default != null:
        return $default(_that.sessions, _that.key);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ActiveSessionsResponse implements ActiveSessionsResponse {
  const _ActiveSessionsResponse(
      {final List<SessionData>? sessions, required this.key})
      : _sessions = sessions;
  factory _ActiveSessionsResponse.fromJson(Map<String, dynamic> json) =>
      _$ActiveSessionsResponseFromJson(json);

  final List<SessionData>? _sessions;
  @override
  List<SessionData>? get sessions {
    final value = _sessions;
    if (value == null) return null;
    if (_sessions is EqualUnmodifiableListView) return _sessions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String key;

  /// Create a copy of ActiveSessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ActiveSessionsResponseCopyWith<_ActiveSessionsResponse> get copyWith =>
      __$ActiveSessionsResponseCopyWithImpl<_ActiveSessionsResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ActiveSessionsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ActiveSessionsResponse &&
            const DeepCollectionEquality().equals(other._sessions, _sessions) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_sessions), key);

  @override
  String toString() {
    return 'ActiveSessionsResponse(sessions: $sessions, key: $key)';
  }
}

/// @nodoc
abstract mixin class _$ActiveSessionsResponseCopyWith<$Res>
    implements $ActiveSessionsResponseCopyWith<$Res> {
  factory _$ActiveSessionsResponseCopyWith(_ActiveSessionsResponse value,
          $Res Function(_ActiveSessionsResponse) _then) =
      __$ActiveSessionsResponseCopyWithImpl;
  @override
  @useResult
  $Res call({List<SessionData>? sessions, String key});
}

/// @nodoc
class __$ActiveSessionsResponseCopyWithImpl<$Res>
    implements _$ActiveSessionsResponseCopyWith<$Res> {
  __$ActiveSessionsResponseCopyWithImpl(this._self, this._then);

  final _ActiveSessionsResponse _self;
  final $Res Function(_ActiveSessionsResponse) _then;

  /// Create a copy of ActiveSessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sessions = freezed,
    Object? key = null,
  }) {
    return _then(_ActiveSessionsResponse(
      sessions: freezed == sessions
          ? _self._sessions
          : sessions // ignore: cast_nullable_to_non_nullable
              as List<SessionData>?,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_option.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TransferOption {
  TransferDestinationType get destinationType;
  List<TransferDestination> get destinations;

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TransferOptionCopyWith<TransferOption> get copyWith =>
      _$TransferOptionCopyWithImpl<TransferOption>(
          this as TransferOption, _$identity);

  /// Serializes this TransferOption to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TransferOption &&
            (identical(other.destinationType, destinationType) ||
                other.destinationType == destinationType) &&
            const DeepCollectionEquality()
                .equals(other.destinations, destinations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, destinationType,
      const DeepCollectionEquality().hash(destinations));

  @override
  String toString() {
    return 'TransferOption(destinationType: $destinationType, destinations: $destinations)';
  }
}

/// @nodoc
abstract mixin class $TransferOptionCopyWith<$Res> {
  factory $TransferOptionCopyWith(
          TransferOption value, $Res Function(TransferOption) _then) =
      _$TransferOptionCopyWithImpl;
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      List<TransferDestination> destinations});
}

/// @nodoc
class _$TransferOptionCopyWithImpl<$Res>
    implements $TransferOptionCopyWith<$Res> {
  _$TransferOptionCopyWithImpl(this._self, this._then);

  final TransferOption _self;
  final $Res Function(TransferOption) _then;

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destinationType = null,
    Object? destinations = null,
  }) {
    return _then(_self.copyWith(
      destinationType: null == destinationType
          ? _self.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinations: null == destinations
          ? _self.destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
    ));
  }
}

/// Adds pattern-matching-related methods to [TransferOption].
extension TransferOptionPatterns on TransferOption {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_TransferOption value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferOption() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_TransferOption value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOption():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_TransferOption value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOption() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(TransferDestinationType destinationType,
            List<TransferDestination> destinations)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferOption() when $default != null:
        return $default(_that.destinationType, _that.destinations);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(TransferDestinationType destinationType,
            List<TransferDestination> destinations)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOption():
        return $default(_that.destinationType, _that.destinations);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(TransferDestinationType destinationType,
            List<TransferDestination> destinations)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferOption() when $default != null:
        return $default(_that.destinationType, _that.destinations);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _TransferOption implements TransferOption {
  const _TransferOption(
      {required this.destinationType,
      final List<TransferDestination> destinations =
          const <TransferDestination>[]})
      : _destinations = destinations;
  factory _TransferOption.fromJson(Map<String, dynamic> json) =>
      _$TransferOptionFromJson(json);

  @override
  final TransferDestinationType destinationType;
  final List<TransferDestination> _destinations;
  @override
  @JsonKey()
  List<TransferDestination> get destinations {
    if (_destinations is EqualUnmodifiableListView) return _destinations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_destinations);
  }

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TransferOptionCopyWith<_TransferOption> get copyWith =>
      __$TransferOptionCopyWithImpl<_TransferOption>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TransferOptionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TransferOption &&
            (identical(other.destinationType, destinationType) ||
                other.destinationType == destinationType) &&
            const DeepCollectionEquality()
                .equals(other._destinations, _destinations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, destinationType,
      const DeepCollectionEquality().hash(_destinations));

  @override
  String toString() {
    return 'TransferOption(destinationType: $destinationType, destinations: $destinations)';
  }
}

/// @nodoc
abstract mixin class _$TransferOptionCopyWith<$Res>
    implements $TransferOptionCopyWith<$Res> {
  factory _$TransferOptionCopyWith(
          _TransferOption value, $Res Function(_TransferOption) _then) =
      __$TransferOptionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      List<TransferDestination> destinations});
}

/// @nodoc
class __$TransferOptionCopyWithImpl<$Res>
    implements _$TransferOptionCopyWith<$Res> {
  __$TransferOptionCopyWithImpl(this._self, this._then);

  final _TransferOption _self;
  final $Res Function(_TransferOption) _then;

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? destinationType = null,
    Object? destinations = null,
  }) {
    return _then(_TransferOption(
      destinationType: null == destinationType
          ? _self.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinations: null == destinations
          ? _self._destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
    ));
  }
}

// dart format on

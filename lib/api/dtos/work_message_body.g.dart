// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_message_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WorkMessageBody _$WorkMessageBodyFromJson(Map json) => $checkedCreate(
      '_WorkMessageBody',
      json,
      ($checkedConvert) {
        final val = _WorkMessageBody(
          messageId: $checkedConvert('messageId', (v) => v as String),
          messageBody: $checkedConvert('messageBody', (v) => v as String?),
          workTargetId: $checkedConvert(
              'workTargetId', (v) => const ParseSfIdConverter().fromJson(v)),
          attachments: $checkedConvert(
              'attachments',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => MessageAttachment.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <MessageAttachment>[]),
        );
        return val;
      },
    );

Map<String, dynamic> _$WorkMessageBodyToJson(_WorkMessageBody instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      if (instance.messageBody case final value?) 'messageBody': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.workTargetId, const ParseSfIdConverter().toJson)
          case final value?)
        'workTargetId': value,
      'attachments': instance.attachments.map((e) => e.toJson()).toList(),
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);

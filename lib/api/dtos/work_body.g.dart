// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WorkBody _$WorkBodyFromJson(Map json) => $checkedCreate(
      '_WorkBody',
      json,
      ($checkedConvert) {
        final val = _WorkBody(
          requestId: $checkedConvert('requestId', (v) => v as String),
          workId: $checkedConvert(
              'workId', (v) => const ParseSfIdConverter().from<PERSON>son(v)),
          workTargetId: $checkedConvert(
              'workTargetId', (v) => const ParseSfIdConverter().fromJson(v)),
        );
        return val;
      },
    );

Map<String, dynamic> _$WorkBodyToJson(_WorkBody instance) => <String, dynamic>{
      'requestId': instance.requestId,
      if (const ParseSfIdConverter().toJson(instance.workId) case final value?)
        'workId': value,
      if (const ParseSfIdConverter().toJson(instance.workTargetId)
          case final value?)
        'workTargetId': value,
    };

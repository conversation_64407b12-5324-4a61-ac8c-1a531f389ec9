// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_failure.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SendFailure _$SendFailureFromJson(Map json) => $checkedCreate(
      '_SendFailure',
      json,
      ($checkedConvert) {
        final val = _SendFailure(
          messageId: $checkedConvert('messageId', (v) => v as String),
          reason: $checkedConvert('reason', (v) => v as String),
        );
        return val;
      },
    );

Map<String, dynamic> _$SendFailureToJson(_SendFailure instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'reason': instance.reason,
    };

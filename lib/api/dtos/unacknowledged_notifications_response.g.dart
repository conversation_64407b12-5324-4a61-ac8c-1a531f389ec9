// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unacknowledged_notifications_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UnacknowledgedNotificationsResponse
    _$UnacknowledgedNotificationsResponseFromJson(Map json) => $checkedCreate(
          '_UnacknowledgedNotificationsResponse',
          json,
          ($checkedConvert) {
            final val = _UnacknowledgedNotificationsResponse(
              notifications: $checkedConvert(
                  'notifications',
                  (v) => (v as List<dynamic>?)
                      ?.map((e) => NotificationMessage.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList()),
              nextToken: $checkedConvert('nextToken', (v) => v as String?),
              sessionActive:
                  $checkedConvert('sessionActive', (v) => v as bool?),
            );
            return val;
          },
        );

Map<String, dynamic> _$UnacknowledgedNotificationsResponseToJson(
        _UnacknowledgedNotificationsResponse instance) =>
    <String, dynamic>{
      if (instance.notifications?.map((e) => e.toJson()).toList()
          case final value?)
        'notifications': value,
      if (instance.nextToken case final value?) 'nextToken': value,
      if (instance.sessionActive case final value?) 'sessionActive': value,
    };

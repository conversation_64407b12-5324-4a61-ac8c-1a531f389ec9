// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'decline_work_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DeclineWorkBody _$DeclineWorkBodyFromJson(Map json) => $checkedCreate(
      '_DeclineWorkBody',
      json,
      ($checkedConvert) {
        final val = _DeclineWorkBody(
          requestId: $checkedConvert('requestId', (v) => v as String),
          workId: $checkedConvert(
              'workId', (v) => const ParseSfIdConverter().from<PERSON>son(v)),
          workTargetId: $checkedConvert(
              'workTargetId', (v) => const ParseSfIdConverter().from<PERSON>son(v)),
          declineReason: $checkedConvert('declineReason', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$DeclineWorkBodyToJson(_DeclineWorkBody instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      if (const ParseSfIdConverter().toJson(instance.workId) case final value?)
        'workId': value,
      if (const ParseSfIdConverter().toJson(instance.workTargetId)
          case final value?)
        'workTargetId': value,
      if (instance.declineReason case final value?) 'declineReason': value,
    };

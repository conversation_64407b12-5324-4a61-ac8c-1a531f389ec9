// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_chat_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PresenceChatMessage {
  String? get content;
  List<PresenceChatAttachment>? get attachments;
  int? get sequence;
  String? get messageId;
  int? get timestamp;

  /// Create a copy of PresenceChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PresenceChatMessageCopyWith<PresenceChatMessage> get copyWith =>
      _$PresenceChatMessageCopyWithImpl<PresenceChatMessage>(
          this as PresenceChatMessage, _$identity);

  /// Serializes this PresenceChatMessage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PresenceChatMessage &&
            (identical(other.content, content) || other.content == content) &&
            const DeepCollectionEquality()
                .equals(other.attachments, attachments) &&
            (identical(other.sequence, sequence) ||
                other.sequence == sequence) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      content,
      const DeepCollectionEquality().hash(attachments),
      sequence,
      messageId,
      timestamp);

  @override
  String toString() {
    return 'PresenceChatMessage(content: $content, attachments: $attachments, sequence: $sequence, messageId: $messageId, timestamp: $timestamp)';
  }
}

/// @nodoc
abstract mixin class $PresenceChatMessageCopyWith<$Res> {
  factory $PresenceChatMessageCopyWith(
          PresenceChatMessage value, $Res Function(PresenceChatMessage) _then) =
      _$PresenceChatMessageCopyWithImpl;
  @useResult
  $Res call(
      {String? content,
      List<PresenceChatAttachment>? attachments,
      int? sequence,
      String? messageId,
      int? timestamp});
}

/// @nodoc
class _$PresenceChatMessageCopyWithImpl<$Res>
    implements $PresenceChatMessageCopyWith<$Res> {
  _$PresenceChatMessageCopyWithImpl(this._self, this._then);

  final PresenceChatMessage _self;
  final $Res Function(PresenceChatMessage) _then;

  /// Create a copy of PresenceChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? attachments = freezed,
    Object? sequence = freezed,
    Object? messageId = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_self.copyWith(
      content: freezed == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      attachments: freezed == attachments
          ? _self.attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatAttachment>?,
      sequence: freezed == sequence
          ? _self.sequence
          : sequence // ignore: cast_nullable_to_non_nullable
              as int?,
      messageId: freezed == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// Adds pattern-matching-related methods to [PresenceChatMessage].
extension PresenceChatMessagePatterns on PresenceChatMessage {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PresenceChatMessage value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceChatMessage() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PresenceChatMessage value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatMessage():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PresenceChatMessage value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatMessage() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? content, List<PresenceChatAttachment>? attachments,
            int? sequence, String? messageId, int? timestamp)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceChatMessage() when $default != null:
        return $default(_that.content, _that.attachments, _that.sequence,
            _that.messageId, _that.timestamp);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? content, List<PresenceChatAttachment>? attachments,
            int? sequence, String? messageId, int? timestamp)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatMessage():
        return $default(_that.content, _that.attachments, _that.sequence,
            _that.messageId, _that.timestamp);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? content,
            List<PresenceChatAttachment>? attachments,
            int? sequence,
            String? messageId,
            int? timestamp)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceChatMessage() when $default != null:
        return $default(_that.content, _that.attachments, _that.sequence,
            _that.messageId, _that.timestamp);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PresenceChatMessage implements PresenceChatMessage {
  const _PresenceChatMessage(
      {this.content,
      final List<PresenceChatAttachment>? attachments,
      this.sequence,
      this.messageId,
      this.timestamp})
      : _attachments = attachments;
  factory _PresenceChatMessage.fromJson(Map<String, dynamic> json) =>
      _$PresenceChatMessageFromJson(json);

  @override
  final String? content;
  final List<PresenceChatAttachment>? _attachments;
  @override
  List<PresenceChatAttachment>? get attachments {
    final value = _attachments;
    if (value == null) return null;
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? sequence;
  @override
  final String? messageId;
  @override
  final int? timestamp;

  /// Create a copy of PresenceChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PresenceChatMessageCopyWith<_PresenceChatMessage> get copyWith =>
      __$PresenceChatMessageCopyWithImpl<_PresenceChatMessage>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PresenceChatMessageToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PresenceChatMessage &&
            (identical(other.content, content) || other.content == content) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            (identical(other.sequence, sequence) ||
                other.sequence == sequence) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      content,
      const DeepCollectionEquality().hash(_attachments),
      sequence,
      messageId,
      timestamp);

  @override
  String toString() {
    return 'PresenceChatMessage(content: $content, attachments: $attachments, sequence: $sequence, messageId: $messageId, timestamp: $timestamp)';
  }
}

/// @nodoc
abstract mixin class _$PresenceChatMessageCopyWith<$Res>
    implements $PresenceChatMessageCopyWith<$Res> {
  factory _$PresenceChatMessageCopyWith(_PresenceChatMessage value,
          $Res Function(_PresenceChatMessage) _then) =
      __$PresenceChatMessageCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? content,
      List<PresenceChatAttachment>? attachments,
      int? sequence,
      String? messageId,
      int? timestamp});
}

/// @nodoc
class __$PresenceChatMessageCopyWithImpl<$Res>
    implements _$PresenceChatMessageCopyWith<$Res> {
  __$PresenceChatMessageCopyWithImpl(this._self, this._then);

  final _PresenceChatMessage _self;
  final $Res Function(_PresenceChatMessage) _then;

  /// Create a copy of PresenceChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? content = freezed,
    Object? attachments = freezed,
    Object? sequence = freezed,
    Object? messageId = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_PresenceChatMessage(
      content: freezed == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      attachments: freezed == attachments
          ? _self._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatAttachment>?,
      sequence: freezed == sequence
          ? _self.sequence
          : sequence // ignore: cast_nullable_to_non_nullable
              as int?,
      messageId: freezed == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on

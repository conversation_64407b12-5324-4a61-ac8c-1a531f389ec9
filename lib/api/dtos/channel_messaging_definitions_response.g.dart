// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'channel_messaging_definitions_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChannelMessagingDefinitionsResponse
    _$ChannelMessagingDefinitionsResponseFromJson(Map json) => $checkedCreate(
          '_ChannelMessagingDefinitionsResponse',
          json,
          ($checkedConvert) {
            final val = _ChannelMessagingDefinitionsResponse(
              definitions: $checkedConvert(
                  'definitions',
                  (v) => (v as List<dynamic>)
                      .map((e) => MessagingDefinition.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList()),
            );
            return val;
          },
        );

Map<String, dynamic> _$ChannelMessagingDefinitionsResponseToJson(
        _ChannelMessagingDefinitionsResponse instance) =>
    <String, dynamic>{
      'definitions': instance.definitions.map((e) => e.toJson()).toList(),
    };

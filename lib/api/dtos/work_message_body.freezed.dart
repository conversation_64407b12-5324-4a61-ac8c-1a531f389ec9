// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_message_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkMessageBody {
  String get messageId;
  String? get messageBody;
  @ParseSfIdConverter()
  SfId? get workTargetId;
  List<MessageAttachment> get attachments;

  /// Create a copy of WorkMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkMessageBodyCopyWith<WorkMessageBody> get copyWith =>
      _$WorkMessageBodyCopyWithImpl<WorkMessageBody>(
          this as WorkMessageBody, _$identity);

  /// Serializes this WorkMessageBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkMessageBody &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messageBody, messageBody) ||
                other.messageBody == messageBody) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            const DeepCollectionEquality()
                .equals(other.attachments, attachments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messageBody,
      workTargetId, const DeepCollectionEquality().hash(attachments));

  @override
  String toString() {
    return 'WorkMessageBody(messageId: $messageId, messageBody: $messageBody, workTargetId: $workTargetId, attachments: $attachments)';
  }
}

/// @nodoc
abstract mixin class $WorkMessageBodyCopyWith<$Res> {
  factory $WorkMessageBodyCopyWith(
          WorkMessageBody value, $Res Function(WorkMessageBody) _then) =
      _$WorkMessageBodyCopyWithImpl;
  @useResult
  $Res call(
      {String messageId,
      String? messageBody,
      @ParseSfIdConverter() SfId? workTargetId,
      List<MessageAttachment> attachments});

  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class _$WorkMessageBodyCopyWithImpl<$Res>
    implements $WorkMessageBodyCopyWith<$Res> {
  _$WorkMessageBodyCopyWithImpl(this._self, this._then);

  final WorkMessageBody _self;
  final $Res Function(WorkMessageBody) _then;

  /// Create a copy of WorkMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messageBody = freezed,
    Object? workTargetId = freezed,
    Object? attachments = null,
  }) {
    return _then(_self.copyWith(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageBody: freezed == messageBody
          ? _self.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      workTargetId: freezed == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      attachments: null == attachments
          ? _self.attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<MessageAttachment>,
    ));
  }

  /// Create a copy of WorkMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_self.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workTargetId!, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [WorkMessageBody].
extension WorkMessageBodyPatterns on WorkMessageBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WorkMessageBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkMessageBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WorkMessageBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessageBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WorkMessageBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessageBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String messageId,
            String? messageBody,
            @ParseSfIdConverter() SfId? workTargetId,
            List<MessageAttachment> attachments)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkMessageBody() when $default != null:
        return $default(_that.messageId, _that.messageBody, _that.workTargetId,
            _that.attachments);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String messageId,
            String? messageBody,
            @ParseSfIdConverter() SfId? workTargetId,
            List<MessageAttachment> attachments)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessageBody():
        return $default(_that.messageId, _that.messageBody, _that.workTargetId,
            _that.attachments);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String messageId,
            String? messageBody,
            @ParseSfIdConverter() SfId? workTargetId,
            List<MessageAttachment> attachments)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessageBody() when $default != null:
        return $default(_that.messageId, _that.messageBody, _that.workTargetId,
            _that.attachments);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WorkMessageBody implements WorkMessageBody {
  const _WorkMessageBody(
      {required this.messageId,
      this.messageBody,
      @ParseSfIdConverter() this.workTargetId,
      final List<MessageAttachment> attachments = const <MessageAttachment>[]})
      : _attachments = attachments;
  factory _WorkMessageBody.fromJson(Map<String, dynamic> json) =>
      _$WorkMessageBodyFromJson(json);

  @override
  final String messageId;
  @override
  final String? messageBody;
  @override
  @ParseSfIdConverter()
  final SfId? workTargetId;
  final List<MessageAttachment> _attachments;
  @override
  @JsonKey()
  List<MessageAttachment> get attachments {
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachments);
  }

  /// Create a copy of WorkMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkMessageBodyCopyWith<_WorkMessageBody> get copyWith =>
      __$WorkMessageBodyCopyWithImpl<_WorkMessageBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkMessageBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkMessageBody &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messageBody, messageBody) ||
                other.messageBody == messageBody) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messageBody,
      workTargetId, const DeepCollectionEquality().hash(_attachments));

  @override
  String toString() {
    return 'WorkMessageBody(messageId: $messageId, messageBody: $messageBody, workTargetId: $workTargetId, attachments: $attachments)';
  }
}

/// @nodoc
abstract mixin class _$WorkMessageBodyCopyWith<$Res>
    implements $WorkMessageBodyCopyWith<$Res> {
  factory _$WorkMessageBodyCopyWith(
          _WorkMessageBody value, $Res Function(_WorkMessageBody) _then) =
      __$WorkMessageBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String messageId,
      String? messageBody,
      @ParseSfIdConverter() SfId? workTargetId,
      List<MessageAttachment> attachments});

  @override
  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class __$WorkMessageBodyCopyWithImpl<$Res>
    implements _$WorkMessageBodyCopyWith<$Res> {
  __$WorkMessageBodyCopyWithImpl(this._self, this._then);

  final _WorkMessageBody _self;
  final $Res Function(_WorkMessageBody) _then;

  /// Create a copy of WorkMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageId = null,
    Object? messageBody = freezed,
    Object? workTargetId = freezed,
    Object? attachments = null,
  }) {
    return _then(_WorkMessageBody(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageBody: freezed == messageBody
          ? _self.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      workTargetId: freezed == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      attachments: null == attachments
          ? _self._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<MessageAttachment>,
    ));
  }

  /// Create a copy of WorkMessageBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_self.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workTargetId!, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PresenceStatus {
  String get id;
  String get label;
  @JsonKey(
      fromJson: _presenceStatusOptionFromJson,
      toJson: _presenceStatusOptionToJson)
  PresenceStatusOption get statusOption;

  /// Create a copy of PresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PresenceStatusCopyWith<PresenceStatus> get copyWith =>
      _$PresenceStatusCopyWithImpl<PresenceStatus>(
          this as PresenceStatus, _$identity);

  /// Serializes this PresenceStatus to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PresenceStatus &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.statusOption, statusOption) ||
                other.statusOption == statusOption));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, label, statusOption);

  @override
  String toString() {
    return 'PresenceStatus(id: $id, label: $label, statusOption: $statusOption)';
  }
}

/// @nodoc
abstract mixin class $PresenceStatusCopyWith<$Res> {
  factory $PresenceStatusCopyWith(
          PresenceStatus value, $Res Function(PresenceStatus) _then) =
      _$PresenceStatusCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String label,
      @JsonKey(
          fromJson: _presenceStatusOptionFromJson,
          toJson: _presenceStatusOptionToJson)
      PresenceStatusOption statusOption});
}

/// @nodoc
class _$PresenceStatusCopyWithImpl<$Res>
    implements $PresenceStatusCopyWith<$Res> {
  _$PresenceStatusCopyWithImpl(this._self, this._then);

  final PresenceStatus _self;
  final $Res Function(PresenceStatus) _then;

  /// Create a copy of PresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? statusOption = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      statusOption: null == statusOption
          ? _self.statusOption
          : statusOption // ignore: cast_nullable_to_non_nullable
              as PresenceStatusOption,
    ));
  }
}

/// Adds pattern-matching-related methods to [PresenceStatus].
extension PresenceStatusPatterns on PresenceStatus {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PresenceStatus value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceStatus() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PresenceStatus value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatus():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PresenceStatus value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatus() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String label,
            @JsonKey(
                fromJson: _presenceStatusOptionFromJson,
                toJson: _presenceStatusOptionToJson)
            PresenceStatusOption statusOption)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceStatus() when $default != null:
        return $default(_that.id, _that.label, _that.statusOption);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String label,
            @JsonKey(
                fromJson: _presenceStatusOptionFromJson,
                toJson: _presenceStatusOptionToJson)
            PresenceStatusOption statusOption)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatus():
        return $default(_that.id, _that.label, _that.statusOption);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String label,
            @JsonKey(
                fromJson: _presenceStatusOptionFromJson,
                toJson: _presenceStatusOptionToJson)
            PresenceStatusOption statusOption)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceStatus() when $default != null:
        return $default(_that.id, _that.label, _that.statusOption);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PresenceStatus extends PresenceStatus {
  const _PresenceStatus(
      {required this.id,
      required this.label,
      @JsonKey(
          fromJson: _presenceStatusOptionFromJson,
          toJson: _presenceStatusOptionToJson)
      required this.statusOption})
      : super._();
  factory _PresenceStatus.fromJson(Map<String, dynamic> json) =>
      _$PresenceStatusFromJson(json);

  @override
  final String id;
  @override
  final String label;
  @override
  @JsonKey(
      fromJson: _presenceStatusOptionFromJson,
      toJson: _presenceStatusOptionToJson)
  final PresenceStatusOption statusOption;

  /// Create a copy of PresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PresenceStatusCopyWith<_PresenceStatus> get copyWith =>
      __$PresenceStatusCopyWithImpl<_PresenceStatus>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PresenceStatusToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PresenceStatus &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.statusOption, statusOption) ||
                other.statusOption == statusOption));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, label, statusOption);

  @override
  String toString() {
    return 'PresenceStatus(id: $id, label: $label, statusOption: $statusOption)';
  }
}

/// @nodoc
abstract mixin class _$PresenceStatusCopyWith<$Res>
    implements $PresenceStatusCopyWith<$Res> {
  factory _$PresenceStatusCopyWith(
          _PresenceStatus value, $Res Function(_PresenceStatus) _then) =
      __$PresenceStatusCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String label,
      @JsonKey(
          fromJson: _presenceStatusOptionFromJson,
          toJson: _presenceStatusOptionToJson)
      PresenceStatusOption statusOption});
}

/// @nodoc
class __$PresenceStatusCopyWithImpl<$Res>
    implements _$PresenceStatusCopyWith<$Res> {
  __$PresenceStatusCopyWithImpl(this._self, this._then);

  final _PresenceStatus _self;
  final $Res Function(_PresenceStatus) _then;

  /// Create a copy of PresenceStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? statusOption = null,
  }) {
    return _then(_PresenceStatus(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      statusOption: null == statusOption
          ? _self.statusOption
          : statusOption // ignore: cast_nullable_to_non_nullable
              as PresenceStatusOption,
    ));
  }
}

// dart format on

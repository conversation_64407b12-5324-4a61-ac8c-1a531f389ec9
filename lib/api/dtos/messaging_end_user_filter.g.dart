// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_end_user_filter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingEndUserFilter _$MessagingEndUserFilterFromJson(Map json) =>
    $checkedCreate(
      '_MessagingEndUserFilter',
      json,
      ($checkedConvert) {
        final val = _MessagingEndUserFilter(
          messageType: $checkedConvert('messageType', (v) => v as String),
          consentStatus: $checkedConvert('consentStatus', (v) => v as String),
          channelName: $checkedConvert('channelName', (v) => v as String),
          channelId: $checkedConvert(
              'channelId', (v) => const ParseSfIdConverter().fromJson(v)),
          enforceMessagingComponent:
              $checkedConvert('enforceMessagingComponent', (v) => v as bool),
        );
        return val;
      },
    );

Map<String, dynamic> _$MessagingEndUserFilterToJson(
        _MessagingEndUserFilter instance) =>
    <String, dynamic>{
      'messageType': instance.messageType,
      'consentStatus': instance.consentStatus,
      'channelName': instance.channelName,
      if (const ParseSfIdConverter().toJson(instance.channelId)
          case final value?)
        'channelId': value,
      'enforceMessagingComponent': instance.enforceMessagingComponent,
    };

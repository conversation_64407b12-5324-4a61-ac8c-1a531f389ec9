// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_messages_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkMessagesResponse {
  List<SendFailure>? get failures;

  /// Create a copy of WorkMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkMessagesResponseCopyWith<WorkMessagesResponse> get copyWith =>
      _$WorkMessagesResponseCopyWithImpl<WorkMessagesResponse>(
          this as WorkMessagesResponse, _$identity);

  /// Serializes this WorkMessagesResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkMessagesResponse &&
            const DeepCollectionEquality().equals(other.failures, failures));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(failures));

  @override
  String toString() {
    return 'WorkMessagesResponse(failures: $failures)';
  }
}

/// @nodoc
abstract mixin class $WorkMessagesResponseCopyWith<$Res> {
  factory $WorkMessagesResponseCopyWith(WorkMessagesResponse value,
          $Res Function(WorkMessagesResponse) _then) =
      _$WorkMessagesResponseCopyWithImpl;
  @useResult
  $Res call({List<SendFailure>? failures});
}

/// @nodoc
class _$WorkMessagesResponseCopyWithImpl<$Res>
    implements $WorkMessagesResponseCopyWith<$Res> {
  _$WorkMessagesResponseCopyWithImpl(this._self, this._then);

  final WorkMessagesResponse _self;
  final $Res Function(WorkMessagesResponse) _then;

  /// Create a copy of WorkMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failures = freezed,
  }) {
    return _then(_self.copyWith(
      failures: freezed == failures
          ? _self.failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [WorkMessagesResponse].
extension WorkMessagesResponsePatterns on WorkMessagesResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WorkMessagesResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WorkMessagesResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WorkMessagesResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<SendFailure>? failures)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesResponse() when $default != null:
        return $default(_that.failures);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<SendFailure>? failures) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesResponse():
        return $default(_that.failures);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<SendFailure>? failures)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkMessagesResponse() when $default != null:
        return $default(_that.failures);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WorkMessagesResponse implements WorkMessagesResponse {
  const _WorkMessagesResponse({final List<SendFailure>? failures})
      : _failures = failures;
  factory _WorkMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkMessagesResponseFromJson(json);

  final List<SendFailure>? _failures;
  @override
  List<SendFailure>? get failures {
    final value = _failures;
    if (value == null) return null;
    if (_failures is EqualUnmodifiableListView) return _failures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of WorkMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkMessagesResponseCopyWith<_WorkMessagesResponse> get copyWith =>
      __$WorkMessagesResponseCopyWithImpl<_WorkMessagesResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkMessagesResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkMessagesResponse &&
            const DeepCollectionEquality().equals(other._failures, _failures));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_failures));

  @override
  String toString() {
    return 'WorkMessagesResponse(failures: $failures)';
  }
}

/// @nodoc
abstract mixin class _$WorkMessagesResponseCopyWith<$Res>
    implements $WorkMessagesResponseCopyWith<$Res> {
  factory _$WorkMessagesResponseCopyWith(_WorkMessagesResponse value,
          $Res Function(_WorkMessagesResponse) _then) =
      __$WorkMessagesResponseCopyWithImpl;
  @override
  @useResult
  $Res call({List<SendFailure>? failures});
}

/// @nodoc
class __$WorkMessagesResponseCopyWithImpl<$Res>
    implements _$WorkMessagesResponseCopyWith<$Res> {
  __$WorkMessagesResponseCopyWithImpl(this._self, this._then);

  final _WorkMessagesResponse _self;
  final $Res Function(_WorkMessagesResponse) _then;

  /// Create a copy of WorkMessagesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failures = freezed,
  }) {
    return _then(_WorkMessagesResponse(
      failures: freezed == failures
          ? _self._failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ));
  }
}

// dart format on

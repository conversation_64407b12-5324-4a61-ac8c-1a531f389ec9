// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_configuration.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PresenceConfiguration {
  bool get autoAcceptEnabled;

  /// Create a copy of PresenceConfiguration
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PresenceConfigurationCopyWith<PresenceConfiguration> get copyWith =>
      _$PresenceConfigurationCopyWithImpl<PresenceConfiguration>(
          this as PresenceConfiguration, _$identity);

  /// Serializes this PresenceConfiguration to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PresenceConfiguration &&
            (identical(other.autoAcceptEnabled, autoAcceptEnabled) ||
                other.autoAcceptEnabled == autoAcceptEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, autoAcceptEnabled);

  @override
  String toString() {
    return 'PresenceConfiguration(autoAcceptEnabled: $autoAcceptEnabled)';
  }
}

/// @nodoc
abstract mixin class $PresenceConfigurationCopyWith<$Res> {
  factory $PresenceConfigurationCopyWith(PresenceConfiguration value,
          $Res Function(PresenceConfiguration) _then) =
      _$PresenceConfigurationCopyWithImpl;
  @useResult
  $Res call({bool autoAcceptEnabled});
}

/// @nodoc
class _$PresenceConfigurationCopyWithImpl<$Res>
    implements $PresenceConfigurationCopyWith<$Res> {
  _$PresenceConfigurationCopyWithImpl(this._self, this._then);

  final PresenceConfiguration _self;
  final $Res Function(PresenceConfiguration) _then;

  /// Create a copy of PresenceConfiguration
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? autoAcceptEnabled = null,
  }) {
    return _then(_self.copyWith(
      autoAcceptEnabled: null == autoAcceptEnabled
          ? _self.autoAcceptEnabled
          : autoAcceptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [PresenceConfiguration].
extension PresenceConfigurationPatterns on PresenceConfiguration {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PresenceConfiguration value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceConfiguration() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PresenceConfiguration value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceConfiguration():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PresenceConfiguration value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceConfiguration() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool autoAcceptEnabled)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceConfiguration() when $default != null:
        return $default(_that.autoAcceptEnabled);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool autoAcceptEnabled) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceConfiguration():
        return $default(_that.autoAcceptEnabled);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool autoAcceptEnabled)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceConfiguration() when $default != null:
        return $default(_that.autoAcceptEnabled);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PresenceConfiguration implements PresenceConfiguration {
  const _PresenceConfiguration({required this.autoAcceptEnabled});
  factory _PresenceConfiguration.fromJson(Map<String, dynamic> json) =>
      _$PresenceConfigurationFromJson(json);

  @override
  final bool autoAcceptEnabled;

  /// Create a copy of PresenceConfiguration
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PresenceConfigurationCopyWith<_PresenceConfiguration> get copyWith =>
      __$PresenceConfigurationCopyWithImpl<_PresenceConfiguration>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PresenceConfigurationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PresenceConfiguration &&
            (identical(other.autoAcceptEnabled, autoAcceptEnabled) ||
                other.autoAcceptEnabled == autoAcceptEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, autoAcceptEnabled);

  @override
  String toString() {
    return 'PresenceConfiguration(autoAcceptEnabled: $autoAcceptEnabled)';
  }
}

/// @nodoc
abstract mixin class _$PresenceConfigurationCopyWith<$Res>
    implements $PresenceConfigurationCopyWith<$Res> {
  factory _$PresenceConfigurationCopyWith(_PresenceConfiguration value,
          $Res Function(_PresenceConfiguration) _then) =
      __$PresenceConfigurationCopyWithImpl;
  @override
  @useResult
  $Res call({bool autoAcceptEnabled});
}

/// @nodoc
class __$PresenceConfigurationCopyWithImpl<$Res>
    implements _$PresenceConfigurationCopyWith<$Res> {
  __$PresenceConfigurationCopyWithImpl(this._self, this._then);

  final _PresenceConfiguration _self;
  final $Res Function(_PresenceConfiguration) _then;

  /// Create a copy of PresenceConfiguration
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? autoAcceptEnabled = null,
  }) {
    return _then(_PresenceConfiguration(
      autoAcceptEnabled: null == autoAcceptEnabled
          ? _self.autoAcceptEnabled
          : autoAcceptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

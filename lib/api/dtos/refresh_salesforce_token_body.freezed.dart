// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refresh_salesforce_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefreshSalesforceTokenBody {
  String get refreshToken;
  String get orgId;
  String get instanceUrl;

  /// Create a copy of RefreshSalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RefreshSalesforceTokenBodyCopyWith<RefreshSalesforceTokenBody>
      get copyWith =>
          _$RefreshSalesforceTokenBodyCopyWithImpl<RefreshSalesforceTokenBody>(
              this as RefreshSalesforceTokenBody, _$identity);

  /// Serializes this RefreshSalesforceTokenBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RefreshSalesforceTokenBody &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, refreshToken, orgId, instanceUrl);

  @override
  String toString() {
    return 'RefreshSalesforceTokenBody(refreshToken: $refreshToken, orgId: $orgId, instanceUrl: $instanceUrl)';
  }
}

/// @nodoc
abstract mixin class $RefreshSalesforceTokenBodyCopyWith<$Res> {
  factory $RefreshSalesforceTokenBodyCopyWith(RefreshSalesforceTokenBody value,
          $Res Function(RefreshSalesforceTokenBody) _then) =
      _$RefreshSalesforceTokenBodyCopyWithImpl;
  @useResult
  $Res call({String refreshToken, String orgId, String instanceUrl});
}

/// @nodoc
class _$RefreshSalesforceTokenBodyCopyWithImpl<$Res>
    implements $RefreshSalesforceTokenBodyCopyWith<$Res> {
  _$RefreshSalesforceTokenBodyCopyWithImpl(this._self, this._then);

  final RefreshSalesforceTokenBody _self;
  final $Res Function(RefreshSalesforceTokenBody) _then;

  /// Create a copy of RefreshSalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refreshToken = null,
    Object? orgId = null,
    Object? instanceUrl = null,
  }) {
    return _then(_self.copyWith(
      refreshToken: null == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      orgId: null == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [RefreshSalesforceTokenBody].
extension RefreshSalesforceTokenBodyPatterns on RefreshSalesforceTokenBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RefreshSalesforceTokenBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RefreshSalesforceTokenBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RefreshSalesforceTokenBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String refreshToken, String orgId, String instanceUrl)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenBody() when $default != null:
        return $default(_that.refreshToken, _that.orgId, _that.instanceUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String refreshToken, String orgId, String instanceUrl)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenBody():
        return $default(_that.refreshToken, _that.orgId, _that.instanceUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String refreshToken, String orgId, String instanceUrl)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshSalesforceTokenBody() when $default != null:
        return $default(_that.refreshToken, _that.orgId, _that.instanceUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RefreshSalesforceTokenBody implements RefreshSalesforceTokenBody {
  const _RefreshSalesforceTokenBody(
      {required this.refreshToken,
      required this.orgId,
      required this.instanceUrl});
  factory _RefreshSalesforceTokenBody.fromJson(Map<String, dynamic> json) =>
      _$RefreshSalesforceTokenBodyFromJson(json);

  @override
  final String refreshToken;
  @override
  final String orgId;
  @override
  final String instanceUrl;

  /// Create a copy of RefreshSalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RefreshSalesforceTokenBodyCopyWith<_RefreshSalesforceTokenBody>
      get copyWith => __$RefreshSalesforceTokenBodyCopyWithImpl<
          _RefreshSalesforceTokenBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RefreshSalesforceTokenBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RefreshSalesforceTokenBody &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, refreshToken, orgId, instanceUrl);

  @override
  String toString() {
    return 'RefreshSalesforceTokenBody(refreshToken: $refreshToken, orgId: $orgId, instanceUrl: $instanceUrl)';
  }
}

/// @nodoc
abstract mixin class _$RefreshSalesforceTokenBodyCopyWith<$Res>
    implements $RefreshSalesforceTokenBodyCopyWith<$Res> {
  factory _$RefreshSalesforceTokenBodyCopyWith(
          _RefreshSalesforceTokenBody value,
          $Res Function(_RefreshSalesforceTokenBody) _then) =
      __$RefreshSalesforceTokenBodyCopyWithImpl;
  @override
  @useResult
  $Res call({String refreshToken, String orgId, String instanceUrl});
}

/// @nodoc
class __$RefreshSalesforceTokenBodyCopyWithImpl<$Res>
    implements _$RefreshSalesforceTokenBodyCopyWith<$Res> {
  __$RefreshSalesforceTokenBodyCopyWithImpl(this._self, this._then);

  final _RefreshSalesforceTokenBody _self;
  final $Res Function(_RefreshSalesforceTokenBody) _then;

  /// Create a copy of RefreshSalesforceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? refreshToken = null,
    Object? orgId = null,
    Object? instanceUrl = null,
  }) {
    return _then(_RefreshSalesforceTokenBody(
      refreshToken: null == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      orgId: null == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definition.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingDefinition {
  @ParseSfIdConverter()
  SfId? get id;
  String? get name;
  String? get internalName;
  String? get description;
  bool? get hasRequiredParameters;

  /// Create a copy of MessagingDefinition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingDefinitionCopyWith<MessagingDefinition> get copyWith =>
      _$MessagingDefinitionCopyWithImpl<MessagingDefinition>(
          this as MessagingDefinition, _$identity);

  /// Serializes this MessagingDefinition to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingDefinition &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.internalName, internalName) ||
                other.internalName == internalName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.hasRequiredParameters, hasRequiredParameters) ||
                other.hasRequiredParameters == hasRequiredParameters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, internalName, description, hasRequiredParameters);

  @override
  String toString() {
    return 'MessagingDefinition(id: $id, name: $name, internalName: $internalName, description: $description, hasRequiredParameters: $hasRequiredParameters)';
  }
}

/// @nodoc
abstract mixin class $MessagingDefinitionCopyWith<$Res> {
  factory $MessagingDefinitionCopyWith(
          MessagingDefinition value, $Res Function(MessagingDefinition) _then) =
      _$MessagingDefinitionCopyWithImpl;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? id,
      String? name,
      String? internalName,
      String? description,
      bool? hasRequiredParameters});

  $SfIdCopyWith<$Res>? get id;
}

/// @nodoc
class _$MessagingDefinitionCopyWithImpl<$Res>
    implements $MessagingDefinitionCopyWith<$Res> {
  _$MessagingDefinitionCopyWithImpl(this._self, this._then);

  final MessagingDefinition _self;
  final $Res Function(MessagingDefinition) _then;

  /// Create a copy of MessagingDefinition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? internalName = freezed,
    Object? description = freezed,
    Object? hasRequiredParameters = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      internalName: freezed == internalName
          ? _self.internalName
          : internalName // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      hasRequiredParameters: freezed == hasRequiredParameters
          ? _self.hasRequiredParameters
          : hasRequiredParameters // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of MessagingDefinition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get id {
    if (_self.id == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.id!, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingDefinition].
extension MessagingDefinitionPatterns on MessagingDefinition {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingDefinition value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinition() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingDefinition value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinition():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingDefinition value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinition() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @ParseSfIdConverter() SfId? id,
            String? name,
            String? internalName,
            String? description,
            bool? hasRequiredParameters)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinition() when $default != null:
        return $default(_that.id, _that.name, _that.internalName,
            _that.description, _that.hasRequiredParameters);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @ParseSfIdConverter() SfId? id,
            String? name,
            String? internalName,
            String? description,
            bool? hasRequiredParameters)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinition():
        return $default(_that.id, _that.name, _that.internalName,
            _that.description, _that.hasRequiredParameters);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @ParseSfIdConverter() SfId? id,
            String? name,
            String? internalName,
            String? description,
            bool? hasRequiredParameters)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinition() when $default != null:
        return $default(_that.id, _that.name, _that.internalName,
            _that.description, _that.hasRequiredParameters);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingDefinition extends MessagingDefinition {
  const _MessagingDefinition(
      {@ParseSfIdConverter() this.id,
      this.name,
      this.internalName,
      this.description = '',
      this.hasRequiredParameters})
      : super._();
  factory _MessagingDefinition.fromJson(Map<String, dynamic> json) =>
      _$MessagingDefinitionFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId? id;
  @override
  final String? name;
  @override
  final String? internalName;
  @override
  @JsonKey()
  final String? description;
  @override
  final bool? hasRequiredParameters;

  /// Create a copy of MessagingDefinition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingDefinitionCopyWith<_MessagingDefinition> get copyWith =>
      __$MessagingDefinitionCopyWithImpl<_MessagingDefinition>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingDefinitionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingDefinition &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.internalName, internalName) ||
                other.internalName == internalName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.hasRequiredParameters, hasRequiredParameters) ||
                other.hasRequiredParameters == hasRequiredParameters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, internalName, description, hasRequiredParameters);

  @override
  String toString() {
    return 'MessagingDefinition(id: $id, name: $name, internalName: $internalName, description: $description, hasRequiredParameters: $hasRequiredParameters)';
  }
}

/// @nodoc
abstract mixin class _$MessagingDefinitionCopyWith<$Res>
    implements $MessagingDefinitionCopyWith<$Res> {
  factory _$MessagingDefinitionCopyWith(_MessagingDefinition value,
          $Res Function(_MessagingDefinition) _then) =
      __$MessagingDefinitionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? id,
      String? name,
      String? internalName,
      String? description,
      bool? hasRequiredParameters});

  @override
  $SfIdCopyWith<$Res>? get id;
}

/// @nodoc
class __$MessagingDefinitionCopyWithImpl<$Res>
    implements _$MessagingDefinitionCopyWith<$Res> {
  __$MessagingDefinitionCopyWithImpl(this._self, this._then);

  final _MessagingDefinition _self;
  final $Res Function(_MessagingDefinition) _then;

  /// Create a copy of MessagingDefinition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? internalName = freezed,
    Object? description = freezed,
    Object? hasRequiredParameters = freezed,
  }) {
    return _then(_MessagingDefinition(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      internalName: freezed == internalName
          ? _self.internalName
          : internalName // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      hasRequiredParameters: freezed == hasRequiredParameters
          ? _self.hasRequiredParameters
          : hasRequiredParameters // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of MessagingDefinition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get id {
    if (_self.id == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.id!, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

// dart format on

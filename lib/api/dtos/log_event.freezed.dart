// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LogEvent {
  int get timestamp;
  String get packageName;
  String get level;
  LogMetadata get metadata;
  String get message;

  /// Create a copy of LogEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LogEventCopyWith<LogEvent> get copyWith =>
      _$LogEventCopyWithImpl<LogEvent>(this as LogEvent, _$identity);

  /// Serializes this LogEvent to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LogEvent &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.packageName, packageName) ||
                other.packageName == packageName) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, timestamp, packageName, level, metadata, message);

  @override
  String toString() {
    return 'LogEvent(timestamp: $timestamp, packageName: $packageName, level: $level, metadata: $metadata, message: $message)';
  }
}

/// @nodoc
abstract mixin class $LogEventCopyWith<$Res> {
  factory $LogEventCopyWith(LogEvent value, $Res Function(LogEvent) _then) =
      _$LogEventCopyWithImpl;
  @useResult
  $Res call(
      {int timestamp,
      String packageName,
      String level,
      LogMetadata metadata,
      String message});

  $LogMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class _$LogEventCopyWithImpl<$Res> implements $LogEventCopyWith<$Res> {
  _$LogEventCopyWithImpl(this._self, this._then);

  final LogEvent _self;
  final $Res Function(LogEvent) _then;

  /// Create a copy of LogEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? packageName = null,
    Object? level = null,
    Object? metadata = null,
    Object? message = null,
  }) {
    return _then(_self.copyWith(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      packageName: null == packageName
          ? _self.packageName
          : packageName // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      metadata: null == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as LogMetadata,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of LogEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LogMetadataCopyWith<$Res> get metadata {
    return $LogMetadataCopyWith<$Res>(_self.metadata, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

/// Adds pattern-matching-related methods to [LogEvent].
extension LogEventPatterns on LogEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_LogEvent value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogEvent() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_LogEvent value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEvent():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_LogEvent value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEvent() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(int timestamp, String packageName, String level,
            LogMetadata metadata, String message)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LogEvent() when $default != null:
        return $default(_that.timestamp, _that.packageName, _that.level,
            _that.metadata, _that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(int timestamp, String packageName, String level,
            LogMetadata metadata, String message)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEvent():
        return $default(_that.timestamp, _that.packageName, _that.level,
            _that.metadata, _that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(int timestamp, String packageName, String level,
            LogMetadata metadata, String message)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _LogEvent() when $default != null:
        return $default(_that.timestamp, _that.packageName, _that.level,
            _that.metadata, _that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _LogEvent extends LogEvent {
  const _LogEvent(
      {required this.timestamp,
      required this.packageName,
      required this.level,
      required this.metadata,
      required this.message})
      : super._();
  factory _LogEvent.fromJson(Map<String, dynamic> json) =>
      _$LogEventFromJson(json);

  @override
  final int timestamp;
  @override
  final String packageName;
  @override
  final String level;
  @override
  final LogMetadata metadata;
  @override
  final String message;

  /// Create a copy of LogEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LogEventCopyWith<_LogEvent> get copyWith =>
      __$LogEventCopyWithImpl<_LogEvent>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LogEventToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LogEvent &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.packageName, packageName) ||
                other.packageName == packageName) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, timestamp, packageName, level, metadata, message);

  @override
  String toString() {
    return 'LogEvent(timestamp: $timestamp, packageName: $packageName, level: $level, metadata: $metadata, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$LogEventCopyWith<$Res>
    implements $LogEventCopyWith<$Res> {
  factory _$LogEventCopyWith(_LogEvent value, $Res Function(_LogEvent) _then) =
      __$LogEventCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int timestamp,
      String packageName,
      String level,
      LogMetadata metadata,
      String message});

  @override
  $LogMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class __$LogEventCopyWithImpl<$Res> implements _$LogEventCopyWith<$Res> {
  __$LogEventCopyWithImpl(this._self, this._then);

  final _LogEvent _self;
  final $Res Function(_LogEvent) _then;

  /// Create a copy of LogEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? timestamp = null,
    Object? packageName = null,
    Object? level = null,
    Object? metadata = null,
    Object? message = null,
  }) {
    return _then(_LogEvent(
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      packageName: null == packageName
          ? _self.packageName
          : packageName // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      metadata: null == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as LogMetadata,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of LogEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LogMetadataCopyWith<$Res> get metadata {
    return $LogMetadataCopyWith<$Res>(_self.metadata, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_id_and_session_id.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessageIdAndSessionId {
  @ParseSfIdConverter()
  SfId get messageId;
  @ParseSfIdConverter()
  SfId get messagingSessionId;

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessageIdAndSessionIdCopyWith<MessageIdAndSessionId> get copyWith =>
      _$MessageIdAndSessionIdCopyWithImpl<MessageIdAndSessionId>(
          this as MessageIdAndSessionId, _$identity);

  /// Serializes this MessageIdAndSessionId to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessageIdAndSessionId &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messagingSessionId);

  @override
  String toString() {
    return 'MessageIdAndSessionId(messageId: $messageId, messagingSessionId: $messagingSessionId)';
  }
}

/// @nodoc
abstract mixin class $MessageIdAndSessionIdCopyWith<$Res> {
  factory $MessageIdAndSessionIdCopyWith(MessageIdAndSessionId value,
          $Res Function(MessageIdAndSessionId) _then) =
      _$MessageIdAndSessionIdCopyWithImpl;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId messageId,
      @ParseSfIdConverter() SfId messagingSessionId});

  $SfIdCopyWith<$Res> get messageId;
  $SfIdCopyWith<$Res> get messagingSessionId;
}

/// @nodoc
class _$MessageIdAndSessionIdCopyWithImpl<$Res>
    implements $MessageIdAndSessionIdCopyWith<$Res> {
  _$MessageIdAndSessionIdCopyWithImpl(this._self, this._then);

  final MessageIdAndSessionId _self;
  final $Res Function(MessageIdAndSessionId) _then;

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_self.copyWith(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingSessionId: null == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messageId {
    return $SfIdCopyWith<$Res>(_self.messageId, (value) {
      return _then(_self.copyWith(messageId: value));
    });
  }

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingSessionId {
    return $SfIdCopyWith<$Res>(_self.messagingSessionId, (value) {
      return _then(_self.copyWith(messagingSessionId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessageIdAndSessionId].
extension MessageIdAndSessionIdPatterns on MessageIdAndSessionId {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessageIdAndSessionId value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessageIdAndSessionId() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessageIdAndSessionId value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessageIdAndSessionId():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessageIdAndSessionId value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessageIdAndSessionId() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId messageId,
            @ParseSfIdConverter() SfId messagingSessionId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessageIdAndSessionId() when $default != null:
        return $default(_that.messageId, _that.messagingSessionId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId messageId,
            @ParseSfIdConverter() SfId messagingSessionId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessageIdAndSessionId():
        return $default(_that.messageId, _that.messagingSessionId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@ParseSfIdConverter() SfId messageId,
            @ParseSfIdConverter() SfId messagingSessionId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessageIdAndSessionId() when $default != null:
        return $default(_that.messageId, _that.messagingSessionId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessageIdAndSessionId implements MessageIdAndSessionId {
  const _MessageIdAndSessionId(
      {@ParseSfIdConverter() required this.messageId,
      @ParseSfIdConverter() required this.messagingSessionId});
  factory _MessageIdAndSessionId.fromJson(Map<String, dynamic> json) =>
      _$MessageIdAndSessionIdFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId messageId;
  @override
  @ParseSfIdConverter()
  final SfId messagingSessionId;

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessageIdAndSessionIdCopyWith<_MessageIdAndSessionId> get copyWith =>
      __$MessageIdAndSessionIdCopyWithImpl<_MessageIdAndSessionId>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessageIdAndSessionIdToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessageIdAndSessionId &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messagingSessionId);

  @override
  String toString() {
    return 'MessageIdAndSessionId(messageId: $messageId, messagingSessionId: $messagingSessionId)';
  }
}

/// @nodoc
abstract mixin class _$MessageIdAndSessionIdCopyWith<$Res>
    implements $MessageIdAndSessionIdCopyWith<$Res> {
  factory _$MessageIdAndSessionIdCopyWith(_MessageIdAndSessionId value,
          $Res Function(_MessageIdAndSessionId) _then) =
      __$MessageIdAndSessionIdCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId messageId,
      @ParseSfIdConverter() SfId messagingSessionId});

  @override
  $SfIdCopyWith<$Res> get messageId;
  @override
  $SfIdCopyWith<$Res> get messagingSessionId;
}

/// @nodoc
class __$MessageIdAndSessionIdCopyWithImpl<$Res>
    implements _$MessageIdAndSessionIdCopyWith<$Res> {
  __$MessageIdAndSessionIdCopyWithImpl(this._self, this._then);

  final _MessageIdAndSessionId _self;
  final $Res Function(_MessageIdAndSessionId) _then;

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_MessageIdAndSessionId(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingSessionId: null == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messageId {
    return $SfIdCopyWith<$Res>(_self.messageId, (value) {
      return _then(_self.copyWith(messageId: value));
    });
  }

  /// Create a copy of MessageIdAndSessionId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingSessionId {
    return $SfIdCopyWith<$Res>(_self.messagingSessionId, (value) {
      return _then(_self.copyWith(messagingSessionId: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sessions_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$<PERSON>Response {
  String get sessionToken;
  String get sessionId;
  int get expirationTime;
  List<PresenceStatus>? get presenceStatuses;
  String? get presenceStatusId;
  String? get scrtAccessToken;
  String? get scrtHost;
  String get webSocketUrl;

  /// Create a copy of SessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SessionsResponseCopyWith<SessionsResponse> get copyWith =>
      _$SessionsResponseCopyWithImpl<SessionsResponse>(
          this as SessionsResponse, _$identity);

  /// Serializes this SessionsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SessionsResponse &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.expirationTime, expirationTime) ||
                other.expirationTime == expirationTime) &&
            const DeepCollectionEquality()
                .equals(other.presenceStatuses, presenceStatuses) &&
            (identical(other.presenceStatusId, presenceStatusId) ||
                other.presenceStatusId == presenceStatusId) &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken) &&
            (identical(other.scrtHost, scrtHost) ||
                other.scrtHost == scrtHost) &&
            (identical(other.webSocketUrl, webSocketUrl) ||
                other.webSocketUrl == webSocketUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionToken,
      sessionId,
      expirationTime,
      const DeepCollectionEquality().hash(presenceStatuses),
      presenceStatusId,
      scrtAccessToken,
      scrtHost,
      webSocketUrl);

  @override
  String toString() {
    return 'SessionsResponse(sessionToken: $sessionToken, sessionId: $sessionId, expirationTime: $expirationTime, presenceStatuses: $presenceStatuses, presenceStatusId: $presenceStatusId, scrtAccessToken: $scrtAccessToken, scrtHost: $scrtHost, webSocketUrl: $webSocketUrl)';
  }
}

/// @nodoc
abstract mixin class $SessionsResponseCopyWith<$Res> {
  factory $SessionsResponseCopyWith(
          SessionsResponse value, $Res Function(SessionsResponse) _then) =
      _$SessionsResponseCopyWithImpl;
  @useResult
  $Res call(
      {String sessionToken,
      String sessionId,
      int expirationTime,
      List<PresenceStatus>? presenceStatuses,
      String? presenceStatusId,
      String? scrtAccessToken,
      String? scrtHost,
      String webSocketUrl});
}

/// @nodoc
class _$SessionsResponseCopyWithImpl<$Res>
    implements $SessionsResponseCopyWith<$Res> {
  _$SessionsResponseCopyWithImpl(this._self, this._then);

  final SessionsResponse _self;
  final $Res Function(SessionsResponse) _then;

  /// Create a copy of SessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionToken = null,
    Object? sessionId = null,
    Object? expirationTime = null,
    Object? presenceStatuses = freezed,
    Object? presenceStatusId = freezed,
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
    Object? webSocketUrl = null,
  }) {
    return _then(_self.copyWith(
      sessionToken: null == sessionToken
          ? _self.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      expirationTime: null == expirationTime
          ? _self.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
      presenceStatuses: freezed == presenceStatuses
          ? _self.presenceStatuses
          : presenceStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>?,
      presenceStatusId: freezed == presenceStatusId
          ? _self.presenceStatusId
          : presenceStatusId // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _self.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _self.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: null == webSocketUrl
          ? _self.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [SessionsResponse].
extension SessionsResponsePatterns on SessionsResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SessionsResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SessionsResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SessionsResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SessionsResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String sessionToken,
            String sessionId,
            int expirationTime,
            List<PresenceStatus>? presenceStatuses,
            String? presenceStatusId,
            String? scrtAccessToken,
            String? scrtHost,
            String webSocketUrl)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SessionsResponse() when $default != null:
        return $default(
            _that.sessionToken,
            _that.sessionId,
            _that.expirationTime,
            _that.presenceStatuses,
            _that.presenceStatusId,
            _that.scrtAccessToken,
            _that.scrtHost,
            _that.webSocketUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String sessionToken,
            String sessionId,
            int expirationTime,
            List<PresenceStatus>? presenceStatuses,
            String? presenceStatusId,
            String? scrtAccessToken,
            String? scrtHost,
            String webSocketUrl)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsResponse():
        return $default(
            _that.sessionToken,
            _that.sessionId,
            _that.expirationTime,
            _that.presenceStatuses,
            _that.presenceStatusId,
            _that.scrtAccessToken,
            _that.scrtHost,
            _that.webSocketUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String sessionToken,
            String sessionId,
            int expirationTime,
            List<PresenceStatus>? presenceStatuses,
            String? presenceStatusId,
            String? scrtAccessToken,
            String? scrtHost,
            String webSocketUrl)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SessionsResponse() when $default != null:
        return $default(
            _that.sessionToken,
            _that.sessionId,
            _that.expirationTime,
            _that.presenceStatuses,
            _that.presenceStatusId,
            _that.scrtAccessToken,
            _that.scrtHost,
            _that.webSocketUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SessionsResponse implements SessionsResponse {
  const _SessionsResponse(
      {required this.sessionToken,
      required this.sessionId,
      required this.expirationTime,
      final List<PresenceStatus>? presenceStatuses,
      this.presenceStatusId,
      this.scrtAccessToken,
      this.scrtHost,
      required this.webSocketUrl})
      : _presenceStatuses = presenceStatuses;
  factory _SessionsResponse.fromJson(Map<String, dynamic> json) =>
      _$SessionsResponseFromJson(json);

  @override
  final String sessionToken;
  @override
  final String sessionId;
  @override
  final int expirationTime;
  final List<PresenceStatus>? _presenceStatuses;
  @override
  List<PresenceStatus>? get presenceStatuses {
    final value = _presenceStatuses;
    if (value == null) return null;
    if (_presenceStatuses is EqualUnmodifiableListView)
      return _presenceStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? presenceStatusId;
  @override
  final String? scrtAccessToken;
  @override
  final String? scrtHost;
  @override
  final String webSocketUrl;

  /// Create a copy of SessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SessionsResponseCopyWith<_SessionsResponse> get copyWith =>
      __$SessionsResponseCopyWithImpl<_SessionsResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SessionsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SessionsResponse &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.expirationTime, expirationTime) ||
                other.expirationTime == expirationTime) &&
            const DeepCollectionEquality()
                .equals(other._presenceStatuses, _presenceStatuses) &&
            (identical(other.presenceStatusId, presenceStatusId) ||
                other.presenceStatusId == presenceStatusId) &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken) &&
            (identical(other.scrtHost, scrtHost) ||
                other.scrtHost == scrtHost) &&
            (identical(other.webSocketUrl, webSocketUrl) ||
                other.webSocketUrl == webSocketUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionToken,
      sessionId,
      expirationTime,
      const DeepCollectionEquality().hash(_presenceStatuses),
      presenceStatusId,
      scrtAccessToken,
      scrtHost,
      webSocketUrl);

  @override
  String toString() {
    return 'SessionsResponse(sessionToken: $sessionToken, sessionId: $sessionId, expirationTime: $expirationTime, presenceStatuses: $presenceStatuses, presenceStatusId: $presenceStatusId, scrtAccessToken: $scrtAccessToken, scrtHost: $scrtHost, webSocketUrl: $webSocketUrl)';
  }
}

/// @nodoc
abstract mixin class _$SessionsResponseCopyWith<$Res>
    implements $SessionsResponseCopyWith<$Res> {
  factory _$SessionsResponseCopyWith(
          _SessionsResponse value, $Res Function(_SessionsResponse) _then) =
      __$SessionsResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String sessionToken,
      String sessionId,
      int expirationTime,
      List<PresenceStatus>? presenceStatuses,
      String? presenceStatusId,
      String? scrtAccessToken,
      String? scrtHost,
      String webSocketUrl});
}

/// @nodoc
class __$SessionsResponseCopyWithImpl<$Res>
    implements _$SessionsResponseCopyWith<$Res> {
  __$SessionsResponseCopyWithImpl(this._self, this._then);

  final _SessionsResponse _self;
  final $Res Function(_SessionsResponse) _then;

  /// Create a copy of SessionsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sessionToken = null,
    Object? sessionId = null,
    Object? expirationTime = null,
    Object? presenceStatuses = freezed,
    Object? presenceStatusId = freezed,
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
    Object? webSocketUrl = null,
  }) {
    return _then(_SessionsResponse(
      sessionToken: null == sessionToken
          ? _self.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _self.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      expirationTime: null == expirationTime
          ? _self.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
      presenceStatuses: freezed == presenceStatuses
          ? _self._presenceStatuses
          : presenceStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>?,
      presenceStatusId: freezed == presenceStatusId
          ? _self.presenceStatusId
          : presenceStatusId // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _self.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _self.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: null == webSocketUrl
          ? _self.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

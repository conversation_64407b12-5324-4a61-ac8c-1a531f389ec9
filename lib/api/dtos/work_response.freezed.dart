// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkResponse {
  String? get conversationIdentifier;
  List<PresenceChatMessage>? get messages;
  bool get canTransfer;
  bool get canRaiseFlag;

  /// Create a copy of WorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkResponseCopyWith<WorkResponse> get copyWith =>
      _$WorkResponseCopyWithImpl<WorkResponse>(
          this as WorkResponse, _$identity);

  /// Serializes this WorkResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkResponse &&
            (identical(other.conversationIdentifier, conversationIdentifier) ||
                other.conversationIdentifier == conversationIdentifier) &&
            const DeepCollectionEquality().equals(other.messages, messages) &&
            (identical(other.canTransfer, canTransfer) ||
                other.canTransfer == canTransfer) &&
            (identical(other.canRaiseFlag, canRaiseFlag) ||
                other.canRaiseFlag == canRaiseFlag));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, conversationIdentifier,
      const DeepCollectionEquality().hash(messages), canTransfer, canRaiseFlag);

  @override
  String toString() {
    return 'WorkResponse(conversationIdentifier: $conversationIdentifier, messages: $messages, canTransfer: $canTransfer, canRaiseFlag: $canRaiseFlag)';
  }
}

/// @nodoc
abstract mixin class $WorkResponseCopyWith<$Res> {
  factory $WorkResponseCopyWith(
          WorkResponse value, $Res Function(WorkResponse) _then) =
      _$WorkResponseCopyWithImpl;
  @useResult
  $Res call(
      {String? conversationIdentifier,
      List<PresenceChatMessage>? messages,
      bool canTransfer,
      bool canRaiseFlag});
}

/// @nodoc
class _$WorkResponseCopyWithImpl<$Res> implements $WorkResponseCopyWith<$Res> {
  _$WorkResponseCopyWithImpl(this._self, this._then);

  final WorkResponse _self;
  final $Res Function(WorkResponse) _then;

  /// Create a copy of WorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversationIdentifier = freezed,
    Object? messages = freezed,
    Object? canTransfer = null,
    Object? canRaiseFlag = null,
  }) {
    return _then(_self.copyWith(
      conversationIdentifier: freezed == conversationIdentifier
          ? _self.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      messages: freezed == messages
          ? _self.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatMessage>?,
      canTransfer: null == canTransfer
          ? _self.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      canRaiseFlag: null == canRaiseFlag
          ? _self.canRaiseFlag
          : canRaiseFlag // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [WorkResponse].
extension WorkResponsePatterns on WorkResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_WorkResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_WorkResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_WorkResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? conversationIdentifier,
            List<PresenceChatMessage>? messages,
            bool canTransfer,
            bool canRaiseFlag)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WorkResponse() when $default != null:
        return $default(_that.conversationIdentifier, _that.messages,
            _that.canTransfer, _that.canRaiseFlag);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? conversationIdentifier,
            List<PresenceChatMessage>? messages,
            bool canTransfer,
            bool canRaiseFlag)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkResponse():
        return $default(_that.conversationIdentifier, _that.messages,
            _that.canTransfer, _that.canRaiseFlag);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? conversationIdentifier,
            List<PresenceChatMessage>? messages,
            bool canTransfer,
            bool canRaiseFlag)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _WorkResponse() when $default != null:
        return $default(_that.conversationIdentifier, _that.messages,
            _that.canTransfer, _that.canRaiseFlag);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _WorkResponse implements WorkResponse {
  const _WorkResponse(
      {this.conversationIdentifier,
      final List<PresenceChatMessage>? messages,
      this.canTransfer = false,
      this.canRaiseFlag = false})
      : _messages = messages;
  factory _WorkResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkResponseFromJson(json);

  @override
  final String? conversationIdentifier;
  final List<PresenceChatMessage>? _messages;
  @override
  List<PresenceChatMessage>? get messages {
    final value = _messages;
    if (value == null) return null;
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool canTransfer;
  @override
  @JsonKey()
  final bool canRaiseFlag;

  /// Create a copy of WorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkResponseCopyWith<_WorkResponse> get copyWith =>
      __$WorkResponseCopyWithImpl<_WorkResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkResponse &&
            (identical(other.conversationIdentifier, conversationIdentifier) ||
                other.conversationIdentifier == conversationIdentifier) &&
            const DeepCollectionEquality().equals(other._messages, _messages) &&
            (identical(other.canTransfer, canTransfer) ||
                other.canTransfer == canTransfer) &&
            (identical(other.canRaiseFlag, canRaiseFlag) ||
                other.canRaiseFlag == canRaiseFlag));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      conversationIdentifier,
      const DeepCollectionEquality().hash(_messages),
      canTransfer,
      canRaiseFlag);

  @override
  String toString() {
    return 'WorkResponse(conversationIdentifier: $conversationIdentifier, messages: $messages, canTransfer: $canTransfer, canRaiseFlag: $canRaiseFlag)';
  }
}

/// @nodoc
abstract mixin class _$WorkResponseCopyWith<$Res>
    implements $WorkResponseCopyWith<$Res> {
  factory _$WorkResponseCopyWith(
          _WorkResponse value, $Res Function(_WorkResponse) _then) =
      __$WorkResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? conversationIdentifier,
      List<PresenceChatMessage>? messages,
      bool canTransfer,
      bool canRaiseFlag});
}

/// @nodoc
class __$WorkResponseCopyWithImpl<$Res>
    implements _$WorkResponseCopyWith<$Res> {
  __$WorkResponseCopyWithImpl(this._self, this._then);

  final _WorkResponse _self;
  final $Res Function(_WorkResponse) _then;

  /// Create a copy of WorkResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? conversationIdentifier = freezed,
    Object? messages = freezed,
    Object? canTransfer = null,
    Object? canRaiseFlag = null,
  }) {
    return _then(_WorkResponse(
      conversationIdentifier: freezed == conversationIdentifier
          ? _self.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      messages: freezed == messages
          ? _self._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatMessage>?,
      canTransfer: null == canTransfer
          ? _self.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      canRaiseFlag: null == canRaiseFlag
          ? _self.canRaiseFlag
          : canRaiseFlag // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

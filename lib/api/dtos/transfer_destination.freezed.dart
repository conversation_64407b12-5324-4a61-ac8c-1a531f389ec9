// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_destination.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TransferDestination {
  @ParseSfIdConverter()
  SfId get id;
  String get label;
  String? get smallPhotoUrl;

  /// seconds
  int? get estimatedWait;

  /// Create a copy of TransferDestination
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TransferDestinationCopyWith<TransferDestination> get copyWith =>
      _$TransferDestinationCopyWithImpl<TransferDestination>(
          this as TransferDestination, _$identity);

  /// Serializes this TransferDestination to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TransferDestination &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.smallPhotoUrl, smallPhotoUrl) ||
                other.smallPhotoUrl == smallPhotoUrl) &&
            (identical(other.estimatedWait, estimatedWait) ||
                other.estimatedWait == estimatedWait));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, label, smallPhotoUrl, estimatedWait);

  @override
  String toString() {
    return 'TransferDestination(id: $id, label: $label, smallPhotoUrl: $smallPhotoUrl, estimatedWait: $estimatedWait)';
  }
}

/// @nodoc
abstract mixin class $TransferDestinationCopyWith<$Res> {
  factory $TransferDestinationCopyWith(
          TransferDestination value, $Res Function(TransferDestination) _then) =
      _$TransferDestinationCopyWithImpl;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String label,
      String? smallPhotoUrl,
      int? estimatedWait});

  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class _$TransferDestinationCopyWithImpl<$Res>
    implements $TransferDestinationCopyWith<$Res> {
  _$TransferDestinationCopyWithImpl(this._self, this._then);

  final TransferDestination _self;
  final $Res Function(TransferDestination) _then;

  /// Create a copy of TransferDestination
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? smallPhotoUrl = freezed,
    Object? estimatedWait = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      smallPhotoUrl: freezed == smallPhotoUrl
          ? _self.smallPhotoUrl
          : smallPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedWait: freezed == estimatedWait
          ? _self.estimatedWait
          : estimatedWait // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }

  /// Create a copy of TransferDestination
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

/// Adds pattern-matching-related methods to [TransferDestination].
extension TransferDestinationPatterns on TransferDestination {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_TransferDestination value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferDestination() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_TransferDestination value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferDestination():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_TransferDestination value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferDestination() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId id, String label,
            String? smallPhotoUrl, int? estimatedWait)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferDestination() when $default != null:
        return $default(
            _that.id, _that.label, _that.smallPhotoUrl, _that.estimatedWait);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId id, String label,
            String? smallPhotoUrl, int? estimatedWait)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferDestination():
        return $default(
            _that.id, _that.label, _that.smallPhotoUrl, _that.estimatedWait);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@ParseSfIdConverter() SfId id, String label,
            String? smallPhotoUrl, int? estimatedWait)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferDestination() when $default != null:
        return $default(
            _that.id, _that.label, _that.smallPhotoUrl, _that.estimatedWait);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _TransferDestination implements TransferDestination {
  const _TransferDestination(
      {@ParseSfIdConverter() required this.id,
      required this.label,
      this.smallPhotoUrl = null,
      this.estimatedWait = null});
  factory _TransferDestination.fromJson(Map<String, dynamic> json) =>
      _$TransferDestinationFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId id;
  @override
  final String label;
  @override
  @JsonKey()
  final String? smallPhotoUrl;

  /// seconds
  @override
  @JsonKey()
  final int? estimatedWait;

  /// Create a copy of TransferDestination
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TransferDestinationCopyWith<_TransferDestination> get copyWith =>
      __$TransferDestinationCopyWithImpl<_TransferDestination>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TransferDestinationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TransferDestination &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.smallPhotoUrl, smallPhotoUrl) ||
                other.smallPhotoUrl == smallPhotoUrl) &&
            (identical(other.estimatedWait, estimatedWait) ||
                other.estimatedWait == estimatedWait));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, label, smallPhotoUrl, estimatedWait);

  @override
  String toString() {
    return 'TransferDestination(id: $id, label: $label, smallPhotoUrl: $smallPhotoUrl, estimatedWait: $estimatedWait)';
  }
}

/// @nodoc
abstract mixin class _$TransferDestinationCopyWith<$Res>
    implements $TransferDestinationCopyWith<$Res> {
  factory _$TransferDestinationCopyWith(_TransferDestination value,
          $Res Function(_TransferDestination) _then) =
      __$TransferDestinationCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String label,
      String? smallPhotoUrl,
      int? estimatedWait});

  @override
  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class __$TransferDestinationCopyWithImpl<$Res>
    implements _$TransferDestinationCopyWith<$Res> {
  __$TransferDestinationCopyWithImpl(this._self, this._then);

  final _TransferDestination _self;
  final $Res Function(_TransferDestination) _then;

  /// Create a copy of TransferDestination
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? smallPhotoUrl = freezed,
    Object? estimatedWait = freezed,
  }) {
    return _then(_TransferDestination(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      smallPhotoUrl: freezed == smallPhotoUrl
          ? _self.smallPhotoUrl
          : smallPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedWait: freezed == estimatedWait
          ? _self.estimatedWait
          : estimatedWait // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }

  /// Create a copy of TransferDestination
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

// dart format on

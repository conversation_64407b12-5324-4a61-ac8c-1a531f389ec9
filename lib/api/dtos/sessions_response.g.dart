// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sessions_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SessionsResponse _$SessionsResponseFromJson(Map json) => $checkedCreate(
      '_SessionsResponse',
      json,
      ($checkedConvert) {
        final val = _SessionsResponse(
          sessionToken: $checkedConvert('sessionToken', (v) => v as String),
          sessionId: $checkedConvert('sessionId', (v) => v as String),
          expirationTime:
              $checkedConvert('expirationTime', (v) => (v as num).toInt()),
          presenceStatuses: $checkedConvert(
              'presenceStatuses',
              (v) => (v as List<dynamic>?)
                  ?.map((e) => PresenceStatus.fromJson(
                      Map<String, dynamic>.from(e as Map)))
                  .toList()),
          presenceStatusId:
              $checkedConvert('presenceStatusId', (v) => v as String?),
          scrtAccessToken:
              $checkedConvert('scrtAccessToken', (v) => v as String?),
          scrtHost: $checkedConvert('scrtHost', (v) => v as String?),
          webSocketUrl: $checkedConvert('webSocketUrl', (v) => v as String),
        );
        return val;
      },
    );

Map<String, dynamic> _$SessionsResponseToJson(_SessionsResponse instance) =>
    <String, dynamic>{
      'sessionToken': instance.sessionToken,
      'sessionId': instance.sessionId,
      'expirationTime': instance.expirationTime,
      if (instance.presenceStatuses?.map((e) => e.toJson()).toList()
          case final value?)
        'presenceStatuses': value,
      if (instance.presenceStatusId case final value?)
        'presenceStatusId': value,
      if (instance.scrtAccessToken case final value?) 'scrtAccessToken': value,
      if (instance.scrtHost case final value?) 'scrtHost': value,
      'webSocketUrl': instance.webSocketUrl,
    };

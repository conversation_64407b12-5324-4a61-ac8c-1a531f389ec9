// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NotificationMessage _$NotificationMessageFromJson(Map json) => $checkedCreate(
      '_NotificationMessage',
      json,
      ($checkedConvert) {
        final val = _NotificationMessage(
          timestamp: $checkedConvert(
              'timestamp', (v) => const TimestampIntConverter().fromJson(v)),
          notificationId: $checkedConvert('notificationId', (v) => v as String),
          sessionId: $checkedConvert('sessionId', (v) => v as String),
          messageCategory:
              $checkedConvert('messageCategory', (v) => v as String),
          payload: $checkedConvert(
              'payload', (v) => Map<String, dynamic>.from(v as Map)),
          sentToWebSocket: $checkedConvert('sentToWebSocket', (v) => v as bool),
          pushed: $checkedConvert('pushed', (v) => v as bool),
        );
        return val;
      },
    );

Map<String, dynamic> _$NotificationMessageToJson(
        _NotificationMessage instance) =>
    <String, dynamic>{
      if (const TimestampIntConverter().toJson(instance.timestamp)
          case final value?)
        'timestamp': value,
      'notificationId': instance.notificationId,
      'sessionId': instance.sessionId,
      'messageCategory': instance.messageCategory,
      'payload': instance.payload,
      'sentToWebSocket': instance.sentToWebSocket,
      'pushed': instance.pushed,
    };

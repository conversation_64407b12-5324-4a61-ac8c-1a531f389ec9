// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_filter.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingEndUserFilter {
  String get messageType;
  String get consentStatus;
  String get channelName;
  @ParseSfIdConverter()
  SfId get channelId;
  bool get enforceMessagingComponent;

  /// Create a copy of MessagingEndUserFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingEndUserFilterCopyWith<MessagingEndUserFilter> get copyWith =>
      _$MessagingEndUserFilterCopyWithImpl<MessagingEndUserFilter>(
          this as MessagingEndUserFilter, _$identity);

  /// Serializes this MessagingEndUserFilter to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingEndUserFilter &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.consentStatus, consentStatus) ||
                other.consentStatus == consentStatus) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.channelId, channelId) ||
                other.channelId == channelId) &&
            (identical(other.enforceMessagingComponent,
                    enforceMessagingComponent) ||
                other.enforceMessagingComponent == enforceMessagingComponent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageType, consentStatus,
      channelName, channelId, enforceMessagingComponent);

  @override
  String toString() {
    return 'MessagingEndUserFilter(messageType: $messageType, consentStatus: $consentStatus, channelName: $channelName, channelId: $channelId, enforceMessagingComponent: $enforceMessagingComponent)';
  }
}

/// @nodoc
abstract mixin class $MessagingEndUserFilterCopyWith<$Res> {
  factory $MessagingEndUserFilterCopyWith(MessagingEndUserFilter value,
          $Res Function(MessagingEndUserFilter) _then) =
      _$MessagingEndUserFilterCopyWithImpl;
  @useResult
  $Res call(
      {String messageType,
      String consentStatus,
      String channelName,
      @ParseSfIdConverter() SfId channelId,
      bool enforceMessagingComponent});

  $SfIdCopyWith<$Res> get channelId;
}

/// @nodoc
class _$MessagingEndUserFilterCopyWithImpl<$Res>
    implements $MessagingEndUserFilterCopyWith<$Res> {
  _$MessagingEndUserFilterCopyWithImpl(this._self, this._then);

  final MessagingEndUserFilter _self;
  final $Res Function(MessagingEndUserFilter) _then;

  /// Create a copy of MessagingEndUserFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageType = null,
    Object? consentStatus = null,
    Object? channelName = null,
    Object? channelId = null,
    Object? enforceMessagingComponent = null,
  }) {
    return _then(_self.copyWith(
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      consentStatus: null == consentStatus
          ? _self.consentStatus
          : consentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      channelName: null == channelName
          ? _self.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String,
      channelId: null == channelId
          ? _self.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      enforceMessagingComponent: null == enforceMessagingComponent
          ? _self.enforceMessagingComponent
          : enforceMessagingComponent // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of MessagingEndUserFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get channelId {
    return $SfIdCopyWith<$Res>(_self.channelId, (value) {
      return _then(_self.copyWith(channelId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingEndUserFilter].
extension MessagingEndUserFilterPatterns on MessagingEndUserFilter {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingEndUserFilter value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFilter() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingEndUserFilter value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFilter():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingEndUserFilter value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFilter() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String messageType,
            String consentStatus,
            String channelName,
            @ParseSfIdConverter() SfId channelId,
            bool enforceMessagingComponent)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFilter() when $default != null:
        return $default(
            _that.messageType,
            _that.consentStatus,
            _that.channelName,
            _that.channelId,
            _that.enforceMessagingComponent);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String messageType,
            String consentStatus,
            String channelName,
            @ParseSfIdConverter() SfId channelId,
            bool enforceMessagingComponent)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFilter():
        return $default(
            _that.messageType,
            _that.consentStatus,
            _that.channelName,
            _that.channelId,
            _that.enforceMessagingComponent);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String messageType,
            String consentStatus,
            String channelName,
            @ParseSfIdConverter() SfId channelId,
            bool enforceMessagingComponent)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserFilter() when $default != null:
        return $default(
            _that.messageType,
            _that.consentStatus,
            _that.channelName,
            _that.channelId,
            _that.enforceMessagingComponent);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingEndUserFilter implements MessagingEndUserFilter {
  const _MessagingEndUserFilter(
      {required this.messageType,
      required this.consentStatus,
      required this.channelName,
      @ParseSfIdConverter() required this.channelId,
      required this.enforceMessagingComponent});
  factory _MessagingEndUserFilter.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserFilterFromJson(json);

  @override
  final String messageType;
  @override
  final String consentStatus;
  @override
  final String channelName;
  @override
  @ParseSfIdConverter()
  final SfId channelId;
  @override
  final bool enforceMessagingComponent;

  /// Create a copy of MessagingEndUserFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingEndUserFilterCopyWith<_MessagingEndUserFilter> get copyWith =>
      __$MessagingEndUserFilterCopyWithImpl<_MessagingEndUserFilter>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingEndUserFilterToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingEndUserFilter &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.consentStatus, consentStatus) ||
                other.consentStatus == consentStatus) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.channelId, channelId) ||
                other.channelId == channelId) &&
            (identical(other.enforceMessagingComponent,
                    enforceMessagingComponent) ||
                other.enforceMessagingComponent == enforceMessagingComponent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, messageType, consentStatus,
      channelName, channelId, enforceMessagingComponent);

  @override
  String toString() {
    return 'MessagingEndUserFilter(messageType: $messageType, consentStatus: $consentStatus, channelName: $channelName, channelId: $channelId, enforceMessagingComponent: $enforceMessagingComponent)';
  }
}

/// @nodoc
abstract mixin class _$MessagingEndUserFilterCopyWith<$Res>
    implements $MessagingEndUserFilterCopyWith<$Res> {
  factory _$MessagingEndUserFilterCopyWith(_MessagingEndUserFilter value,
          $Res Function(_MessagingEndUserFilter) _then) =
      __$MessagingEndUserFilterCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String messageType,
      String consentStatus,
      String channelName,
      @ParseSfIdConverter() SfId channelId,
      bool enforceMessagingComponent});

  @override
  $SfIdCopyWith<$Res> get channelId;
}

/// @nodoc
class __$MessagingEndUserFilterCopyWithImpl<$Res>
    implements _$MessagingEndUserFilterCopyWith<$Res> {
  __$MessagingEndUserFilterCopyWithImpl(this._self, this._then);

  final _MessagingEndUserFilter _self;
  final $Res Function(_MessagingEndUserFilter) _then;

  /// Create a copy of MessagingEndUserFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageType = null,
    Object? consentStatus = null,
    Object? channelName = null,
    Object? channelId = null,
    Object? enforceMessagingComponent = null,
  }) {
    return _then(_MessagingEndUserFilter(
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      consentStatus: null == consentStatus
          ? _self.consentStatus
          : consentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      channelName: null == channelName
          ? _self.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String,
      channelId: null == channelId
          ? _self.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      enforceMessagingComponent: null == enforceMessagingComponent
          ? _self.enforceMessagingComponent
          : enforceMessagingComponent // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of MessagingEndUserFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get channelId {
    return $SfIdCopyWith<$Res>(_self.channelId, (value) {
      return _then(_self.copyWith(channelId: value));
    });
  }
}

// dart format on

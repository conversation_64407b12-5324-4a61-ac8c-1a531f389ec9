// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definition_parameters.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingDefinitionParameters {
  @ParseSfIdConverter()
  SfId get id;
  List<String>? get parameters;

  /// Create a copy of MessagingDefinitionParameters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingDefinitionParametersCopyWith<MessagingDefinitionParameters>
      get copyWith => _$MessagingDefinitionParametersCopyWithImpl<
              MessagingDefinitionParameters>(
          this as MessagingDefinitionParameters, _$identity);

  /// Serializes this MessagingDefinitionParameters to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingDefinitionParameters &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other.parameters, parameters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(parameters));

  @override
  String toString() {
    return 'MessagingDefinitionParameters(id: $id, parameters: $parameters)';
  }
}

/// @nodoc
abstract mixin class $MessagingDefinitionParametersCopyWith<$Res> {
  factory $MessagingDefinitionParametersCopyWith(
          MessagingDefinitionParameters value,
          $Res Function(MessagingDefinitionParameters) _then) =
      _$MessagingDefinitionParametersCopyWithImpl;
  @useResult
  $Res call({@ParseSfIdConverter() SfId id, List<String>? parameters});

  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class _$MessagingDefinitionParametersCopyWithImpl<$Res>
    implements $MessagingDefinitionParametersCopyWith<$Res> {
  _$MessagingDefinitionParametersCopyWithImpl(this._self, this._then);

  final MessagingDefinitionParameters _self;
  final $Res Function(MessagingDefinitionParameters) _then;

  /// Create a copy of MessagingDefinitionParameters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? parameters = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      parameters: freezed == parameters
          ? _self.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }

  /// Create a copy of MessagingDefinitionParameters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingDefinitionParameters].
extension MessagingDefinitionParametersPatterns
    on MessagingDefinitionParameters {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingDefinitionParameters value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionParameters() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingDefinitionParameters value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionParameters():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingDefinitionParameters value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionParameters() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId id, List<String>? parameters)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionParameters() when $default != null:
        return $default(_that.id, _that.parameters);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId id, List<String>? parameters)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionParameters():
        return $default(_that.id, _that.parameters);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@ParseSfIdConverter() SfId id, List<String>? parameters)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionParameters() when $default != null:
        return $default(_that.id, _that.parameters);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingDefinitionParameters implements MessagingDefinitionParameters {
  const _MessagingDefinitionParameters(
      {@ParseSfIdConverter() required this.id, final List<String>? parameters})
      : _parameters = parameters;
  factory _MessagingDefinitionParameters.fromJson(Map<String, dynamic> json) =>
      _$MessagingDefinitionParametersFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId id;
  final List<String>? _parameters;
  @override
  List<String>? get parameters {
    final value = _parameters;
    if (value == null) return null;
    if (_parameters is EqualUnmodifiableListView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of MessagingDefinitionParameters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingDefinitionParametersCopyWith<_MessagingDefinitionParameters>
      get copyWith => __$MessagingDefinitionParametersCopyWithImpl<
          _MessagingDefinitionParameters>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingDefinitionParametersToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingDefinitionParameters &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other._parameters, _parameters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_parameters));

  @override
  String toString() {
    return 'MessagingDefinitionParameters(id: $id, parameters: $parameters)';
  }
}

/// @nodoc
abstract mixin class _$MessagingDefinitionParametersCopyWith<$Res>
    implements $MessagingDefinitionParametersCopyWith<$Res> {
  factory _$MessagingDefinitionParametersCopyWith(
          _MessagingDefinitionParameters value,
          $Res Function(_MessagingDefinitionParameters) _then) =
      __$MessagingDefinitionParametersCopyWithImpl;
  @override
  @useResult
  $Res call({@ParseSfIdConverter() SfId id, List<String>? parameters});

  @override
  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class __$MessagingDefinitionParametersCopyWithImpl<$Res>
    implements _$MessagingDefinitionParametersCopyWith<$Res> {
  __$MessagingDefinitionParametersCopyWithImpl(this._self, this._then);

  final _MessagingDefinitionParameters _self;
  final $Res Function(_MessagingDefinitionParameters) _then;

  /// Create a copy of MessagingDefinitionParameters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? parameters = freezed,
  }) {
    return _then(_MessagingDefinitionParameters(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      parameters: freezed == parameters
          ? _self._parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }

  /// Create a copy of MessagingDefinitionParameters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_channel_entry.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingChannelEntry {
  @JsonKey(name: 'id')
  @ParseSfIdConverter()
  SfId get channelId;
  String get messageType;
  String get name;
  bool get isFavorite;
  @ParseSfIdConverter()
  SfId? get messagingEndUserId;
  int? get messagingEndUserTimestamp;

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingChannelEntryCopyWith<MessagingChannelEntry> get copyWith =>
      _$MessagingChannelEntryCopyWithImpl<MessagingChannelEntry>(
          this as MessagingChannelEntry, _$identity);

  /// Serializes this MessagingChannelEntry to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingChannelEntry &&
            (identical(other.channelId, channelId) ||
                other.channelId == channelId) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingEndUserTimestamp,
                    messagingEndUserTimestamp) ||
                other.messagingEndUserTimestamp == messagingEndUserTimestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, channelId, messageType, name,
      isFavorite, messagingEndUserId, messagingEndUserTimestamp);

  @override
  String toString() {
    return 'MessagingChannelEntry(channelId: $channelId, messageType: $messageType, name: $name, isFavorite: $isFavorite, messagingEndUserId: $messagingEndUserId, messagingEndUserTimestamp: $messagingEndUserTimestamp)';
  }
}

/// @nodoc
abstract mixin class $MessagingChannelEntryCopyWith<$Res> {
  factory $MessagingChannelEntryCopyWith(MessagingChannelEntry value,
          $Res Function(MessagingChannelEntry) _then) =
      _$MessagingChannelEntryCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') @ParseSfIdConverter() SfId channelId,
      String messageType,
      String name,
      bool isFavorite,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      int? messagingEndUserTimestamp});

  $SfIdCopyWith<$Res> get channelId;
  $SfIdCopyWith<$Res>? get messagingEndUserId;
}

/// @nodoc
class _$MessagingChannelEntryCopyWithImpl<$Res>
    implements $MessagingChannelEntryCopyWith<$Res> {
  _$MessagingChannelEntryCopyWithImpl(this._self, this._then);

  final MessagingChannelEntry _self;
  final $Res Function(MessagingChannelEntry) _then;

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelId = null,
    Object? messageType = null,
    Object? name = null,
    Object? isFavorite = null,
    Object? messagingEndUserId = freezed,
    Object? messagingEndUserTimestamp = freezed,
  }) {
    return _then(_self.copyWith(
      channelId: null == channelId
          ? _self.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      isFavorite: null == isFavorite
          ? _self.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserTimestamp: freezed == messagingEndUserTimestamp
          ? _self.messagingEndUserTimestamp
          : messagingEndUserTimestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get channelId {
    return $SfIdCopyWith<$Res>(_self.channelId, (value) {
      return _then(_self.copyWith(channelId: value));
    });
  }

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_self.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUserId!, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingChannelEntry].
extension MessagingChannelEntryPatterns on MessagingChannelEntry {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingChannelEntry value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelEntry() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingChannelEntry value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelEntry():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingChannelEntry value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelEntry() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'id') @ParseSfIdConverter() SfId channelId,
            String messageType,
            String name,
            bool isFavorite,
            @ParseSfIdConverter() SfId? messagingEndUserId,
            int? messagingEndUserTimestamp)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelEntry() when $default != null:
        return $default(
            _that.channelId,
            _that.messageType,
            _that.name,
            _that.isFavorite,
            _that.messagingEndUserId,
            _that.messagingEndUserTimestamp);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'id') @ParseSfIdConverter() SfId channelId,
            String messageType,
            String name,
            bool isFavorite,
            @ParseSfIdConverter() SfId? messagingEndUserId,
            int? messagingEndUserTimestamp)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelEntry():
        return $default(
            _that.channelId,
            _that.messageType,
            _that.name,
            _that.isFavorite,
            _that.messagingEndUserId,
            _that.messagingEndUserTimestamp);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'id') @ParseSfIdConverter() SfId channelId,
            String messageType,
            String name,
            bool isFavorite,
            @ParseSfIdConverter() SfId? messagingEndUserId,
            int? messagingEndUserTimestamp)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingChannelEntry() when $default != null:
        return $default(
            _that.channelId,
            _that.messageType,
            _that.name,
            _that.isFavorite,
            _that.messagingEndUserId,
            _that.messagingEndUserTimestamp);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingChannelEntry extends MessagingChannelEntry {
  const _MessagingChannelEntry(
      {@JsonKey(name: 'id') @ParseSfIdConverter() required this.channelId,
      required this.messageType,
      required this.name,
      required this.isFavorite,
      @ParseSfIdConverter() this.messagingEndUserId,
      this.messagingEndUserTimestamp})
      : super._();
  factory _MessagingChannelEntry.fromJson(Map<String, dynamic> json) =>
      _$MessagingChannelEntryFromJson(json);

  @override
  @JsonKey(name: 'id')
  @ParseSfIdConverter()
  final SfId channelId;
  @override
  final String messageType;
  @override
  final String name;
  @override
  final bool isFavorite;
  @override
  @ParseSfIdConverter()
  final SfId? messagingEndUserId;
  @override
  final int? messagingEndUserTimestamp;

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingChannelEntryCopyWith<_MessagingChannelEntry> get copyWith =>
      __$MessagingChannelEntryCopyWithImpl<_MessagingChannelEntry>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingChannelEntryToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingChannelEntry &&
            (identical(other.channelId, channelId) ||
                other.channelId == channelId) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingEndUserTimestamp,
                    messagingEndUserTimestamp) ||
                other.messagingEndUserTimestamp == messagingEndUserTimestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, channelId, messageType, name,
      isFavorite, messagingEndUserId, messagingEndUserTimestamp);

  @override
  String toString() {
    return 'MessagingChannelEntry(channelId: $channelId, messageType: $messageType, name: $name, isFavorite: $isFavorite, messagingEndUserId: $messagingEndUserId, messagingEndUserTimestamp: $messagingEndUserTimestamp)';
  }
}

/// @nodoc
abstract mixin class _$MessagingChannelEntryCopyWith<$Res>
    implements $MessagingChannelEntryCopyWith<$Res> {
  factory _$MessagingChannelEntryCopyWith(_MessagingChannelEntry value,
          $Res Function(_MessagingChannelEntry) _then) =
      __$MessagingChannelEntryCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') @ParseSfIdConverter() SfId channelId,
      String messageType,
      String name,
      bool isFavorite,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      int? messagingEndUserTimestamp});

  @override
  $SfIdCopyWith<$Res> get channelId;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUserId;
}

/// @nodoc
class __$MessagingChannelEntryCopyWithImpl<$Res>
    implements _$MessagingChannelEntryCopyWith<$Res> {
  __$MessagingChannelEntryCopyWithImpl(this._self, this._then);

  final _MessagingChannelEntry _self;
  final $Res Function(_MessagingChannelEntry) _then;

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? channelId = null,
    Object? messageType = null,
    Object? name = null,
    Object? isFavorite = null,
    Object? messagingEndUserId = freezed,
    Object? messagingEndUserTimestamp = freezed,
  }) {
    return _then(_MessagingChannelEntry(
      channelId: null == channelId
          ? _self.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      isFavorite: null == isFavorite
          ? _self.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserTimestamp: freezed == messagingEndUserTimestamp
          ? _self.messagingEndUserTimestamp
          : messagingEndUserTimestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get channelId {
    return $SfIdCopyWith<$Res>(_self.channelId, (value) {
      return _then(_self.copyWith(channelId: value));
    });
  }

  /// Create a copy of MessagingChannelEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_self.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUserId!, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }
}

// dart format on

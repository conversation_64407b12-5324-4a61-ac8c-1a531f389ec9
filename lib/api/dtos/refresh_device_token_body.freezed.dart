// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refresh_device_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RefreshDeviceTokenBody {
  String get deviceToken;

  /// Create a copy of RefreshDeviceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RefreshDeviceTokenBodyCopyWith<RefreshDeviceTokenBody> get copyWith =>
      _$RefreshDeviceTokenBodyCopyWithImpl<RefreshDeviceTokenBody>(
          this as RefreshDeviceTokenBody, _$identity);

  /// Serializes this RefreshDeviceTokenBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RefreshDeviceTokenBody &&
            (identical(other.deviceToken, deviceToken) ||
                other.deviceToken == deviceToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, deviceToken);

  @override
  String toString() {
    return 'RefreshDeviceTokenBody(deviceToken: $deviceToken)';
  }
}

/// @nodoc
abstract mixin class $RefreshDeviceTokenBodyCopyWith<$Res> {
  factory $RefreshDeviceTokenBodyCopyWith(RefreshDeviceTokenBody value,
          $Res Function(RefreshDeviceTokenBody) _then) =
      _$RefreshDeviceTokenBodyCopyWithImpl;
  @useResult
  $Res call({String deviceToken});
}

/// @nodoc
class _$RefreshDeviceTokenBodyCopyWithImpl<$Res>
    implements $RefreshDeviceTokenBodyCopyWith<$Res> {
  _$RefreshDeviceTokenBodyCopyWithImpl(this._self, this._then);

  final RefreshDeviceTokenBody _self;
  final $Res Function(RefreshDeviceTokenBody) _then;

  /// Create a copy of RefreshDeviceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceToken = null,
  }) {
    return _then(_self.copyWith(
      deviceToken: null == deviceToken
          ? _self.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [RefreshDeviceTokenBody].
extension RefreshDeviceTokenBodyPatterns on RefreshDeviceTokenBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RefreshDeviceTokenBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RefreshDeviceTokenBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RefreshDeviceTokenBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshDeviceTokenBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RefreshDeviceTokenBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshDeviceTokenBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String deviceToken)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RefreshDeviceTokenBody() when $default != null:
        return $default(_that.deviceToken);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String deviceToken) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshDeviceTokenBody():
        return $default(_that.deviceToken);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String deviceToken)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RefreshDeviceTokenBody() when $default != null:
        return $default(_that.deviceToken);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RefreshDeviceTokenBody implements RefreshDeviceTokenBody {
  const _RefreshDeviceTokenBody({required this.deviceToken});
  factory _RefreshDeviceTokenBody.fromJson(Map<String, dynamic> json) =>
      _$RefreshDeviceTokenBodyFromJson(json);

  @override
  final String deviceToken;

  /// Create a copy of RefreshDeviceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RefreshDeviceTokenBodyCopyWith<_RefreshDeviceTokenBody> get copyWith =>
      __$RefreshDeviceTokenBodyCopyWithImpl<_RefreshDeviceTokenBody>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RefreshDeviceTokenBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RefreshDeviceTokenBody &&
            (identical(other.deviceToken, deviceToken) ||
                other.deviceToken == deviceToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, deviceToken);

  @override
  String toString() {
    return 'RefreshDeviceTokenBody(deviceToken: $deviceToken)';
  }
}

/// @nodoc
abstract mixin class _$RefreshDeviceTokenBodyCopyWith<$Res>
    implements $RefreshDeviceTokenBodyCopyWith<$Res> {
  factory _$RefreshDeviceTokenBodyCopyWith(_RefreshDeviceTokenBody value,
          $Res Function(_RefreshDeviceTokenBody) _then) =
      __$RefreshDeviceTokenBodyCopyWithImpl;
  @override
  @useResult
  $Res call({String deviceToken});
}

/// @nodoc
class __$RefreshDeviceTokenBodyCopyWithImpl<$Res>
    implements _$RefreshDeviceTokenBodyCopyWith<$Res> {
  __$RefreshDeviceTokenBodyCopyWithImpl(this._self, this._then);

  final _RefreshDeviceTokenBody _self;
  final $Res Function(_RefreshDeviceTokenBody) _then;

  /// Create a copy of RefreshDeviceTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? deviceToken = null,
  }) {
    return _then(_RefreshDeviceTokenBody(
      deviceToken: null == deviceToken
          ? _self.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

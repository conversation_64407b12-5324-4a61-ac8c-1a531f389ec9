// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quick_action.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_QuickAction _$QuickActionFromJson(Map json) => $checkedCreate(
      '_QuickAction',
      json,
      ($checkedConvert) {
        final val = _QuickAction(
          isResolvable:
              $checkedConvert('isResolvable', (v) => v as bool? ?? true),
          actionUrl: $checkedConvert('action_url__c', (v) => v as String?),
          actionName: $checkedConvert('action_name__c', (v) => v as String?),
          actionIcon: $checkedConvert('action_icon__c', (v) => v as String?),
          actionDescription:
              $checkedConvert('action_description__c', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'actionUrl': 'action_url__c',
        'actionName': 'action_name__c',
        'actionIcon': 'action_icon__c',
        'actionDescription': 'action_description__c'
      },
    );

Map<String, dynamic> _$QuickActionToJson(_QuickAction instance) =>
    <String, dynamic>{
      'isResolvable': instance.isResolvable,
      if (instance.actionUrl case final value?) 'action_url__c': value,
      if (instance.actionName case final value?) 'action_name__c': value,
      if (instance.actionIcon case final value?) 'action_icon__c': value,
      if (instance.actionDescription case final value?)
        'action_description__c': value,
    };

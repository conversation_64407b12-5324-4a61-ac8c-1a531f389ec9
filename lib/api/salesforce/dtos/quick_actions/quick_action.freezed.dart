// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quick_action.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuickAction implements DiagnosticableTreeMixin {
  bool get isResolvable;
  @JsonKey(name: 'action_url__c')
  String? get actionUrl;
  @JsonKey(name: 'action_name__c')
  String? get actionName;
  @JsonKey(name: 'action_icon__c')
  String? get actionIcon;
  @JsonKey(name: 'action_description__c')
  String? get actionDescription;

  /// Create a copy of QuickAction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuickActionCopyWith<QuickAction> get copyWith =>
      _$QuickActionCopyWithImpl<QuickAction>(this as QuickAction, _$identity);

  /// Serializes this QuickAction to a JSON map.
  Map<String, dynamic> toJson();

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'QuickAction'))
      ..add(DiagnosticsProperty('isResolvable', isResolvable))
      ..add(DiagnosticsProperty('actionUrl', actionUrl))
      ..add(DiagnosticsProperty('actionName', actionName))
      ..add(DiagnosticsProperty('actionIcon', actionIcon))
      ..add(DiagnosticsProperty('actionDescription', actionDescription));
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'QuickAction(isResolvable: $isResolvable, actionUrl: $actionUrl, actionName: $actionName, actionIcon: $actionIcon, actionDescription: $actionDescription)';
  }
}

/// @nodoc
abstract mixin class $QuickActionCopyWith<$Res> {
  factory $QuickActionCopyWith(
          QuickAction value, $Res Function(QuickAction) _then) =
      _$QuickActionCopyWithImpl;
  @useResult
  $Res call(
      {bool isResolvable,
      @JsonKey(name: 'action_url__c') String? actionUrl,
      @JsonKey(name: 'action_name__c') String? actionName,
      @JsonKey(name: 'action_icon__c') String? actionIcon,
      @JsonKey(name: 'action_description__c') String? actionDescription});
}

/// @nodoc
class _$QuickActionCopyWithImpl<$Res> implements $QuickActionCopyWith<$Res> {
  _$QuickActionCopyWithImpl(this._self, this._then);

  final QuickAction _self;
  final $Res Function(QuickAction) _then;

  /// Create a copy of QuickAction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isResolvable = null,
    Object? actionUrl = freezed,
    Object? actionName = freezed,
    Object? actionIcon = freezed,
    Object? actionDescription = freezed,
  }) {
    return _then(_self.copyWith(
      isResolvable: null == isResolvable
          ? _self.isResolvable
          : isResolvable // ignore: cast_nullable_to_non_nullable
              as bool,
      actionUrl: freezed == actionUrl
          ? _self.actionUrl
          : actionUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      actionName: freezed == actionName
          ? _self.actionName
          : actionName // ignore: cast_nullable_to_non_nullable
              as String?,
      actionIcon: freezed == actionIcon
          ? _self.actionIcon
          : actionIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      actionDescription: freezed == actionDescription
          ? _self.actionDescription
          : actionDescription // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [QuickAction].
extension QuickActionPatterns on QuickAction {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_QuickAction value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QuickAction() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_QuickAction value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QuickAction():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_QuickAction value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QuickAction() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool isResolvable,
            @JsonKey(name: 'action_url__c') String? actionUrl,
            @JsonKey(name: 'action_name__c') String? actionName,
            @JsonKey(name: 'action_icon__c') String? actionIcon,
            @JsonKey(name: 'action_description__c') String? actionDescription)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _QuickAction() when $default != null:
        return $default(_that.isResolvable, _that.actionUrl, _that.actionName,
            _that.actionIcon, _that.actionDescription);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool isResolvable,
            @JsonKey(name: 'action_url__c') String? actionUrl,
            @JsonKey(name: 'action_name__c') String? actionName,
            @JsonKey(name: 'action_icon__c') String? actionIcon,
            @JsonKey(name: 'action_description__c') String? actionDescription)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QuickAction():
        return $default(_that.isResolvable, _that.actionUrl, _that.actionName,
            _that.actionIcon, _that.actionDescription);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool isResolvable,
            @JsonKey(name: 'action_url__c') String? actionUrl,
            @JsonKey(name: 'action_name__c') String? actionName,
            @JsonKey(name: 'action_icon__c') String? actionIcon,
            @JsonKey(name: 'action_description__c') String? actionDescription)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _QuickAction() when $default != null:
        return $default(_that.isResolvable, _that.actionUrl, _that.actionName,
            _that.actionIcon, _that.actionDescription);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _QuickAction extends QuickAction with DiagnosticableTreeMixin {
  const _QuickAction(
      {this.isResolvable = true,
      @JsonKey(name: 'action_url__c') this.actionUrl,
      @JsonKey(name: 'action_name__c') this.actionName,
      @JsonKey(name: 'action_icon__c') this.actionIcon,
      @JsonKey(name: 'action_description__c') this.actionDescription})
      : super._();
  factory _QuickAction.fromJson(Map<String, dynamic> json) =>
      _$QuickActionFromJson(json);

  @override
  @JsonKey()
  final bool isResolvable;
  @override
  @JsonKey(name: 'action_url__c')
  final String? actionUrl;
  @override
  @JsonKey(name: 'action_name__c')
  final String? actionName;
  @override
  @JsonKey(name: 'action_icon__c')
  final String? actionIcon;
  @override
  @JsonKey(name: 'action_description__c')
  final String? actionDescription;

  /// Create a copy of QuickAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuickActionCopyWith<_QuickAction> get copyWith =>
      __$QuickActionCopyWithImpl<_QuickAction>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuickActionToJson(
      this,
    );
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'QuickAction'))
      ..add(DiagnosticsProperty('isResolvable', isResolvable))
      ..add(DiagnosticsProperty('actionUrl', actionUrl))
      ..add(DiagnosticsProperty('actionName', actionName))
      ..add(DiagnosticsProperty('actionIcon', actionIcon))
      ..add(DiagnosticsProperty('actionDescription', actionDescription));
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'QuickAction(isResolvable: $isResolvable, actionUrl: $actionUrl, actionName: $actionName, actionIcon: $actionIcon, actionDescription: $actionDescription)';
  }
}

/// @nodoc
abstract mixin class _$QuickActionCopyWith<$Res>
    implements $QuickActionCopyWith<$Res> {
  factory _$QuickActionCopyWith(
          _QuickAction value, $Res Function(_QuickAction) _then) =
      __$QuickActionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isResolvable,
      @JsonKey(name: 'action_url__c') String? actionUrl,
      @JsonKey(name: 'action_name__c') String? actionName,
      @JsonKey(name: 'action_icon__c') String? actionIcon,
      @JsonKey(name: 'action_description__c') String? actionDescription});
}

/// @nodoc
class __$QuickActionCopyWithImpl<$Res> implements _$QuickActionCopyWith<$Res> {
  __$QuickActionCopyWithImpl(this._self, this._then);

  final _QuickAction _self;
  final $Res Function(_QuickAction) _then;

  /// Create a copy of QuickAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isResolvable = null,
    Object? actionUrl = freezed,
    Object? actionName = freezed,
    Object? actionIcon = freezed,
    Object? actionDescription = freezed,
  }) {
    return _then(_QuickAction(
      isResolvable: null == isResolvable
          ? _self.isResolvable
          : isResolvable // ignore: cast_nullable_to_non_nullable
              as bool,
      actionUrl: freezed == actionUrl
          ? _self.actionUrl
          : actionUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      actionName: freezed == actionName
          ? _self.actionName
          : actionName // ignore: cast_nullable_to_non_nullable
              as String?,
      actionIcon: freezed == actionIcon
          ? _self.actionIcon
          : actionIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      actionDescription: freezed == actionDescription
          ? _self.actionDescription
          : actionDescription // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

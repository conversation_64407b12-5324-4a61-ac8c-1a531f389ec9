// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_records_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SearchRecordsResponse {
  List<SearchRecord> get searchRecords;

  /// Create a copy of SearchRecordsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SearchRecordsResponseCopyWith<SearchRecordsResponse> get copyWith =>
      _$SearchRecordsResponseCopyWithImpl<SearchRecordsResponse>(
          this as SearchRecordsResponse, _$identity);

  /// Serializes this SearchRecordsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SearchRecordsResponse &&
            const DeepCollectionEquality()
                .equals(other.searchRecords, searchRecords));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(searchRecords));

  @override
  String toString() {
    return 'SearchRecordsResponse(searchRecords: $searchRecords)';
  }
}

/// @nodoc
abstract mixin class $SearchRecordsResponseCopyWith<$Res> {
  factory $SearchRecordsResponseCopyWith(SearchRecordsResponse value,
          $Res Function(SearchRecordsResponse) _then) =
      _$SearchRecordsResponseCopyWithImpl;
  @useResult
  $Res call({List<SearchRecord> searchRecords});
}

/// @nodoc
class _$SearchRecordsResponseCopyWithImpl<$Res>
    implements $SearchRecordsResponseCopyWith<$Res> {
  _$SearchRecordsResponseCopyWithImpl(this._self, this._then);

  final SearchRecordsResponse _self;
  final $Res Function(SearchRecordsResponse) _then;

  /// Create a copy of SearchRecordsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchRecords = null,
  }) {
    return _then(_self.copyWith(
      searchRecords: null == searchRecords
          ? _self.searchRecords
          : searchRecords // ignore: cast_nullable_to_non_nullable
              as List<SearchRecord>,
    ));
  }
}

/// Adds pattern-matching-related methods to [SearchRecordsResponse].
extension SearchRecordsResponsePatterns on SearchRecordsResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SearchRecordsResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SearchRecordsResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SearchRecordsResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecordsResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SearchRecordsResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecordsResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List<SearchRecord> searchRecords)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SearchRecordsResponse() when $default != null:
        return $default(_that.searchRecords);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List<SearchRecord> searchRecords) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecordsResponse():
        return $default(_that.searchRecords);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List<SearchRecord> searchRecords)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecordsResponse() when $default != null:
        return $default(_that.searchRecords);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SearchRecordsResponse implements SearchRecordsResponse {
  const _SearchRecordsResponse(
      {required final List<SearchRecord> searchRecords})
      : _searchRecords = searchRecords;
  factory _SearchRecordsResponse.fromJson(Map<String, dynamic> json) =>
      _$SearchRecordsResponseFromJson(json);

  final List<SearchRecord> _searchRecords;
  @override
  List<SearchRecord> get searchRecords {
    if (_searchRecords is EqualUnmodifiableListView) return _searchRecords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_searchRecords);
  }

  /// Create a copy of SearchRecordsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchRecordsResponseCopyWith<_SearchRecordsResponse> get copyWith =>
      __$SearchRecordsResponseCopyWithImpl<_SearchRecordsResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SearchRecordsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SearchRecordsResponse &&
            const DeepCollectionEquality()
                .equals(other._searchRecords, _searchRecords));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_searchRecords));

  @override
  String toString() {
    return 'SearchRecordsResponse(searchRecords: $searchRecords)';
  }
}

/// @nodoc
abstract mixin class _$SearchRecordsResponseCopyWith<$Res>
    implements $SearchRecordsResponseCopyWith<$Res> {
  factory _$SearchRecordsResponseCopyWith(_SearchRecordsResponse value,
          $Res Function(_SearchRecordsResponse) _then) =
      __$SearchRecordsResponseCopyWithImpl;
  @override
  @useResult
  $Res call({List<SearchRecord> searchRecords});
}

/// @nodoc
class __$SearchRecordsResponseCopyWithImpl<$Res>
    implements _$SearchRecordsResponseCopyWith<$Res> {
  __$SearchRecordsResponseCopyWithImpl(this._self, this._then);

  final _SearchRecordsResponse _self;
  final $Res Function(_SearchRecordsResponse) _then;

  /// Create a copy of SearchRecordsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? searchRecords = null,
  }) {
    return _then(_SearchRecordsResponse(
      searchRecords: null == searchRecords
          ? _self._searchRecords
          : searchRecords // ignore: cast_nullable_to_non_nullable
              as List<SearchRecord>,
    ));
  }
}

/// @nodoc
mixin _$SearchRecord {
  Attributes get attributes;
  @JsonKey(name: 'Name')
  String? get name;
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @JsonKey(name: 'LastName')
  String? get lastName;
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl;
  @JsonKey(name: 'Id')
  String? get id;
  @JsonKey(name: 'Email')
  String? get email;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @JsonKey(name: 'Title')
  String? get title;

  /// Create a copy of SearchRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SearchRecordCopyWith<SearchRecord> get copyWith =>
      _$SearchRecordCopyWithImpl<SearchRecord>(
          this as SearchRecord, _$identity);

  /// Serializes this SearchRecord to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SearchRecord &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, attributes, name, firstName,
      lastName, photoUrl, id, email, mobilePhone, title);

  @override
  String toString() {
    return 'SearchRecord(attributes: $attributes, name: $name, firstName: $firstName, lastName: $lastName, photoUrl: $photoUrl, id: $id, email: $email, mobilePhone: $mobilePhone, title: $title)';
  }
}

/// @nodoc
abstract mixin class $SearchRecordCopyWith<$Res> {
  factory $SearchRecordCopyWith(
          SearchRecord value, $Res Function(SearchRecord) _then) =
      _$SearchRecordCopyWithImpl;
  @useResult
  $Res call(
      {Attributes attributes,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Title') String? title});

  $AttributesCopyWith<$Res> get attributes;
}

/// @nodoc
class _$SearchRecordCopyWithImpl<$Res> implements $SearchRecordCopyWith<$Res> {
  _$SearchRecordCopyWithImpl(this._self, this._then);

  final SearchRecord _self;
  final $Res Function(SearchRecord) _then;

  /// Create a copy of SearchRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = null,
    Object? name = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? photoUrl = freezed,
    Object? id = freezed,
    Object? email = freezed,
    Object? mobilePhone = freezed,
    Object? title = freezed,
  }) {
    return _then(_self.copyWith(
      attributes: null == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of SearchRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res> get attributes {
    return $AttributesCopyWith<$Res>(_self.attributes, (value) {
      return _then(_self.copyWith(attributes: value));
    });
  }
}

/// Adds pattern-matching-related methods to [SearchRecord].
extension SearchRecordPatterns on SearchRecord {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SearchRecord value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SearchRecord() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SearchRecord value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecord():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SearchRecord value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecord() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Attributes attributes,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Title') String? title)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SearchRecord() when $default != null:
        return $default(
            _that.attributes,
            _that.name,
            _that.firstName,
            _that.lastName,
            _that.photoUrl,
            _that.id,
            _that.email,
            _that.mobilePhone,
            _that.title);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Attributes attributes,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Title') String? title)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecord():
        return $default(
            _that.attributes,
            _that.name,
            _that.firstName,
            _that.lastName,
            _that.photoUrl,
            _that.id,
            _that.email,
            _that.mobilePhone,
            _that.title);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Attributes attributes,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Title') String? title)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchRecord() when $default != null:
        return $default(
            _that.attributes,
            _that.name,
            _that.firstName,
            _that.lastName,
            _that.photoUrl,
            _that.id,
            _that.email,
            _that.mobilePhone,
            _that.title);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SearchRecord implements SearchRecord {
  const _SearchRecord(
      {required this.attributes,
      @JsonKey(name: 'Name') this.name,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'PhotoUrl') this.photoUrl,
      @JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'Title') this.title});
  factory _SearchRecord.fromJson(Map<String, dynamic> json) =>
      _$SearchRecordFromJson(json);

  @override
  final Attributes attributes;
  @override
  @JsonKey(name: 'Name')
  final String? name;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'PhotoUrl')
  final String? photoUrl;
  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'Title')
  final String? title;

  /// Create a copy of SearchRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchRecordCopyWith<_SearchRecord> get copyWith =>
      __$SearchRecordCopyWithImpl<_SearchRecord>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SearchRecordToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SearchRecord &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, attributes, name, firstName,
      lastName, photoUrl, id, email, mobilePhone, title);

  @override
  String toString() {
    return 'SearchRecord(attributes: $attributes, name: $name, firstName: $firstName, lastName: $lastName, photoUrl: $photoUrl, id: $id, email: $email, mobilePhone: $mobilePhone, title: $title)';
  }
}

/// @nodoc
abstract mixin class _$SearchRecordCopyWith<$Res>
    implements $SearchRecordCopyWith<$Res> {
  factory _$SearchRecordCopyWith(
          _SearchRecord value, $Res Function(_SearchRecord) _then) =
      __$SearchRecordCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Attributes attributes,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Title') String? title});

  @override
  $AttributesCopyWith<$Res> get attributes;
}

/// @nodoc
class __$SearchRecordCopyWithImpl<$Res>
    implements _$SearchRecordCopyWith<$Res> {
  __$SearchRecordCopyWithImpl(this._self, this._then);

  final _SearchRecord _self;
  final $Res Function(_SearchRecord) _then;

  /// Create a copy of SearchRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? attributes = null,
    Object? name = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? photoUrl = freezed,
    Object? id = freezed,
    Object? email = freezed,
    Object? mobilePhone = freezed,
    Object? title = freezed,
  }) {
    return _then(_SearchRecord(
      attributes: null == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of SearchRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res> get attributes {
    return $AttributesCopyWith<$Res>(_self.attributes, (value) {
      return _then(_self.copyWith(attributes: value));
    });
  }
}

/// @nodoc
mixin _$Attributes {
  String get type;
  String get url;

  /// Create a copy of Attributes
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<Attributes> get copyWith =>
      _$AttributesCopyWithImpl<Attributes>(this as Attributes, _$identity);

  /// Serializes this Attributes to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Attributes &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, url);

  @override
  String toString() {
    return 'Attributes(type: $type, url: $url)';
  }
}

/// @nodoc
abstract mixin class $AttributesCopyWith<$Res> {
  factory $AttributesCopyWith(
          Attributes value, $Res Function(Attributes) _then) =
      _$AttributesCopyWithImpl;
  @useResult
  $Res call({String type, String url});
}

/// @nodoc
class _$AttributesCopyWithImpl<$Res> implements $AttributesCopyWith<$Res> {
  _$AttributesCopyWithImpl(this._self, this._then);

  final Attributes _self;
  final $Res Function(Attributes) _then;

  /// Create a copy of Attributes
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? url = null,
  }) {
    return _then(_self.copyWith(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [Attributes].
extension AttributesPatterns on Attributes {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Attributes value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Attributes() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Attributes value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Attributes():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Attributes value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Attributes() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String type, String url)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Attributes() when $default != null:
        return $default(_that.type, _that.url);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String type, String url) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Attributes():
        return $default(_that.type, _that.url);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String type, String url)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Attributes() when $default != null:
        return $default(_that.type, _that.url);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Attributes implements Attributes {
  const _Attributes({required this.type, required this.url});
  factory _Attributes.fromJson(Map<String, dynamic> json) =>
      _$AttributesFromJson(json);

  @override
  final String type;
  @override
  final String url;

  /// Create a copy of Attributes
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AttributesCopyWith<_Attributes> get copyWith =>
      __$AttributesCopyWithImpl<_Attributes>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AttributesToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Attributes &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, url);

  @override
  String toString() {
    return 'Attributes(type: $type, url: $url)';
  }
}

/// @nodoc
abstract mixin class _$AttributesCopyWith<$Res>
    implements $AttributesCopyWith<$Res> {
  factory _$AttributesCopyWith(
          _Attributes value, $Res Function(_Attributes) _then) =
      __$AttributesCopyWithImpl;
  @override
  @useResult
  $Res call({String type, String url});
}

/// @nodoc
class __$AttributesCopyWithImpl<$Res> implements _$AttributesCopyWith<$Res> {
  __$AttributesCopyWithImpl(this._self, this._then);

  final _Attributes _self;
  final $Res Function(_Attributes) _then;

  /// Create a copy of Attributes
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? type = null,
    Object? url = null,
  }) {
    return _then(_Attributes(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

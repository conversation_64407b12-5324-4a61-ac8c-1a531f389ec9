// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'case.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Case {
  String? get id;
  String? get status;
  String? get caseNumber;

  /// Create a copy of Case
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CaseCopyWith<Case> get copyWith =>
      _$CaseCopyWithImpl<Case>(this as Case, _$identity);

  /// Serializes this Case to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Case &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.caseNumber, caseNumber) ||
                other.caseNumber == caseNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, status, caseNumber);

  @override
  String toString() {
    return 'Case(id: $id, status: $status, caseNumber: $caseNumber)';
  }
}

/// @nodoc
abstract mixin class $CaseCopyWith<$Res> {
  factory $CaseCopyWith(Case value, $Res Function(Case) _then) =
      _$CaseCopyWithImpl;
  @useResult
  $Res call({String? id, String? status, String? caseNumber});
}

/// @nodoc
class _$CaseCopyWithImpl<$Res> implements $CaseCopyWith<$Res> {
  _$CaseCopyWithImpl(this._self, this._then);

  final Case _self;
  final $Res Function(Case) _then;

  /// Create a copy of Case
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? status = freezed,
    Object? caseNumber = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      caseNumber: freezed == caseNumber
          ? _self.caseNumber
          : caseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [Case].
extension CasePatterns on Case {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Case value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Case() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Case value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Case():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Case value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Case() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? id, String? status, String? caseNumber)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Case() when $default != null:
        return $default(_that.id, _that.status, _that.caseNumber);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? id, String? status, String? caseNumber) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Case():
        return $default(_that.id, _that.status, _that.caseNumber);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? id, String? status, String? caseNumber)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Case() when $default != null:
        return $default(_that.id, _that.status, _that.caseNumber);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Case implements Case {
  const _Case({this.id, this.status, this.caseNumber});
  factory _Case.fromJson(Map<String, dynamic> json) => _$CaseFromJson(json);

  @override
  final String? id;
  @override
  final String? status;
  @override
  final String? caseNumber;

  /// Create a copy of Case
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CaseCopyWith<_Case> get copyWith =>
      __$CaseCopyWithImpl<_Case>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CaseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Case &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.caseNumber, caseNumber) ||
                other.caseNumber == caseNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, status, caseNumber);

  @override
  String toString() {
    return 'Case(id: $id, status: $status, caseNumber: $caseNumber)';
  }
}

/// @nodoc
abstract mixin class _$CaseCopyWith<$Res> implements $CaseCopyWith<$Res> {
  factory _$CaseCopyWith(_Case value, $Res Function(_Case) _then) =
      __$CaseCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, String? status, String? caseNumber});
}

/// @nodoc
class __$CaseCopyWithImpl<$Res> implements _$CaseCopyWith<$Res> {
  __$CaseCopyWithImpl(this._self, this._then);

  final _Case _self;
  final $Res Function(_Case) _then;

  /// Create a copy of Case
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? status = freezed,
    Object? caseNumber = freezed,
  }) {
    return _then(_Case(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      caseNumber: freezed == caseNumber
          ? _self.caseNumber
          : caseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

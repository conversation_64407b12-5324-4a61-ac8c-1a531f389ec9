// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'content_version_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContentVersionResponse {
  @JsonKey(name: 'Id')
  String? get id;
  @JsonKey(name: 'ContentDocumentId')
  String? get contentDocumentId;
  @JsonKey(name: 'IsLatest')
  bool? get isLatest;
  @JsonKey(name: 'ContentBodyId')
  String? get contentBodyId;
  @JsonKey(name: 'VersionNumber')
  String? get versionNumber;
  @JsonKey(name: 'Title')
  String? get title;
  @JsonKey(name: 'SharingOption')
  String? get sharingOption;
  @JsonKey(name: 'SharingPrivacy')
  String? get sharingPrivacy;
  @JsonKey(name: 'PathOnClient')
  String? get pathOnClient;
  @JsonKey(name: 'RatingCount')
  int? get ratingCount;
  @JsonKey(name: 'IsDeleted')
  bool? get isDeleted;
  @JsonKey(name: 'ContentModifiedDate')
  String? get contentModifiedDate;
  @JsonKey(name: 'ContentModifiedById')
  String? get contentModifiedById;
  @JsonKey(name: 'Language')
  String? get language;
  @JsonKey(name: 'PositiveRatingCount')
  int? get positiveRatingCount;
  @JsonKey(name: 'NegativeRatingCount')
  int? get negativeRatingCount;
  @JsonKey(name: 'OwnerId')
  String? get ownerId;
  @JsonKey(name: 'CreatedById')
  String? get createdById;
  @JsonKey(name: 'CreatedDate')
  String? get createdDate;
  @JsonKey(name: 'LastModifiedById')
  String? get lastModifiedById;
  @JsonKey(name: 'LastModifiedDate')
  String? get lastModifiedDate;
  @JsonKey(name: 'SystemModstamp')
  String? get systemModstamp;
  @JsonKey(name: 'FileType')
  String? get fileType;
  @JsonKey(name: 'PublishStatus')
  String? get publishStatus;
  @JsonKey(name: 'VersionData')
  String? get versionData;
  @JsonKey(name: 'ContentSize')
  int? get contentSize;
  @JsonKey(name: 'FileExtension')
  String? get fileExtension;
  @JsonKey(name: 'FirstPublishLocationId')
  String? get firstPublishLocationId;
  @JsonKey(name: 'Origin')
  String? get origin;
  @JsonKey(name: 'ContentLocation')
  String? get contentLocation;
  @JsonKey(name: 'VersionDataUrl')
  String? get versionDataUrl;

  /// Create a copy of ContentVersionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContentVersionResponseCopyWith<ContentVersionResponse> get copyWith =>
      _$ContentVersionResponseCopyWithImpl<ContentVersionResponse>(
          this as ContentVersionResponse, _$identity);

  /// Serializes this ContentVersionResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContentVersionResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contentDocumentId, contentDocumentId) ||
                other.contentDocumentId == contentDocumentId) &&
            (identical(other.isLatest, isLatest) ||
                other.isLatest == isLatest) &&
            (identical(other.contentBodyId, contentBodyId) ||
                other.contentBodyId == contentBodyId) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.sharingOption, sharingOption) ||
                other.sharingOption == sharingOption) &&
            (identical(other.sharingPrivacy, sharingPrivacy) ||
                other.sharingPrivacy == sharingPrivacy) &&
            (identical(other.pathOnClient, pathOnClient) ||
                other.pathOnClient == pathOnClient) &&
            (identical(other.ratingCount, ratingCount) ||
                other.ratingCount == ratingCount) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.contentModifiedDate, contentModifiedDate) ||
                other.contentModifiedDate == contentModifiedDate) &&
            (identical(other.contentModifiedById, contentModifiedById) ||
                other.contentModifiedById == contentModifiedById) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.positiveRatingCount, positiveRatingCount) ||
                other.positiveRatingCount == positiveRatingCount) &&
            (identical(other.negativeRatingCount, negativeRatingCount) ||
                other.negativeRatingCount == negativeRatingCount) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.createdById, createdById) ||
                other.createdById == createdById) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.lastModifiedById, lastModifiedById) ||
                other.lastModifiedById == lastModifiedById) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.systemModstamp, systemModstamp) ||
                other.systemModstamp == systemModstamp) &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.versionData, versionData) ||
                other.versionData == versionData) &&
            (identical(other.contentSize, contentSize) ||
                other.contentSize == contentSize) &&
            (identical(other.fileExtension, fileExtension) ||
                other.fileExtension == fileExtension) &&
            (identical(other.firstPublishLocationId, firstPublishLocationId) ||
                other.firstPublishLocationId == firstPublishLocationId) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.contentLocation, contentLocation) ||
                other.contentLocation == contentLocation) &&
            (identical(other.versionDataUrl, versionDataUrl) ||
                other.versionDataUrl == versionDataUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        contentDocumentId,
        isLatest,
        contentBodyId,
        versionNumber,
        title,
        sharingOption,
        sharingPrivacy,
        pathOnClient,
        ratingCount,
        isDeleted,
        contentModifiedDate,
        contentModifiedById,
        language,
        positiveRatingCount,
        negativeRatingCount,
        ownerId,
        createdById,
        createdDate,
        lastModifiedById,
        lastModifiedDate,
        systemModstamp,
        fileType,
        publishStatus,
        versionData,
        contentSize,
        fileExtension,
        firstPublishLocationId,
        origin,
        contentLocation,
        versionDataUrl
      ]);

  @override
  String toString() {
    return 'ContentVersionResponse(id: $id, contentDocumentId: $contentDocumentId, isLatest: $isLatest, contentBodyId: $contentBodyId, versionNumber: $versionNumber, title: $title, sharingOption: $sharingOption, sharingPrivacy: $sharingPrivacy, pathOnClient: $pathOnClient, ratingCount: $ratingCount, isDeleted: $isDeleted, contentModifiedDate: $contentModifiedDate, contentModifiedById: $contentModifiedById, language: $language, positiveRatingCount: $positiveRatingCount, negativeRatingCount: $negativeRatingCount, ownerId: $ownerId, createdById: $createdById, createdDate: $createdDate, lastModifiedById: $lastModifiedById, lastModifiedDate: $lastModifiedDate, systemModstamp: $systemModstamp, fileType: $fileType, publishStatus: $publishStatus, versionData: $versionData, contentSize: $contentSize, fileExtension: $fileExtension, firstPublishLocationId: $firstPublishLocationId, origin: $origin, contentLocation: $contentLocation, versionDataUrl: $versionDataUrl)';
  }
}

/// @nodoc
abstract mixin class $ContentVersionResponseCopyWith<$Res> {
  factory $ContentVersionResponseCopyWith(ContentVersionResponse value,
          $Res Function(ContentVersionResponse) _then) =
      _$ContentVersionResponseCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
      @JsonKey(name: 'IsLatest') bool? isLatest,
      @JsonKey(name: 'ContentBodyId') String? contentBodyId,
      @JsonKey(name: 'VersionNumber') String? versionNumber,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'SharingOption') String? sharingOption,
      @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
      @JsonKey(name: 'PathOnClient') String? pathOnClient,
      @JsonKey(name: 'RatingCount') int? ratingCount,
      @JsonKey(name: 'IsDeleted') bool? isDeleted,
      @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
      @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
      @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'CreatedById') String? createdById,
      @JsonKey(name: 'CreatedDate') String? createdDate,
      @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'SystemModstamp') String? systemModstamp,
      @JsonKey(name: 'FileType') String? fileType,
      @JsonKey(name: 'PublishStatus') String? publishStatus,
      @JsonKey(name: 'VersionData') String? versionData,
      @JsonKey(name: 'ContentSize') int? contentSize,
      @JsonKey(name: 'FileExtension') String? fileExtension,
      @JsonKey(name: 'FirstPublishLocationId') String? firstPublishLocationId,
      @JsonKey(name: 'Origin') String? origin,
      @JsonKey(name: 'ContentLocation') String? contentLocation,
      @JsonKey(name: 'VersionDataUrl') String? versionDataUrl});
}

/// @nodoc
class _$ContentVersionResponseCopyWithImpl<$Res>
    implements $ContentVersionResponseCopyWith<$Res> {
  _$ContentVersionResponseCopyWithImpl(this._self, this._then);

  final ContentVersionResponse _self;
  final $Res Function(ContentVersionResponse) _then;

  /// Create a copy of ContentVersionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? contentDocumentId = freezed,
    Object? isLatest = freezed,
    Object? contentBodyId = freezed,
    Object? versionNumber = freezed,
    Object? title = freezed,
    Object? sharingOption = freezed,
    Object? sharingPrivacy = freezed,
    Object? pathOnClient = freezed,
    Object? ratingCount = freezed,
    Object? isDeleted = freezed,
    Object? contentModifiedDate = freezed,
    Object? contentModifiedById = freezed,
    Object? language = freezed,
    Object? positiveRatingCount = freezed,
    Object? negativeRatingCount = freezed,
    Object? ownerId = freezed,
    Object? createdById = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedById = freezed,
    Object? lastModifiedDate = freezed,
    Object? systemModstamp = freezed,
    Object? fileType = freezed,
    Object? publishStatus = freezed,
    Object? versionData = freezed,
    Object? contentSize = freezed,
    Object? fileExtension = freezed,
    Object? firstPublishLocationId = freezed,
    Object? origin = freezed,
    Object? contentLocation = freezed,
    Object? versionDataUrl = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      contentDocumentId: freezed == contentDocumentId
          ? _self.contentDocumentId
          : contentDocumentId // ignore: cast_nullable_to_non_nullable
              as String?,
      isLatest: freezed == isLatest
          ? _self.isLatest
          : isLatest // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentBodyId: freezed == contentBodyId
          ? _self.contentBodyId
          : contentBodyId // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _self.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingOption: freezed == sharingOption
          ? _self.sharingOption
          : sharingOption // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingPrivacy: freezed == sharingPrivacy
          ? _self.sharingPrivacy
          : sharingPrivacy // ignore: cast_nullable_to_non_nullable
              as String?,
      pathOnClient: freezed == pathOnClient
          ? _self.pathOnClient
          : pathOnClient // ignore: cast_nullable_to_non_nullable
              as String?,
      ratingCount: freezed == ratingCount
          ? _self.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isDeleted: freezed == isDeleted
          ? _self.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentModifiedDate: freezed == contentModifiedDate
          ? _self.contentModifiedDate
          : contentModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contentModifiedById: freezed == contentModifiedById
          ? _self.contentModifiedById
          : contentModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      positiveRatingCount: freezed == positiveRatingCount
          ? _self.positiveRatingCount
          : positiveRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      negativeRatingCount: freezed == negativeRatingCount
          ? _self.negativeRatingCount
          : negativeRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdById: freezed == createdById
          ? _self.createdById
          : createdById // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedById: freezed == lastModifiedById
          ? _self.lastModifiedById
          : lastModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      systemModstamp: freezed == systemModstamp
          ? _self.systemModstamp
          : systemModstamp // ignore: cast_nullable_to_non_nullable
              as String?,
      fileType: freezed == fileType
          ? _self.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String?,
      publishStatus: freezed == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      versionData: freezed == versionData
          ? _self.versionData
          : versionData // ignore: cast_nullable_to_non_nullable
              as String?,
      contentSize: freezed == contentSize
          ? _self.contentSize
          : contentSize // ignore: cast_nullable_to_non_nullable
              as int?,
      fileExtension: freezed == fileExtension
          ? _self.fileExtension
          : fileExtension // ignore: cast_nullable_to_non_nullable
              as String?,
      firstPublishLocationId: freezed == firstPublishLocationId
          ? _self.firstPublishLocationId
          : firstPublishLocationId // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _self.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as String?,
      contentLocation: freezed == contentLocation
          ? _self.contentLocation
          : contentLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      versionDataUrl: freezed == versionDataUrl
          ? _self.versionDataUrl
          : versionDataUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContentVersionResponse].
extension ContentVersionResponsePatterns on ContentVersionResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContentVersionResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContentVersionResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContentVersionResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContentVersionResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContentVersionResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContentVersionResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
            @JsonKey(name: 'IsLatest') bool? isLatest,
            @JsonKey(name: 'ContentBodyId') String? contentBodyId,
            @JsonKey(name: 'VersionNumber') String? versionNumber,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'SharingOption') String? sharingOption,
            @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
            @JsonKey(name: 'PathOnClient') String? pathOnClient,
            @JsonKey(name: 'RatingCount') int? ratingCount,
            @JsonKey(name: 'IsDeleted') bool? isDeleted,
            @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
            @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
            @JsonKey(name: 'Language') String? language,
            @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
            @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
            @JsonKey(name: 'OwnerId') String? ownerId,
            @JsonKey(name: 'CreatedById') String? createdById,
            @JsonKey(name: 'CreatedDate') String? createdDate,
            @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
            @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
            @JsonKey(name: 'SystemModstamp') String? systemModstamp,
            @JsonKey(name: 'FileType') String? fileType,
            @JsonKey(name: 'PublishStatus') String? publishStatus,
            @JsonKey(name: 'VersionData') String? versionData,
            @JsonKey(name: 'ContentSize') int? contentSize,
            @JsonKey(name: 'FileExtension') String? fileExtension,
            @JsonKey(name: 'FirstPublishLocationId')
            String? firstPublishLocationId,
            @JsonKey(name: 'Origin') String? origin,
            @JsonKey(name: 'ContentLocation') String? contentLocation,
            @JsonKey(name: 'VersionDataUrl') String? versionDataUrl)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContentVersionResponse() when $default != null:
        return $default(
            _that.id,
            _that.contentDocumentId,
            _that.isLatest,
            _that.contentBodyId,
            _that.versionNumber,
            _that.title,
            _that.sharingOption,
            _that.sharingPrivacy,
            _that.pathOnClient,
            _that.ratingCount,
            _that.isDeleted,
            _that.contentModifiedDate,
            _that.contentModifiedById,
            _that.language,
            _that.positiveRatingCount,
            _that.negativeRatingCount,
            _that.ownerId,
            _that.createdById,
            _that.createdDate,
            _that.lastModifiedById,
            _that.lastModifiedDate,
            _that.systemModstamp,
            _that.fileType,
            _that.publishStatus,
            _that.versionData,
            _that.contentSize,
            _that.fileExtension,
            _that.firstPublishLocationId,
            _that.origin,
            _that.contentLocation,
            _that.versionDataUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
            @JsonKey(name: 'IsLatest') bool? isLatest,
            @JsonKey(name: 'ContentBodyId') String? contentBodyId,
            @JsonKey(name: 'VersionNumber') String? versionNumber,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'SharingOption') String? sharingOption,
            @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
            @JsonKey(name: 'PathOnClient') String? pathOnClient,
            @JsonKey(name: 'RatingCount') int? ratingCount,
            @JsonKey(name: 'IsDeleted') bool? isDeleted,
            @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
            @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
            @JsonKey(name: 'Language') String? language,
            @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
            @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
            @JsonKey(name: 'OwnerId') String? ownerId,
            @JsonKey(name: 'CreatedById') String? createdById,
            @JsonKey(name: 'CreatedDate') String? createdDate,
            @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
            @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
            @JsonKey(name: 'SystemModstamp') String? systemModstamp,
            @JsonKey(name: 'FileType') String? fileType,
            @JsonKey(name: 'PublishStatus') String? publishStatus,
            @JsonKey(name: 'VersionData') String? versionData,
            @JsonKey(name: 'ContentSize') int? contentSize,
            @JsonKey(name: 'FileExtension') String? fileExtension,
            @JsonKey(name: 'FirstPublishLocationId')
            String? firstPublishLocationId,
            @JsonKey(name: 'Origin') String? origin,
            @JsonKey(name: 'ContentLocation') String? contentLocation,
            @JsonKey(name: 'VersionDataUrl') String? versionDataUrl)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContentVersionResponse():
        return $default(
            _that.id,
            _that.contentDocumentId,
            _that.isLatest,
            _that.contentBodyId,
            _that.versionNumber,
            _that.title,
            _that.sharingOption,
            _that.sharingPrivacy,
            _that.pathOnClient,
            _that.ratingCount,
            _that.isDeleted,
            _that.contentModifiedDate,
            _that.contentModifiedById,
            _that.language,
            _that.positiveRatingCount,
            _that.negativeRatingCount,
            _that.ownerId,
            _that.createdById,
            _that.createdDate,
            _that.lastModifiedById,
            _that.lastModifiedDate,
            _that.systemModstamp,
            _that.fileType,
            _that.publishStatus,
            _that.versionData,
            _that.contentSize,
            _that.fileExtension,
            _that.firstPublishLocationId,
            _that.origin,
            _that.contentLocation,
            _that.versionDataUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
            @JsonKey(name: 'IsLatest') bool? isLatest,
            @JsonKey(name: 'ContentBodyId') String? contentBodyId,
            @JsonKey(name: 'VersionNumber') String? versionNumber,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'SharingOption') String? sharingOption,
            @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
            @JsonKey(name: 'PathOnClient') String? pathOnClient,
            @JsonKey(name: 'RatingCount') int? ratingCount,
            @JsonKey(name: 'IsDeleted') bool? isDeleted,
            @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
            @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
            @JsonKey(name: 'Language') String? language,
            @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
            @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
            @JsonKey(name: 'OwnerId') String? ownerId,
            @JsonKey(name: 'CreatedById') String? createdById,
            @JsonKey(name: 'CreatedDate') String? createdDate,
            @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
            @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
            @JsonKey(name: 'SystemModstamp') String? systemModstamp,
            @JsonKey(name: 'FileType') String? fileType,
            @JsonKey(name: 'PublishStatus') String? publishStatus,
            @JsonKey(name: 'VersionData') String? versionData,
            @JsonKey(name: 'ContentSize') int? contentSize,
            @JsonKey(name: 'FileExtension') String? fileExtension,
            @JsonKey(name: 'FirstPublishLocationId')
            String? firstPublishLocationId,
            @JsonKey(name: 'Origin') String? origin,
            @JsonKey(name: 'ContentLocation') String? contentLocation,
            @JsonKey(name: 'VersionDataUrl') String? versionDataUrl)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContentVersionResponse() when $default != null:
        return $default(
            _that.id,
            _that.contentDocumentId,
            _that.isLatest,
            _that.contentBodyId,
            _that.versionNumber,
            _that.title,
            _that.sharingOption,
            _that.sharingPrivacy,
            _that.pathOnClient,
            _that.ratingCount,
            _that.isDeleted,
            _that.contentModifiedDate,
            _that.contentModifiedById,
            _that.language,
            _that.positiveRatingCount,
            _that.negativeRatingCount,
            _that.ownerId,
            _that.createdById,
            _that.createdDate,
            _that.lastModifiedById,
            _that.lastModifiedDate,
            _that.systemModstamp,
            _that.fileType,
            _that.publishStatus,
            _that.versionData,
            _that.contentSize,
            _that.fileExtension,
            _that.firstPublishLocationId,
            _that.origin,
            _that.contentLocation,
            _that.versionDataUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContentVersionResponse implements ContentVersionResponse {
  const _ContentVersionResponse(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'ContentDocumentId') this.contentDocumentId,
      @JsonKey(name: 'IsLatest') this.isLatest,
      @JsonKey(name: 'ContentBodyId') this.contentBodyId,
      @JsonKey(name: 'VersionNumber') this.versionNumber,
      @JsonKey(name: 'Title') this.title,
      @JsonKey(name: 'SharingOption') this.sharingOption,
      @JsonKey(name: 'SharingPrivacy') this.sharingPrivacy,
      @JsonKey(name: 'PathOnClient') this.pathOnClient,
      @JsonKey(name: 'RatingCount') this.ratingCount,
      @JsonKey(name: 'IsDeleted') this.isDeleted,
      @JsonKey(name: 'ContentModifiedDate') this.contentModifiedDate,
      @JsonKey(name: 'ContentModifiedById') this.contentModifiedById,
      @JsonKey(name: 'Language') this.language,
      @JsonKey(name: 'PositiveRatingCount') this.positiveRatingCount,
      @JsonKey(name: 'NegativeRatingCount') this.negativeRatingCount,
      @JsonKey(name: 'OwnerId') this.ownerId,
      @JsonKey(name: 'CreatedById') this.createdById,
      @JsonKey(name: 'CreatedDate') this.createdDate,
      @JsonKey(name: 'LastModifiedById') this.lastModifiedById,
      @JsonKey(name: 'LastModifiedDate') this.lastModifiedDate,
      @JsonKey(name: 'SystemModstamp') this.systemModstamp,
      @JsonKey(name: 'FileType') this.fileType,
      @JsonKey(name: 'PublishStatus') this.publishStatus,
      @JsonKey(name: 'VersionData') this.versionData,
      @JsonKey(name: 'ContentSize') this.contentSize,
      @JsonKey(name: 'FileExtension') this.fileExtension,
      @JsonKey(name: 'FirstPublishLocationId') this.firstPublishLocationId,
      @JsonKey(name: 'Origin') this.origin,
      @JsonKey(name: 'ContentLocation') this.contentLocation,
      @JsonKey(name: 'VersionDataUrl') this.versionDataUrl});
  factory _ContentVersionResponse.fromJson(Map<String, dynamic> json) =>
      _$ContentVersionResponseFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'ContentDocumentId')
  final String? contentDocumentId;
  @override
  @JsonKey(name: 'IsLatest')
  final bool? isLatest;
  @override
  @JsonKey(name: 'ContentBodyId')
  final String? contentBodyId;
  @override
  @JsonKey(name: 'VersionNumber')
  final String? versionNumber;
  @override
  @JsonKey(name: 'Title')
  final String? title;
  @override
  @JsonKey(name: 'SharingOption')
  final String? sharingOption;
  @override
  @JsonKey(name: 'SharingPrivacy')
  final String? sharingPrivacy;
  @override
  @JsonKey(name: 'PathOnClient')
  final String? pathOnClient;
  @override
  @JsonKey(name: 'RatingCount')
  final int? ratingCount;
  @override
  @JsonKey(name: 'IsDeleted')
  final bool? isDeleted;
  @override
  @JsonKey(name: 'ContentModifiedDate')
  final String? contentModifiedDate;
  @override
  @JsonKey(name: 'ContentModifiedById')
  final String? contentModifiedById;
  @override
  @JsonKey(name: 'Language')
  final String? language;
  @override
  @JsonKey(name: 'PositiveRatingCount')
  final int? positiveRatingCount;
  @override
  @JsonKey(name: 'NegativeRatingCount')
  final int? negativeRatingCount;
  @override
  @JsonKey(name: 'OwnerId')
  final String? ownerId;
  @override
  @JsonKey(name: 'CreatedById')
  final String? createdById;
  @override
  @JsonKey(name: 'CreatedDate')
  final String? createdDate;
  @override
  @JsonKey(name: 'LastModifiedById')
  final String? lastModifiedById;
  @override
  @JsonKey(name: 'LastModifiedDate')
  final String? lastModifiedDate;
  @override
  @JsonKey(name: 'SystemModstamp')
  final String? systemModstamp;
  @override
  @JsonKey(name: 'FileType')
  final String? fileType;
  @override
  @JsonKey(name: 'PublishStatus')
  final String? publishStatus;
  @override
  @JsonKey(name: 'VersionData')
  final String? versionData;
  @override
  @JsonKey(name: 'ContentSize')
  final int? contentSize;
  @override
  @JsonKey(name: 'FileExtension')
  final String? fileExtension;
  @override
  @JsonKey(name: 'FirstPublishLocationId')
  final String? firstPublishLocationId;
  @override
  @JsonKey(name: 'Origin')
  final String? origin;
  @override
  @JsonKey(name: 'ContentLocation')
  final String? contentLocation;
  @override
  @JsonKey(name: 'VersionDataUrl')
  final String? versionDataUrl;

  /// Create a copy of ContentVersionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContentVersionResponseCopyWith<_ContentVersionResponse> get copyWith =>
      __$ContentVersionResponseCopyWithImpl<_ContentVersionResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContentVersionResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContentVersionResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contentDocumentId, contentDocumentId) ||
                other.contentDocumentId == contentDocumentId) &&
            (identical(other.isLatest, isLatest) ||
                other.isLatest == isLatest) &&
            (identical(other.contentBodyId, contentBodyId) ||
                other.contentBodyId == contentBodyId) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.sharingOption, sharingOption) ||
                other.sharingOption == sharingOption) &&
            (identical(other.sharingPrivacy, sharingPrivacy) ||
                other.sharingPrivacy == sharingPrivacy) &&
            (identical(other.pathOnClient, pathOnClient) ||
                other.pathOnClient == pathOnClient) &&
            (identical(other.ratingCount, ratingCount) ||
                other.ratingCount == ratingCount) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.contentModifiedDate, contentModifiedDate) ||
                other.contentModifiedDate == contentModifiedDate) &&
            (identical(other.contentModifiedById, contentModifiedById) ||
                other.contentModifiedById == contentModifiedById) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.positiveRatingCount, positiveRatingCount) ||
                other.positiveRatingCount == positiveRatingCount) &&
            (identical(other.negativeRatingCount, negativeRatingCount) ||
                other.negativeRatingCount == negativeRatingCount) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.createdById, createdById) ||
                other.createdById == createdById) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.lastModifiedById, lastModifiedById) ||
                other.lastModifiedById == lastModifiedById) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.systemModstamp, systemModstamp) ||
                other.systemModstamp == systemModstamp) &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.versionData, versionData) ||
                other.versionData == versionData) &&
            (identical(other.contentSize, contentSize) ||
                other.contentSize == contentSize) &&
            (identical(other.fileExtension, fileExtension) ||
                other.fileExtension == fileExtension) &&
            (identical(other.firstPublishLocationId, firstPublishLocationId) ||
                other.firstPublishLocationId == firstPublishLocationId) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.contentLocation, contentLocation) ||
                other.contentLocation == contentLocation) &&
            (identical(other.versionDataUrl, versionDataUrl) ||
                other.versionDataUrl == versionDataUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        contentDocumentId,
        isLatest,
        contentBodyId,
        versionNumber,
        title,
        sharingOption,
        sharingPrivacy,
        pathOnClient,
        ratingCount,
        isDeleted,
        contentModifiedDate,
        contentModifiedById,
        language,
        positiveRatingCount,
        negativeRatingCount,
        ownerId,
        createdById,
        createdDate,
        lastModifiedById,
        lastModifiedDate,
        systemModstamp,
        fileType,
        publishStatus,
        versionData,
        contentSize,
        fileExtension,
        firstPublishLocationId,
        origin,
        contentLocation,
        versionDataUrl
      ]);

  @override
  String toString() {
    return 'ContentVersionResponse(id: $id, contentDocumentId: $contentDocumentId, isLatest: $isLatest, contentBodyId: $contentBodyId, versionNumber: $versionNumber, title: $title, sharingOption: $sharingOption, sharingPrivacy: $sharingPrivacy, pathOnClient: $pathOnClient, ratingCount: $ratingCount, isDeleted: $isDeleted, contentModifiedDate: $contentModifiedDate, contentModifiedById: $contentModifiedById, language: $language, positiveRatingCount: $positiveRatingCount, negativeRatingCount: $negativeRatingCount, ownerId: $ownerId, createdById: $createdById, createdDate: $createdDate, lastModifiedById: $lastModifiedById, lastModifiedDate: $lastModifiedDate, systemModstamp: $systemModstamp, fileType: $fileType, publishStatus: $publishStatus, versionData: $versionData, contentSize: $contentSize, fileExtension: $fileExtension, firstPublishLocationId: $firstPublishLocationId, origin: $origin, contentLocation: $contentLocation, versionDataUrl: $versionDataUrl)';
  }
}

/// @nodoc
abstract mixin class _$ContentVersionResponseCopyWith<$Res>
    implements $ContentVersionResponseCopyWith<$Res> {
  factory _$ContentVersionResponseCopyWith(_ContentVersionResponse value,
          $Res Function(_ContentVersionResponse) _then) =
      __$ContentVersionResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
      @JsonKey(name: 'IsLatest') bool? isLatest,
      @JsonKey(name: 'ContentBodyId') String? contentBodyId,
      @JsonKey(name: 'VersionNumber') String? versionNumber,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'SharingOption') String? sharingOption,
      @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
      @JsonKey(name: 'PathOnClient') String? pathOnClient,
      @JsonKey(name: 'RatingCount') int? ratingCount,
      @JsonKey(name: 'IsDeleted') bool? isDeleted,
      @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
      @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
      @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'CreatedById') String? createdById,
      @JsonKey(name: 'CreatedDate') String? createdDate,
      @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'SystemModstamp') String? systemModstamp,
      @JsonKey(name: 'FileType') String? fileType,
      @JsonKey(name: 'PublishStatus') String? publishStatus,
      @JsonKey(name: 'VersionData') String? versionData,
      @JsonKey(name: 'ContentSize') int? contentSize,
      @JsonKey(name: 'FileExtension') String? fileExtension,
      @JsonKey(name: 'FirstPublishLocationId') String? firstPublishLocationId,
      @JsonKey(name: 'Origin') String? origin,
      @JsonKey(name: 'ContentLocation') String? contentLocation,
      @JsonKey(name: 'VersionDataUrl') String? versionDataUrl});
}

/// @nodoc
class __$ContentVersionResponseCopyWithImpl<$Res>
    implements _$ContentVersionResponseCopyWith<$Res> {
  __$ContentVersionResponseCopyWithImpl(this._self, this._then);

  final _ContentVersionResponse _self;
  final $Res Function(_ContentVersionResponse) _then;

  /// Create a copy of ContentVersionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? contentDocumentId = freezed,
    Object? isLatest = freezed,
    Object? contentBodyId = freezed,
    Object? versionNumber = freezed,
    Object? title = freezed,
    Object? sharingOption = freezed,
    Object? sharingPrivacy = freezed,
    Object? pathOnClient = freezed,
    Object? ratingCount = freezed,
    Object? isDeleted = freezed,
    Object? contentModifiedDate = freezed,
    Object? contentModifiedById = freezed,
    Object? language = freezed,
    Object? positiveRatingCount = freezed,
    Object? negativeRatingCount = freezed,
    Object? ownerId = freezed,
    Object? createdById = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedById = freezed,
    Object? lastModifiedDate = freezed,
    Object? systemModstamp = freezed,
    Object? fileType = freezed,
    Object? publishStatus = freezed,
    Object? versionData = freezed,
    Object? contentSize = freezed,
    Object? fileExtension = freezed,
    Object? firstPublishLocationId = freezed,
    Object? origin = freezed,
    Object? contentLocation = freezed,
    Object? versionDataUrl = freezed,
  }) {
    return _then(_ContentVersionResponse(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      contentDocumentId: freezed == contentDocumentId
          ? _self.contentDocumentId
          : contentDocumentId // ignore: cast_nullable_to_non_nullable
              as String?,
      isLatest: freezed == isLatest
          ? _self.isLatest
          : isLatest // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentBodyId: freezed == contentBodyId
          ? _self.contentBodyId
          : contentBodyId // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _self.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingOption: freezed == sharingOption
          ? _self.sharingOption
          : sharingOption // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingPrivacy: freezed == sharingPrivacy
          ? _self.sharingPrivacy
          : sharingPrivacy // ignore: cast_nullable_to_non_nullable
              as String?,
      pathOnClient: freezed == pathOnClient
          ? _self.pathOnClient
          : pathOnClient // ignore: cast_nullable_to_non_nullable
              as String?,
      ratingCount: freezed == ratingCount
          ? _self.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isDeleted: freezed == isDeleted
          ? _self.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentModifiedDate: freezed == contentModifiedDate
          ? _self.contentModifiedDate
          : contentModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contentModifiedById: freezed == contentModifiedById
          ? _self.contentModifiedById
          : contentModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      positiveRatingCount: freezed == positiveRatingCount
          ? _self.positiveRatingCount
          : positiveRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      negativeRatingCount: freezed == negativeRatingCount
          ? _self.negativeRatingCount
          : negativeRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdById: freezed == createdById
          ? _self.createdById
          : createdById // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedById: freezed == lastModifiedById
          ? _self.lastModifiedById
          : lastModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      systemModstamp: freezed == systemModstamp
          ? _self.systemModstamp
          : systemModstamp // ignore: cast_nullable_to_non_nullable
              as String?,
      fileType: freezed == fileType
          ? _self.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String?,
      publishStatus: freezed == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      versionData: freezed == versionData
          ? _self.versionData
          : versionData // ignore: cast_nullable_to_non_nullable
              as String?,
      contentSize: freezed == contentSize
          ? _self.contentSize
          : contentSize // ignore: cast_nullable_to_non_nullable
              as int?,
      fileExtension: freezed == fileExtension
          ? _self.fileExtension
          : fileExtension // ignore: cast_nullable_to_non_nullable
              as String?,
      firstPublishLocationId: freezed == firstPublishLocationId
          ? _self.firstPublishLocationId
          : firstPublishLocationId // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _self.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as String?,
      contentLocation: freezed == contentLocation
          ? _self.contentLocation
          : contentLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      versionDataUrl: freezed == versionDataUrl
          ? _self.versionDataUrl
          : versionDataUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

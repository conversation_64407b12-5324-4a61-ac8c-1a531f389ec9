// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'case.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Case _$CaseFromJson(Map json) => $checkedCreate(
      '_Case',
      json,
      ($checkedConvert) {
        final val = _Case(
          id: $checkedConvert('id', (v) => v as String?),
          status: $checkedConvert('status', (v) => v as String?),
          caseNumber: $checkedConvert('caseNumber', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$CaseToJson(_Case instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.status case final value?) 'status': value,
      if (instance.caseNumber case final value?) 'caseNumber': value,
    };

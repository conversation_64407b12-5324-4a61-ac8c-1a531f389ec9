// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_soql_query_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesforceSoqlQueryResponse {
  bool get done;
  int get totalSize;
  String? get nextRecordsUrl;
  List<Map<String, dynamic>> get records;

  /// Create a copy of SalesforceSoqlQueryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SalesforceSoqlQueryResponseCopyWith<SalesforceSoqlQueryResponse>
      get copyWith => _$SalesforceSoqlQueryResponseCopyWithImpl<
              SalesforceSoqlQueryResponse>(
          this as SalesforceSoqlQueryResponse, _$identity);

  /// Serializes this SalesforceSoqlQueryResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SalesforceSoqlQueryResponse &&
            (identical(other.done, done) || other.done == done) &&
            (identical(other.totalSize, totalSize) ||
                other.totalSize == totalSize) &&
            (identical(other.nextRecordsUrl, nextRecordsUrl) ||
                other.nextRecordsUrl == nextRecordsUrl) &&
            const DeepCollectionEquality().equals(other.records, records));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, done, totalSize, nextRecordsUrl,
      const DeepCollectionEquality().hash(records));

  @override
  String toString() {
    return 'SalesforceSoqlQueryResponse(done: $done, totalSize: $totalSize, nextRecordsUrl: $nextRecordsUrl, records: $records)';
  }
}

/// @nodoc
abstract mixin class $SalesforceSoqlQueryResponseCopyWith<$Res> {
  factory $SalesforceSoqlQueryResponseCopyWith(
          SalesforceSoqlQueryResponse value,
          $Res Function(SalesforceSoqlQueryResponse) _then) =
      _$SalesforceSoqlQueryResponseCopyWithImpl;
  @useResult
  $Res call(
      {bool done,
      int totalSize,
      String? nextRecordsUrl,
      List<Map<String, dynamic>> records});
}

/// @nodoc
class _$SalesforceSoqlQueryResponseCopyWithImpl<$Res>
    implements $SalesforceSoqlQueryResponseCopyWith<$Res> {
  _$SalesforceSoqlQueryResponseCopyWithImpl(this._self, this._then);

  final SalesforceSoqlQueryResponse _self;
  final $Res Function(SalesforceSoqlQueryResponse) _then;

  /// Create a copy of SalesforceSoqlQueryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? done = null,
    Object? totalSize = null,
    Object? nextRecordsUrl = freezed,
    Object? records = null,
  }) {
    return _then(_self.copyWith(
      done: null == done
          ? _self.done
          : done // ignore: cast_nullable_to_non_nullable
              as bool,
      totalSize: null == totalSize
          ? _self.totalSize
          : totalSize // ignore: cast_nullable_to_non_nullable
              as int,
      nextRecordsUrl: freezed == nextRecordsUrl
          ? _self.nextRecordsUrl
          : nextRecordsUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      records: null == records
          ? _self.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ));
  }
}

/// Adds pattern-matching-related methods to [SalesforceSoqlQueryResponse].
extension SalesforceSoqlQueryResponsePatterns on SalesforceSoqlQueryResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SalesforceSoqlQueryResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceSoqlQueryResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SalesforceSoqlQueryResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceSoqlQueryResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SalesforceSoqlQueryResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceSoqlQueryResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool done, int totalSize, String? nextRecordsUrl,
            List<Map<String, dynamic>> records)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceSoqlQueryResponse() when $default != null:
        return $default(
            _that.done, _that.totalSize, _that.nextRecordsUrl, _that.records);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool done, int totalSize, String? nextRecordsUrl,
            List<Map<String, dynamic>> records)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceSoqlQueryResponse():
        return $default(
            _that.done, _that.totalSize, _that.nextRecordsUrl, _that.records);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool done, int totalSize, String? nextRecordsUrl,
            List<Map<String, dynamic>> records)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceSoqlQueryResponse() when $default != null:
        return $default(
            _that.done, _that.totalSize, _that.nextRecordsUrl, _that.records);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SalesforceSoqlQueryResponse implements SalesforceSoqlQueryResponse {
  const _SalesforceSoqlQueryResponse(
      {required this.done,
      required this.totalSize,
      this.nextRecordsUrl,
      required final List<Map<String, dynamic>> records})
      : _records = records;
  factory _SalesforceSoqlQueryResponse.fromJson(Map<String, dynamic> json) =>
      _$SalesforceSoqlQueryResponseFromJson(json);

  @override
  final bool done;
  @override
  final int totalSize;
  @override
  final String? nextRecordsUrl;
  final List<Map<String, dynamic>> _records;
  @override
  List<Map<String, dynamic>> get records {
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_records);
  }

  /// Create a copy of SalesforceSoqlQueryResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SalesforceSoqlQueryResponseCopyWith<_SalesforceSoqlQueryResponse>
      get copyWith => __$SalesforceSoqlQueryResponseCopyWithImpl<
          _SalesforceSoqlQueryResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SalesforceSoqlQueryResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SalesforceSoqlQueryResponse &&
            (identical(other.done, done) || other.done == done) &&
            (identical(other.totalSize, totalSize) ||
                other.totalSize == totalSize) &&
            (identical(other.nextRecordsUrl, nextRecordsUrl) ||
                other.nextRecordsUrl == nextRecordsUrl) &&
            const DeepCollectionEquality().equals(other._records, _records));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, done, totalSize, nextRecordsUrl,
      const DeepCollectionEquality().hash(_records));

  @override
  String toString() {
    return 'SalesforceSoqlQueryResponse(done: $done, totalSize: $totalSize, nextRecordsUrl: $nextRecordsUrl, records: $records)';
  }
}

/// @nodoc
abstract mixin class _$SalesforceSoqlQueryResponseCopyWith<$Res>
    implements $SalesforceSoqlQueryResponseCopyWith<$Res> {
  factory _$SalesforceSoqlQueryResponseCopyWith(
          _SalesforceSoqlQueryResponse value,
          $Res Function(_SalesforceSoqlQueryResponse) _then) =
      __$SalesforceSoqlQueryResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool done,
      int totalSize,
      String? nextRecordsUrl,
      List<Map<String, dynamic>> records});
}

/// @nodoc
class __$SalesforceSoqlQueryResponseCopyWithImpl<$Res>
    implements _$SalesforceSoqlQueryResponseCopyWith<$Res> {
  __$SalesforceSoqlQueryResponseCopyWithImpl(this._self, this._then);

  final _SalesforceSoqlQueryResponse _self;
  final $Res Function(_SalesforceSoqlQueryResponse) _then;

  /// Create a copy of SalesforceSoqlQueryResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? done = null,
    Object? totalSize = null,
    Object? nextRecordsUrl = freezed,
    Object? records = null,
  }) {
    return _then(_SalesforceSoqlQueryResponse(
      done: null == done
          ? _self.done
          : done // ignore: cast_nullable_to_non_nullable
              as bool,
      totalSize: null == totalSize
          ? _self.totalSize
          : totalSize // ignore: cast_nullable_to_non_nullable
              as int,
      nextRecordsUrl: freezed == nextRecordsUrl
          ? _self.nextRecordsUrl
          : nextRecordsUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      records: null == records
          ? _self._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'revoke_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RevokeTokenBody {
  String get token;

  /// Create a copy of RevokeTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RevokeTokenBodyCopyWith<RevokeTokenBody> get copyWith =>
      _$RevokeTokenBodyCopyWithImpl<RevokeTokenBody>(
          this as RevokeTokenBody, _$identity);

  /// Serializes this RevokeTokenBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RevokeTokenBody &&
            (identical(other.token, token) || other.token == token));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, token);

  @override
  String toString() {
    return 'RevokeTokenBody(token: $token)';
  }
}

/// @nodoc
abstract mixin class $RevokeTokenBodyCopyWith<$Res> {
  factory $RevokeTokenBodyCopyWith(
          RevokeTokenBody value, $Res Function(RevokeTokenBody) _then) =
      _$RevokeTokenBodyCopyWithImpl;
  @useResult
  $Res call({String token});
}

/// @nodoc
class _$RevokeTokenBodyCopyWithImpl<$Res>
    implements $RevokeTokenBodyCopyWith<$Res> {
  _$RevokeTokenBodyCopyWithImpl(this._self, this._then);

  final RevokeTokenBody _self;
  final $Res Function(RevokeTokenBody) _then;

  /// Create a copy of RevokeTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
  }) {
    return _then(_self.copyWith(
      token: null == token
          ? _self.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [RevokeTokenBody].
extension RevokeTokenBodyPatterns on RevokeTokenBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RevokeTokenBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RevokeTokenBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RevokeTokenBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RevokeTokenBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RevokeTokenBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RevokeTokenBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String token)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RevokeTokenBody() when $default != null:
        return $default(_that.token);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String token) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RevokeTokenBody():
        return $default(_that.token);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String token)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RevokeTokenBody() when $default != null:
        return $default(_that.token);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RevokeTokenBody implements RevokeTokenBody {
  const _RevokeTokenBody({required this.token});
  factory _RevokeTokenBody.fromJson(Map<String, dynamic> json) =>
      _$RevokeTokenBodyFromJson(json);

  @override
  final String token;

  /// Create a copy of RevokeTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RevokeTokenBodyCopyWith<_RevokeTokenBody> get copyWith =>
      __$RevokeTokenBodyCopyWithImpl<_RevokeTokenBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RevokeTokenBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RevokeTokenBody &&
            (identical(other.token, token) || other.token == token));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, token);

  @override
  String toString() {
    return 'RevokeTokenBody(token: $token)';
  }
}

/// @nodoc
abstract mixin class _$RevokeTokenBodyCopyWith<$Res>
    implements $RevokeTokenBodyCopyWith<$Res> {
  factory _$RevokeTokenBodyCopyWith(
          _RevokeTokenBody value, $Res Function(_RevokeTokenBody) _then) =
      __$RevokeTokenBodyCopyWithImpl;
  @override
  @useResult
  $Res call({String token});
}

/// @nodoc
class __$RevokeTokenBodyCopyWithImpl<$Res>
    implements _$RevokeTokenBodyCopyWith<$Res> {
  __$RevokeTokenBodyCopyWithImpl(this._self, this._then);

  final _RevokeTokenBody _self;
  final $Res Function(_RevokeTokenBody) _then;

  /// Create a copy of RevokeTokenBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? token = null,
  }) {
    return _then(_RevokeTokenBody(
      token: null == token
          ? _self.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

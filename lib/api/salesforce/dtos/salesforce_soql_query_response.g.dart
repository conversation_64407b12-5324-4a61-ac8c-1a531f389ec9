// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salesforce_soql_query_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SalesforceSoqlQueryResponse _$SalesforceSoqlQueryResponseFromJson(Map json) =>
    $checkedCreate(
      '_SalesforceSoqlQueryResponse',
      json,
      ($checkedConvert) {
        final val = _SalesforceSoqlQueryResponse(
          done: $checkedConvert('done', (v) => v as bool),
          totalSize: $checkedConvert('totalSize', (v) => (v as num).toInt()),
          nextRecordsUrl:
              $checkedConvert('nextRecordsUrl', (v) => v as String?),
          records: $checkedConvert(
              'records',
              (v) => (v as List<dynamic>)
                  .map((e) => Map<String, dynamic>.from(e as Map))
                  .toList()),
        );
        return val;
      },
    );

Map<String, dynamic> _$SalesforceSoqlQueryResponseToJson(
        _SalesforceSoqlQueryResponse instance) =>
    <String, dynamic>{
      'done': instance.done,
      'totalSize': instance.totalSize,
      if (instance.nextRecordsUrl case final value?) 'nextRecordsUrl': value,
      'records': instance.records,
    };

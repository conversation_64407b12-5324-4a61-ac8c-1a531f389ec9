// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_session_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingSessionResponse {
  @Json<PERSON>ey(name: "Id")
  String get id;
  @Json<PERSON>ey(name: "Name")
  String? get name;
  @JsonKey(name: "ChannelName")
  String? get channelName;
  @JsonKey(name: "SessionKey")
  String? get sessionKey;
  @JsonKey(name: "Origin")
  @MessagingSessionOriginConverter()
  MessagingSessionOrigin? get origin;
  @JsonKey(name: "ChannelType")
  String? get channelType;
  @JsonKey(name: "ConversationId")
  String? get conversationId;
  @JsonKey(name: "MessagingChannelId")
  String? get messagingChannelId;
  @JsonKey(name: "MessagingEndUserId")
  String? get messagingEndUserId;
  @JsonKey(name: "CreatedDate")
  DateTime? get createdDate;
  @JsonKey(name: "OwnerId")
  String? get ownerId;
  @JsonKey(name: "Status")
  @MessagingSessionStatusConverter()
  MessagingSessionStatus? get status;
  @JsonKey(name: "EndUserAccountId")
  String? get endUserAccountId;
  @JsonKey(name: "EndUserContactId")
  String? get endUserContactId;
  @JsonKey(name: "EndUserLanguage")
  String? get endUserLanguage;
  @JsonKey(name: "CaseId")
  String? get caseId;
  @JsonKey(name: "LeadId")
  String? get leadId;
  @JsonKey(name: "OpportunityId")
  String? get opportunityId;

  /// Create a copy of MessagingSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingSessionResponseCopyWith<MessagingSessionResponse> get copyWith =>
      _$MessagingSessionResponseCopyWithImpl<MessagingSessionResponse>(
          this as MessagingSessionResponse, _$identity);

  /// Serializes this MessagingSessionResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingSessionResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.sessionKey, sessionKey) ||
                other.sessionKey == sessionKey) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.endUserAccountId, endUserAccountId) ||
                other.endUserAccountId == endUserAccountId) &&
            (identical(other.endUserContactId, endUserContactId) ||
                other.endUserContactId == endUserContactId) &&
            (identical(other.endUserLanguage, endUserLanguage) ||
                other.endUserLanguage == endUserLanguage) &&
            (identical(other.caseId, caseId) || other.caseId == caseId) &&
            (identical(other.leadId, leadId) || other.leadId == leadId) &&
            (identical(other.opportunityId, opportunityId) ||
                other.opportunityId == opportunityId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      channelName,
      sessionKey,
      origin,
      channelType,
      conversationId,
      messagingChannelId,
      messagingEndUserId,
      createdDate,
      ownerId,
      status,
      endUserAccountId,
      endUserContactId,
      endUserLanguage,
      caseId,
      leadId,
      opportunityId);

  @override
  String toString() {
    return 'MessagingSessionResponse(id: $id, name: $name, channelName: $channelName, sessionKey: $sessionKey, origin: $origin, channelType: $channelType, conversationId: $conversationId, messagingChannelId: $messagingChannelId, messagingEndUserId: $messagingEndUserId, createdDate: $createdDate, ownerId: $ownerId, status: $status, endUserAccountId: $endUserAccountId, endUserContactId: $endUserContactId, endUserLanguage: $endUserLanguage, caseId: $caseId, leadId: $leadId, opportunityId: $opportunityId)';
  }
}

/// @nodoc
abstract mixin class $MessagingSessionResponseCopyWith<$Res> {
  factory $MessagingSessionResponseCopyWith(MessagingSessionResponse value,
          $Res Function(MessagingSessionResponse) _then) =
      _$MessagingSessionResponseCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: "Id") String id,
      @JsonKey(name: "Name") String? name,
      @JsonKey(name: "ChannelName") String? channelName,
      @JsonKey(name: "SessionKey") String? sessionKey,
      @JsonKey(name: "Origin")
      @MessagingSessionOriginConverter()
      MessagingSessionOrigin? origin,
      @JsonKey(name: "ChannelType") String? channelType,
      @JsonKey(name: "ConversationId") String? conversationId,
      @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
      @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
      @JsonKey(name: "CreatedDate") DateTime? createdDate,
      @JsonKey(name: "OwnerId") String? ownerId,
      @JsonKey(name: "Status")
      @MessagingSessionStatusConverter()
      MessagingSessionStatus? status,
      @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
      @JsonKey(name: "EndUserContactId") String? endUserContactId,
      @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
      @JsonKey(name: "CaseId") String? caseId,
      @JsonKey(name: "LeadId") String? leadId,
      @JsonKey(name: "OpportunityId") String? opportunityId});
}

/// @nodoc
class _$MessagingSessionResponseCopyWithImpl<$Res>
    implements $MessagingSessionResponseCopyWith<$Res> {
  _$MessagingSessionResponseCopyWithImpl(this._self, this._then);

  final MessagingSessionResponse _self;
  final $Res Function(MessagingSessionResponse) _then;

  /// Create a copy of MessagingSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? channelName = freezed,
    Object? sessionKey = freezed,
    Object? origin = freezed,
    Object? channelType = freezed,
    Object? conversationId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingEndUserId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserAccountId = freezed,
    Object? endUserContactId = freezed,
    Object? endUserLanguage = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      channelName: freezed == channelName
          ? _self.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionKey: freezed == sessionKey
          ? _self.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _self.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as MessagingSessionOrigin?,
      channelType: freezed == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingChannelId: freezed == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserAccountId: freezed == endUserAccountId
          ? _self.endUserAccountId
          : endUserAccountId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserContactId: freezed == endUserContactId
          ? _self.endUserContactId
          : endUserContactId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserLanguage: freezed == endUserLanguage
          ? _self.endUserLanguage
          : endUserLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _self.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _self.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _self.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [MessagingSessionResponse].
extension MessagingSessionResponsePatterns on MessagingSessionResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingSessionResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingSessionResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingSessionResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSessionResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingSessionResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSessionResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: "Id") String id,
            @JsonKey(name: "Name") String? name,
            @JsonKey(name: "ChannelName") String? channelName,
            @JsonKey(name: "SessionKey") String? sessionKey,
            @JsonKey(name: "Origin")
            @MessagingSessionOriginConverter()
            MessagingSessionOrigin? origin,
            @JsonKey(name: "ChannelType") String? channelType,
            @JsonKey(name: "ConversationId") String? conversationId,
            @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
            @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
            @JsonKey(name: "CreatedDate") DateTime? createdDate,
            @JsonKey(name: "OwnerId") String? ownerId,
            @JsonKey(name: "Status")
            @MessagingSessionStatusConverter()
            MessagingSessionStatus? status,
            @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
            @JsonKey(name: "EndUserContactId") String? endUserContactId,
            @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
            @JsonKey(name: "CaseId") String? caseId,
            @JsonKey(name: "LeadId") String? leadId,
            @JsonKey(name: "OpportunityId") String? opportunityId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingSessionResponse() when $default != null:
        return $default(
            _that.id,
            _that.name,
            _that.channelName,
            _that.sessionKey,
            _that.origin,
            _that.channelType,
            _that.conversationId,
            _that.messagingChannelId,
            _that.messagingEndUserId,
            _that.createdDate,
            _that.ownerId,
            _that.status,
            _that.endUserAccountId,
            _that.endUserContactId,
            _that.endUserLanguage,
            _that.caseId,
            _that.leadId,
            _that.opportunityId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: "Id") String id,
            @JsonKey(name: "Name") String? name,
            @JsonKey(name: "ChannelName") String? channelName,
            @JsonKey(name: "SessionKey") String? sessionKey,
            @JsonKey(name: "Origin")
            @MessagingSessionOriginConverter()
            MessagingSessionOrigin? origin,
            @JsonKey(name: "ChannelType") String? channelType,
            @JsonKey(name: "ConversationId") String? conversationId,
            @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
            @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
            @JsonKey(name: "CreatedDate") DateTime? createdDate,
            @JsonKey(name: "OwnerId") String? ownerId,
            @JsonKey(name: "Status")
            @MessagingSessionStatusConverter()
            MessagingSessionStatus? status,
            @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
            @JsonKey(name: "EndUserContactId") String? endUserContactId,
            @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
            @JsonKey(name: "CaseId") String? caseId,
            @JsonKey(name: "LeadId") String? leadId,
            @JsonKey(name: "OpportunityId") String? opportunityId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSessionResponse():
        return $default(
            _that.id,
            _that.name,
            _that.channelName,
            _that.sessionKey,
            _that.origin,
            _that.channelType,
            _that.conversationId,
            _that.messagingChannelId,
            _that.messagingEndUserId,
            _that.createdDate,
            _that.ownerId,
            _that.status,
            _that.endUserAccountId,
            _that.endUserContactId,
            _that.endUserLanguage,
            _that.caseId,
            _that.leadId,
            _that.opportunityId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: "Id") String id,
            @JsonKey(name: "Name") String? name,
            @JsonKey(name: "ChannelName") String? channelName,
            @JsonKey(name: "SessionKey") String? sessionKey,
            @JsonKey(name: "Origin")
            @MessagingSessionOriginConverter()
            MessagingSessionOrigin? origin,
            @JsonKey(name: "ChannelType") String? channelType,
            @JsonKey(name: "ConversationId") String? conversationId,
            @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
            @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
            @JsonKey(name: "CreatedDate") DateTime? createdDate,
            @JsonKey(name: "OwnerId") String? ownerId,
            @JsonKey(name: "Status")
            @MessagingSessionStatusConverter()
            MessagingSessionStatus? status,
            @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
            @JsonKey(name: "EndUserContactId") String? endUserContactId,
            @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
            @JsonKey(name: "CaseId") String? caseId,
            @JsonKey(name: "LeadId") String? leadId,
            @JsonKey(name: "OpportunityId") String? opportunityId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSessionResponse() when $default != null:
        return $default(
            _that.id,
            _that.name,
            _that.channelName,
            _that.sessionKey,
            _that.origin,
            _that.channelType,
            _that.conversationId,
            _that.messagingChannelId,
            _that.messagingEndUserId,
            _that.createdDate,
            _that.ownerId,
            _that.status,
            _that.endUserAccountId,
            _that.endUserContactId,
            _that.endUserLanguage,
            _that.caseId,
            _that.leadId,
            _that.opportunityId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingSessionResponse implements MessagingSessionResponse {
  const _MessagingSessionResponse(
      {@JsonKey(name: "Id") required this.id,
      @JsonKey(name: "Name") this.name,
      @JsonKey(name: "ChannelName") this.channelName,
      @JsonKey(name: "SessionKey") this.sessionKey,
      @JsonKey(name: "Origin") @MessagingSessionOriginConverter() this.origin,
      @JsonKey(name: "ChannelType") this.channelType,
      @JsonKey(name: "ConversationId") this.conversationId,
      @JsonKey(name: "MessagingChannelId") this.messagingChannelId,
      @JsonKey(name: "MessagingEndUserId") this.messagingEndUserId,
      @JsonKey(name: "CreatedDate") this.createdDate,
      @JsonKey(name: "OwnerId") this.ownerId,
      @JsonKey(name: "Status") @MessagingSessionStatusConverter() this.status,
      @JsonKey(name: "EndUserAccountId") this.endUserAccountId,
      @JsonKey(name: "EndUserContactId") this.endUserContactId,
      @JsonKey(name: "EndUserLanguage") this.endUserLanguage,
      @JsonKey(name: "CaseId") this.caseId,
      @JsonKey(name: "LeadId") this.leadId,
      @JsonKey(name: "OpportunityId") this.opportunityId});
  factory _MessagingSessionResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingSessionResponseFromJson(json);

  @override
  @JsonKey(name: "Id")
  final String id;
  @override
  @JsonKey(name: "Name")
  final String? name;
  @override
  @JsonKey(name: "ChannelName")
  final String? channelName;
  @override
  @JsonKey(name: "SessionKey")
  final String? sessionKey;
  @override
  @JsonKey(name: "Origin")
  @MessagingSessionOriginConverter()
  final MessagingSessionOrigin? origin;
  @override
  @JsonKey(name: "ChannelType")
  final String? channelType;
  @override
  @JsonKey(name: "ConversationId")
  final String? conversationId;
  @override
  @JsonKey(name: "MessagingChannelId")
  final String? messagingChannelId;
  @override
  @JsonKey(name: "MessagingEndUserId")
  final String? messagingEndUserId;
  @override
  @JsonKey(name: "CreatedDate")
  final DateTime? createdDate;
  @override
  @JsonKey(name: "OwnerId")
  final String? ownerId;
  @override
  @JsonKey(name: "Status")
  @MessagingSessionStatusConverter()
  final MessagingSessionStatus? status;
  @override
  @JsonKey(name: "EndUserAccountId")
  final String? endUserAccountId;
  @override
  @JsonKey(name: "EndUserContactId")
  final String? endUserContactId;
  @override
  @JsonKey(name: "EndUserLanguage")
  final String? endUserLanguage;
  @override
  @JsonKey(name: "CaseId")
  final String? caseId;
  @override
  @JsonKey(name: "LeadId")
  final String? leadId;
  @override
  @JsonKey(name: "OpportunityId")
  final String? opportunityId;

  /// Create a copy of MessagingSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingSessionResponseCopyWith<_MessagingSessionResponse> get copyWith =>
      __$MessagingSessionResponseCopyWithImpl<_MessagingSessionResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingSessionResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingSessionResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.sessionKey, sessionKey) ||
                other.sessionKey == sessionKey) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.endUserAccountId, endUserAccountId) ||
                other.endUserAccountId == endUserAccountId) &&
            (identical(other.endUserContactId, endUserContactId) ||
                other.endUserContactId == endUserContactId) &&
            (identical(other.endUserLanguage, endUserLanguage) ||
                other.endUserLanguage == endUserLanguage) &&
            (identical(other.caseId, caseId) || other.caseId == caseId) &&
            (identical(other.leadId, leadId) || other.leadId == leadId) &&
            (identical(other.opportunityId, opportunityId) ||
                other.opportunityId == opportunityId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      channelName,
      sessionKey,
      origin,
      channelType,
      conversationId,
      messagingChannelId,
      messagingEndUserId,
      createdDate,
      ownerId,
      status,
      endUserAccountId,
      endUserContactId,
      endUserLanguage,
      caseId,
      leadId,
      opportunityId);

  @override
  String toString() {
    return 'MessagingSessionResponse(id: $id, name: $name, channelName: $channelName, sessionKey: $sessionKey, origin: $origin, channelType: $channelType, conversationId: $conversationId, messagingChannelId: $messagingChannelId, messagingEndUserId: $messagingEndUserId, createdDate: $createdDate, ownerId: $ownerId, status: $status, endUserAccountId: $endUserAccountId, endUserContactId: $endUserContactId, endUserLanguage: $endUserLanguage, caseId: $caseId, leadId: $leadId, opportunityId: $opportunityId)';
  }
}

/// @nodoc
abstract mixin class _$MessagingSessionResponseCopyWith<$Res>
    implements $MessagingSessionResponseCopyWith<$Res> {
  factory _$MessagingSessionResponseCopyWith(_MessagingSessionResponse value,
          $Res Function(_MessagingSessionResponse) _then) =
      __$MessagingSessionResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "Id") String id,
      @JsonKey(name: "Name") String? name,
      @JsonKey(name: "ChannelName") String? channelName,
      @JsonKey(name: "SessionKey") String? sessionKey,
      @JsonKey(name: "Origin")
      @MessagingSessionOriginConverter()
      MessagingSessionOrigin? origin,
      @JsonKey(name: "ChannelType") String? channelType,
      @JsonKey(name: "ConversationId") String? conversationId,
      @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
      @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
      @JsonKey(name: "CreatedDate") DateTime? createdDate,
      @JsonKey(name: "OwnerId") String? ownerId,
      @JsonKey(name: "Status")
      @MessagingSessionStatusConverter()
      MessagingSessionStatus? status,
      @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
      @JsonKey(name: "EndUserContactId") String? endUserContactId,
      @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
      @JsonKey(name: "CaseId") String? caseId,
      @JsonKey(name: "LeadId") String? leadId,
      @JsonKey(name: "OpportunityId") String? opportunityId});
}

/// @nodoc
class __$MessagingSessionResponseCopyWithImpl<$Res>
    implements _$MessagingSessionResponseCopyWith<$Res> {
  __$MessagingSessionResponseCopyWithImpl(this._self, this._then);

  final _MessagingSessionResponse _self;
  final $Res Function(_MessagingSessionResponse) _then;

  /// Create a copy of MessagingSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? channelName = freezed,
    Object? sessionKey = freezed,
    Object? origin = freezed,
    Object? channelType = freezed,
    Object? conversationId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingEndUserId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserAccountId = freezed,
    Object? endUserContactId = freezed,
    Object? endUserLanguage = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
  }) {
    return _then(_MessagingSessionResponse(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      channelName: freezed == channelName
          ? _self.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionKey: freezed == sessionKey
          ? _self.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _self.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as MessagingSessionOrigin?,
      channelType: freezed == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingChannelId: freezed == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserAccountId: freezed == endUserAccountId
          ? _self.endUserAccountId
          : endUserAccountId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserContactId: freezed == endUserContactId
          ? _self.endUserContactId
          : endUserContactId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserLanguage: freezed == endUserLanguage
          ? _self.endUserLanguage
          : endUserLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _self.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _self.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _self.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

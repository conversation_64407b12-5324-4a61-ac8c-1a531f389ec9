// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContactResponse {
  Attributes? get attributes;
  @JsonKey(name: 'Id')
  String? get id;
  @JsonKey(name: 'AccountId')
  String? get accountId;
  @JsonKey(name: 'IndividualId')
  String? get individualId;
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl;
  @JsonKey(name: 'OwnerId')
  String? get ownerId;
  @JsonKey(name: 'Department')
  String? get department;
  @JsonKey(name: 'Title')
  String? get title;
  @JsonKey(name: 'Email')
  String? get email;
  @JsonKey(name: 'Phone')
  String? get phone;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @JsonKey(name: 'LastName')
  String? get lastName;
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @JsonKey(name: 'Name')
  String? get name;

  /// Create a copy of ContactResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactResponseCopyWith<ContactResponse> get copyWith =>
      _$ContactResponseCopyWithImpl<ContactResponse>(
          this as ContactResponse, _$identity);

  /// Serializes this ContactResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactResponse &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.individualId, individualId) ||
                other.individualId == individualId) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      attributes,
      id,
      accountId,
      individualId,
      photoUrl,
      ownerId,
      department,
      title,
      email,
      phone,
      mobilePhone,
      lastName,
      firstName,
      name);

  @override
  String toString() {
    return 'ContactResponse(attributes: $attributes, id: $id, accountId: $accountId, individualId: $individualId, photoUrl: $photoUrl, ownerId: $ownerId, department: $department, title: $title, email: $email, phone: $phone, mobilePhone: $mobilePhone, lastName: $lastName, firstName: $firstName, name: $name)';
  }
}

/// @nodoc
abstract mixin class $ContactResponseCopyWith<$Res> {
  factory $ContactResponseCopyWith(
          ContactResponse value, $Res Function(ContactResponse) _then) =
      _$ContactResponseCopyWithImpl;
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'IndividualId') String? individualId,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'Department') String? department,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name});

  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class _$ContactResponseCopyWithImpl<$Res>
    implements $ContactResponseCopyWith<$Res> {
  _$ContactResponseCopyWithImpl(this._self, this._then);

  final ContactResponse _self;
  final $Res Function(ContactResponse) _then;

  /// Create a copy of ContactResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? accountId = freezed,
    Object? individualId = freezed,
    Object? photoUrl = freezed,
    Object? ownerId = freezed,
    Object? department = freezed,
    Object? title = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
  }) {
    return _then(_self.copyWith(
      attributes: freezed == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _self.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      individualId: freezed == individualId
          ? _self.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _self.department
          : department // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of ContactResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res>? get attributes {
    if (_self.attributes == null) {
      return null;
    }

    return $AttributesCopyWith<$Res>(_self.attributes!, (value) {
      return _then(_self.copyWith(attributes: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ContactResponse].
extension ContactResponsePatterns on ContactResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Attributes? attributes,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'AccountId') String? accountId,
            @JsonKey(name: 'IndividualId') String? individualId,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'OwnerId') String? ownerId,
            @JsonKey(name: 'Department') String? department,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'Phone') String? phone,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'Name') String? name)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactResponse() when $default != null:
        return $default(
            _that.attributes,
            _that.id,
            _that.accountId,
            _that.individualId,
            _that.photoUrl,
            _that.ownerId,
            _that.department,
            _that.title,
            _that.email,
            _that.phone,
            _that.mobilePhone,
            _that.lastName,
            _that.firstName,
            _that.name);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Attributes? attributes,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'AccountId') String? accountId,
            @JsonKey(name: 'IndividualId') String? individualId,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'OwnerId') String? ownerId,
            @JsonKey(name: 'Department') String? department,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'Phone') String? phone,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'Name') String? name)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactResponse():
        return $default(
            _that.attributes,
            _that.id,
            _that.accountId,
            _that.individualId,
            _that.photoUrl,
            _that.ownerId,
            _that.department,
            _that.title,
            _that.email,
            _that.phone,
            _that.mobilePhone,
            _that.lastName,
            _that.firstName,
            _that.name);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Attributes? attributes,
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'AccountId') String? accountId,
            @JsonKey(name: 'IndividualId') String? individualId,
            @JsonKey(name: 'PhotoUrl') String? photoUrl,
            @JsonKey(name: 'OwnerId') String? ownerId,
            @JsonKey(name: 'Department') String? department,
            @JsonKey(name: 'Title') String? title,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'Phone') String? phone,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'Name') String? name)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactResponse() when $default != null:
        return $default(
            _that.attributes,
            _that.id,
            _that.accountId,
            _that.individualId,
            _that.photoUrl,
            _that.ownerId,
            _that.department,
            _that.title,
            _that.email,
            _that.phone,
            _that.mobilePhone,
            _that.lastName,
            _that.firstName,
            _that.name);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactResponse implements ContactResponse {
  const _ContactResponse(
      {this.attributes,
      @JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'AccountId') this.accountId,
      @JsonKey(name: 'IndividualId') this.individualId,
      @JsonKey(name: 'PhotoUrl') this.photoUrl,
      @JsonKey(name: 'OwnerId') this.ownerId,
      @JsonKey(name: 'Department') this.department,
      @JsonKey(name: 'Title') this.title,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'Phone') this.phone,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'Name') this.name});
  factory _ContactResponse.fromJson(Map<String, dynamic> json) =>
      _$ContactResponseFromJson(json);

  @override
  final Attributes? attributes;
  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'AccountId')
  final String? accountId;
  @override
  @JsonKey(name: 'IndividualId')
  final String? individualId;
  @override
  @JsonKey(name: 'PhotoUrl')
  final String? photoUrl;
  @override
  @JsonKey(name: 'OwnerId')
  final String? ownerId;
  @override
  @JsonKey(name: 'Department')
  final String? department;
  @override
  @JsonKey(name: 'Title')
  final String? title;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'Phone')
  final String? phone;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'Name')
  final String? name;

  /// Create a copy of ContactResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactResponseCopyWith<_ContactResponse> get copyWith =>
      __$ContactResponseCopyWithImpl<_ContactResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactResponse &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.individualId, individualId) ||
                other.individualId == individualId) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      attributes,
      id,
      accountId,
      individualId,
      photoUrl,
      ownerId,
      department,
      title,
      email,
      phone,
      mobilePhone,
      lastName,
      firstName,
      name);

  @override
  String toString() {
    return 'ContactResponse(attributes: $attributes, id: $id, accountId: $accountId, individualId: $individualId, photoUrl: $photoUrl, ownerId: $ownerId, department: $department, title: $title, email: $email, phone: $phone, mobilePhone: $mobilePhone, lastName: $lastName, firstName: $firstName, name: $name)';
  }
}

/// @nodoc
abstract mixin class _$ContactResponseCopyWith<$Res>
    implements $ContactResponseCopyWith<$Res> {
  factory _$ContactResponseCopyWith(
          _ContactResponse value, $Res Function(_ContactResponse) _then) =
      __$ContactResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'IndividualId') String? individualId,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'Department') String? department,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name});

  @override
  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class __$ContactResponseCopyWithImpl<$Res>
    implements _$ContactResponseCopyWith<$Res> {
  __$ContactResponseCopyWithImpl(this._self, this._then);

  final _ContactResponse _self;
  final $Res Function(_ContactResponse) _then;

  /// Create a copy of ContactResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? accountId = freezed,
    Object? individualId = freezed,
    Object? photoUrl = freezed,
    Object? ownerId = freezed,
    Object? department = freezed,
    Object? title = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
  }) {
    return _then(_ContactResponse(
      attributes: freezed == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _self.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      individualId: freezed == individualId
          ? _self.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _self.department
          : department // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of ContactResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res>? get attributes {
    if (_self.attributes == null) {
      return null;
    }

    return $AttributesCopyWith<$Res>(_self.attributes!, (value) {
      return _then(_self.copyWith(attributes: value));
    });
  }
}

// dart format on

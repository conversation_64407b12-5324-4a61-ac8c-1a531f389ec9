// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_session_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingSessionResponse _$MessagingSessionResponseFromJson(Map json) =>
    $checkedCreate(
      '_MessagingSessionResponse',
      json,
      ($checkedConvert) {
        final val = _MessagingSessionResponse(
          id: $checkedConvert('Id', (v) => v as String),
          name: $checkedConvert('Name', (v) => v as String?),
          channelName: $checkedConvert('ChannelName', (v) => v as String?),
          sessionKey: $checkedConvert('SessionKey', (v) => v as String?),
          origin: $checkedConvert(
              'Origin',
              (v) => _$JsonConverterFromJson<String, MessagingSessionOrigin>(
                  v, const MessagingSessionOriginConverter().fromJson)),
          channelType: $checkedConvert('ChannelType', (v) => v as String?),
          conversationId:
              $checkedConvert('ConversationId', (v) => v as String?),
          messagingChannelId:
              $checkedConvert('MessagingChannelId', (v) => v as String?),
          messagingEndUserId:
              $checkedConvert('MessagingEndUserId', (v) => v as String?),
          createdDate: $checkedConvert('CreatedDate',
              (v) => v == null ? null : DateTime.parse(v as String)),
          ownerId: $checkedConvert('OwnerId', (v) => v as String?),
          status: $checkedConvert(
              'Status',
              (v) => _$JsonConverterFromJson<String, MessagingSessionStatus>(
                  v, const MessagingSessionStatusConverter().fromJson)),
          endUserAccountId:
              $checkedConvert('EndUserAccountId', (v) => v as String?),
          endUserContactId:
              $checkedConvert('EndUserContactId', (v) => v as String?),
          endUserLanguage:
              $checkedConvert('EndUserLanguage', (v) => v as String?),
          caseId: $checkedConvert('CaseId', (v) => v as String?),
          leadId: $checkedConvert('LeadId', (v) => v as String?),
          opportunityId: $checkedConvert('OpportunityId', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'id': 'Id',
        'name': 'Name',
        'channelName': 'ChannelName',
        'sessionKey': 'SessionKey',
        'origin': 'Origin',
        'channelType': 'ChannelType',
        'conversationId': 'ConversationId',
        'messagingChannelId': 'MessagingChannelId',
        'messagingEndUserId': 'MessagingEndUserId',
        'createdDate': 'CreatedDate',
        'ownerId': 'OwnerId',
        'status': 'Status',
        'endUserAccountId': 'EndUserAccountId',
        'endUserContactId': 'EndUserContactId',
        'endUserLanguage': 'EndUserLanguage',
        'caseId': 'CaseId',
        'leadId': 'LeadId',
        'opportunityId': 'OpportunityId'
      },
    );

Map<String, dynamic> _$MessagingSessionResponseToJson(
        _MessagingSessionResponse instance) =>
    <String, dynamic>{
      'Id': instance.id,
      if (instance.name case final value?) 'Name': value,
      if (instance.channelName case final value?) 'ChannelName': value,
      if (instance.sessionKey case final value?) 'SessionKey': value,
      if (_$JsonConverterToJson<String, MessagingSessionOrigin>(
              instance.origin, const MessagingSessionOriginConverter().toJson)
          case final value?)
        'Origin': value,
      if (instance.channelType case final value?) 'ChannelType': value,
      if (instance.conversationId case final value?) 'ConversationId': value,
      if (instance.messagingChannelId case final value?)
        'MessagingChannelId': value,
      if (instance.messagingEndUserId case final value?)
        'MessagingEndUserId': value,
      if (instance.createdDate?.toIso8601String() case final value?)
        'CreatedDate': value,
      if (instance.ownerId case final value?) 'OwnerId': value,
      if (_$JsonConverterToJson<String, MessagingSessionStatus>(
              instance.status, const MessagingSessionStatusConverter().toJson)
          case final value?)
        'Status': value,
      if (instance.endUserAccountId case final value?)
        'EndUserAccountId': value,
      if (instance.endUserContactId case final value?)
        'EndUserContactId': value,
      if (instance.endUserLanguage case final value?) 'EndUserLanguage': value,
      if (instance.caseId case final value?) 'CaseId': value,
      if (instance.leadId case final value?) 'LeadId': value,
      if (instance.opportunityId case final value?) 'OpportunityId': value,
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);

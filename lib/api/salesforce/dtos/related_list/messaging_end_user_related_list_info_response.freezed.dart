// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_related_list_info_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingEndUserRelatedListInfoResponse {
  Map<String, dynamic> get body;

  /// Create a copy of MessagingEndUserRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingEndUserRelatedListInfoResponseCopyWith<
          MessagingEndUserRelatedListInfoResponse>
      get copyWith => _$MessagingEndUserRelatedListInfoResponseCopyWithImpl<
              MessagingEndUserRelatedListInfoResponse>(
          this as MessagingEndUserRelatedListInfoResponse, _$identity);

  /// Serializes this MessagingEndUserRelatedListInfoResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingEndUserRelatedListInfoResponse &&
            const DeepCollectionEquality().equals(other.body, body));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(body));

  @override
  String toString() {
    return 'MessagingEndUserRelatedListInfoResponse(body: $body)';
  }
}

/// @nodoc
abstract mixin class $MessagingEndUserRelatedListInfoResponseCopyWith<$Res> {
  factory $MessagingEndUserRelatedListInfoResponseCopyWith(
          MessagingEndUserRelatedListInfoResponse value,
          $Res Function(MessagingEndUserRelatedListInfoResponse) _then) =
      _$MessagingEndUserRelatedListInfoResponseCopyWithImpl;
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class _$MessagingEndUserRelatedListInfoResponseCopyWithImpl<$Res>
    implements $MessagingEndUserRelatedListInfoResponseCopyWith<$Res> {
  _$MessagingEndUserRelatedListInfoResponseCopyWithImpl(this._self, this._then);

  final MessagingEndUserRelatedListInfoResponse _self;
  final $Res Function(MessagingEndUserRelatedListInfoResponse) _then;

  /// Create a copy of MessagingEndUserRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
  }) {
    return _then(_self.copyWith(
      body: null == body
          ? _self.body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// Adds pattern-matching-related methods to [MessagingEndUserRelatedListInfoResponse].
extension MessagingEndUserRelatedListInfoResponsePatterns
    on MessagingEndUserRelatedListInfoResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingEndUserRelatedListInfoResponse value)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserRelatedListInfoResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingEndUserRelatedListInfoResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserRelatedListInfoResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingEndUserRelatedListInfoResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserRelatedListInfoResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(Map<String, dynamic> body)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserRelatedListInfoResponse() when $default != null:
        return $default(_that.body);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(Map<String, dynamic> body) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserRelatedListInfoResponse():
        return $default(_that.body);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(Map<String, dynamic> body)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingEndUserRelatedListInfoResponse() when $default != null:
        return $default(_that.body);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingEndUserRelatedListInfoResponse
    implements MessagingEndUserRelatedListInfoResponse {
  const _MessagingEndUserRelatedListInfoResponse(
      {final Map<String, dynamic> body = const {}})
      : _body = body;
  factory _MessagingEndUserRelatedListInfoResponse.fromJson(
          Map<String, dynamic> json) =>
      _$MessagingEndUserRelatedListInfoResponseFromJson(json);

  final Map<String, dynamic> _body;
  @override
  @JsonKey()
  Map<String, dynamic> get body {
    if (_body is EqualUnmodifiableMapView) return _body;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_body);
  }

  /// Create a copy of MessagingEndUserRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingEndUserRelatedListInfoResponseCopyWith<
          _MessagingEndUserRelatedListInfoResponse>
      get copyWith => __$MessagingEndUserRelatedListInfoResponseCopyWithImpl<
          _MessagingEndUserRelatedListInfoResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingEndUserRelatedListInfoResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingEndUserRelatedListInfoResponse &&
            const DeepCollectionEquality().equals(other._body, _body));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_body));

  @override
  String toString() {
    return 'MessagingEndUserRelatedListInfoResponse(body: $body)';
  }
}

/// @nodoc
abstract mixin class _$MessagingEndUserRelatedListInfoResponseCopyWith<$Res>
    implements $MessagingEndUserRelatedListInfoResponseCopyWith<$Res> {
  factory _$MessagingEndUserRelatedListInfoResponseCopyWith(
          _MessagingEndUserRelatedListInfoResponse value,
          $Res Function(_MessagingEndUserRelatedListInfoResponse) _then) =
      __$MessagingEndUserRelatedListInfoResponseCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class __$MessagingEndUserRelatedListInfoResponseCopyWithImpl<$Res>
    implements _$MessagingEndUserRelatedListInfoResponseCopyWith<$Res> {
  __$MessagingEndUserRelatedListInfoResponseCopyWithImpl(
      this._self, this._then);

  final _MessagingEndUserRelatedListInfoResponse _self;
  final $Res Function(_MessagingEndUserRelatedListInfoResponse) _then;

  /// Create a copy of MessagingEndUserRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? body = null,
  }) {
    return _then(_MessagingEndUserRelatedListInfoResponse(
      body: null == body
          ? _self._body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on

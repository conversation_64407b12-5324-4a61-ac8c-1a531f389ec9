// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_related_list_info_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContactRelatedListInfoResponse {
  Map<String, dynamic> get body;

  /// Create a copy of ContactRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactRelatedListInfoResponseCopyWith<ContactRelatedListInfoResponse>
      get copyWith => _$ContactRelatedListInfoResponseCopyWithImpl<
              ContactRelatedListInfoResponse>(
          this as ContactRelatedListInfoResponse, _$identity);

  /// Serializes this ContactRelatedListInfoResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactRelatedListInfoResponse &&
            const DeepCollectionEquality().equals(other.body, body));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(body));

  @override
  String toString() {
    return 'ContactRelatedListInfoResponse(body: $body)';
  }
}

/// @nodoc
abstract mixin class $ContactRelatedListInfoResponseCopyWith<$Res> {
  factory $ContactRelatedListInfoResponseCopyWith(
          ContactRelatedListInfoResponse value,
          $Res Function(ContactRelatedListInfoResponse) _then) =
      _$ContactRelatedListInfoResponseCopyWithImpl;
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class _$ContactRelatedListInfoResponseCopyWithImpl<$Res>
    implements $ContactRelatedListInfoResponseCopyWith<$Res> {
  _$ContactRelatedListInfoResponseCopyWithImpl(this._self, this._then);

  final ContactRelatedListInfoResponse _self;
  final $Res Function(ContactRelatedListInfoResponse) _then;

  /// Create a copy of ContactRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
  }) {
    return _then(_self.copyWith(
      body: null == body
          ? _self.body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactRelatedListInfoResponse].
extension ContactRelatedListInfoResponsePatterns
    on ContactRelatedListInfoResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactRelatedListInfoResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRelatedListInfoResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactRelatedListInfoResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRelatedListInfoResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactRelatedListInfoResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRelatedListInfoResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(Map<String, dynamic> body)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRelatedListInfoResponse() when $default != null:
        return $default(_that.body);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(Map<String, dynamic> body) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRelatedListInfoResponse():
        return $default(_that.body);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(Map<String, dynamic> body)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRelatedListInfoResponse() when $default != null:
        return $default(_that.body);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactRelatedListInfoResponse
    implements ContactRelatedListInfoResponse {
  const _ContactRelatedListInfoResponse(
      {final Map<String, dynamic> body = const {}})
      : _body = body;
  factory _ContactRelatedListInfoResponse.fromJson(Map<String, dynamic> json) =>
      _$ContactRelatedListInfoResponseFromJson(json);

  final Map<String, dynamic> _body;
  @override
  @JsonKey()
  Map<String, dynamic> get body {
    if (_body is EqualUnmodifiableMapView) return _body;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_body);
  }

  /// Create a copy of ContactRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactRelatedListInfoResponseCopyWith<_ContactRelatedListInfoResponse>
      get copyWith => __$ContactRelatedListInfoResponseCopyWithImpl<
          _ContactRelatedListInfoResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactRelatedListInfoResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactRelatedListInfoResponse &&
            const DeepCollectionEquality().equals(other._body, _body));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_body));

  @override
  String toString() {
    return 'ContactRelatedListInfoResponse(body: $body)';
  }
}

/// @nodoc
abstract mixin class _$ContactRelatedListInfoResponseCopyWith<$Res>
    implements $ContactRelatedListInfoResponseCopyWith<$Res> {
  factory _$ContactRelatedListInfoResponseCopyWith(
          _ContactRelatedListInfoResponse value,
          $Res Function(_ContactRelatedListInfoResponse) _then) =
      __$ContactRelatedListInfoResponseCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class __$ContactRelatedListInfoResponseCopyWithImpl<$Res>
    implements _$ContactRelatedListInfoResponseCopyWith<$Res> {
  __$ContactRelatedListInfoResponseCopyWithImpl(this._self, this._then);

  final _ContactRelatedListInfoResponse _self;
  final $Res Function(_ContactRelatedListInfoResponse) _then;

  /// Create a copy of ContactRelatedListInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? body = null,
  }) {
    return _then(_ContactRelatedListInfoResponse(
      body: null == body
          ? _self._body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on

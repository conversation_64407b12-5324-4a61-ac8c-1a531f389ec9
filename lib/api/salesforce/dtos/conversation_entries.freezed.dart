// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversation_entries.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ConversationEntries {
  @JsonKey(name: 'Id')
  String? get id;
  @JsonKey(name: 'ConversationIdentifier')
  String? get conversationIdentifier;
  @JsonKey(name: 'ConversationChannelId')
  String? get conversationChannelId;

  /// Create a copy of ConversationEntries
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConversationEntriesCopyWith<ConversationEntries> get copyWith =>
      _$ConversationEntriesCopyWithImpl<ConversationEntries>(
          this as ConversationEntries, _$identity);

  /// Serializes this ConversationEntries to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ConversationEntries &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.conversationIdentifier, conversationIdentifier) ||
                other.conversationIdentifier == conversationIdentifier) &&
            (identical(other.conversationChannelId, conversationChannelId) ||
                other.conversationChannelId == conversationChannelId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, conversationIdentifier, conversationChannelId);

  @override
  String toString() {
    return 'ConversationEntries(id: $id, conversationIdentifier: $conversationIdentifier, conversationChannelId: $conversationChannelId)';
  }
}

/// @nodoc
abstract mixin class $ConversationEntriesCopyWith<$Res> {
  factory $ConversationEntriesCopyWith(
          ConversationEntries value, $Res Function(ConversationEntries) _then) =
      _$ConversationEntriesCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ConversationIdentifier') String? conversationIdentifier,
      @JsonKey(name: 'ConversationChannelId') String? conversationChannelId});
}

/// @nodoc
class _$ConversationEntriesCopyWithImpl<$Res>
    implements $ConversationEntriesCopyWith<$Res> {
  _$ConversationEntriesCopyWithImpl(this._self, this._then);

  final ConversationEntries _self;
  final $Res Function(ConversationEntries) _then;

  /// Create a copy of ConversationEntries
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? conversationIdentifier = freezed,
    Object? conversationChannelId = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationIdentifier: freezed == conversationIdentifier
          ? _self.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationChannelId: freezed == conversationChannelId
          ? _self.conversationChannelId
          : conversationChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ConversationEntries].
extension ConversationEntriesPatterns on ConversationEntries {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ConversationEntries value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ConversationEntries() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ConversationEntries value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationEntries():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ConversationEntries value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationEntries() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'ConversationIdentifier')
            String? conversationIdentifier,
            @JsonKey(name: 'ConversationChannelId')
            String? conversationChannelId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ConversationEntries() when $default != null:
        return $default(_that.id, _that.conversationIdentifier,
            _that.conversationChannelId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'ConversationIdentifier')
            String? conversationIdentifier,
            @JsonKey(name: 'ConversationChannelId')
            String? conversationChannelId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationEntries():
        return $default(_that.id, _that.conversationIdentifier,
            _that.conversationChannelId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'ConversationIdentifier')
            String? conversationIdentifier,
            @JsonKey(name: 'ConversationChannelId')
            String? conversationChannelId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ConversationEntries() when $default != null:
        return $default(_that.id, _that.conversationIdentifier,
            _that.conversationChannelId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ConversationEntries extends ConversationEntries {
  const _ConversationEntries(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'ConversationIdentifier') this.conversationIdentifier,
      @JsonKey(name: 'ConversationChannelId') this.conversationChannelId})
      : super._();
  factory _ConversationEntries.fromJson(Map<String, dynamic> json) =>
      _$ConversationEntriesFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'ConversationIdentifier')
  final String? conversationIdentifier;
  @override
  @JsonKey(name: 'ConversationChannelId')
  final String? conversationChannelId;

  /// Create a copy of ConversationEntries
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConversationEntriesCopyWith<_ConversationEntries> get copyWith =>
      __$ConversationEntriesCopyWithImpl<_ConversationEntries>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConversationEntriesToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ConversationEntries &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.conversationIdentifier, conversationIdentifier) ||
                other.conversationIdentifier == conversationIdentifier) &&
            (identical(other.conversationChannelId, conversationChannelId) ||
                other.conversationChannelId == conversationChannelId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, conversationIdentifier, conversationChannelId);

  @override
  String toString() {
    return 'ConversationEntries(id: $id, conversationIdentifier: $conversationIdentifier, conversationChannelId: $conversationChannelId)';
  }
}

/// @nodoc
abstract mixin class _$ConversationEntriesCopyWith<$Res>
    implements $ConversationEntriesCopyWith<$Res> {
  factory _$ConversationEntriesCopyWith(_ConversationEntries value,
          $Res Function(_ConversationEntries) _then) =
      __$ConversationEntriesCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ConversationIdentifier') String? conversationIdentifier,
      @JsonKey(name: 'ConversationChannelId') String? conversationChannelId});
}

/// @nodoc
class __$ConversationEntriesCopyWithImpl<$Res>
    implements _$ConversationEntriesCopyWith<$Res> {
  __$ConversationEntriesCopyWithImpl(this._self, this._then);

  final _ConversationEntries _self;
  final $Res Function(_ConversationEntries) _then;

  /// Create a copy of ConversationEntries
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? conversationIdentifier = freezed,
    Object? conversationChannelId = freezed,
  }) {
    return _then(_ConversationEntries(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationIdentifier: freezed == conversationIdentifier
          ? _self.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationChannelId: freezed == conversationChannelId
          ? _self.conversationChannelId
          : conversationChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

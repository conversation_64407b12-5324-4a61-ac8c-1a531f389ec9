// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_records_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SearchRecordsResponse _$SearchRecordsResponseFromJson(Map json) =>
    $checkedCreate(
      '_SearchRecordsResponse',
      json,
      ($checkedConvert) {
        final val = _SearchRecordsResponse(
          searchRecords: $checkedConvert(
              'searchRecords',
              (v) => (v as List<dynamic>)
                  .map((e) => SearchRecord.fromJson(
                      Map<String, dynamic>.from(e as Map)))
                  .toList()),
        );
        return val;
      },
    );

Map<String, dynamic> _$SearchRecordsResponseToJson(
        _SearchRecordsResponse instance) =>
    <String, dynamic>{
      'searchRecords': instance.searchRecords.map((e) => e.toJson()).toList(),
    };

_SearchRecord _$SearchRecordFromJson(Map json) => $checkedCreate(
      '_SearchRecord',
      json,
      ($checkedConvert) {
        final val = _SearchRecord(
          attributes: $checkedConvert('attributes',
              (v) => Attributes.fromJson(Map<String, dynamic>.from(v as Map))),
          name: $checkedConvert('Name', (v) => v as String?),
          firstName: $checkedConvert('FirstName', (v) => v as String?),
          lastName: $checkedConvert('LastName', (v) => v as String?),
          photoUrl: $checkedConvert('PhotoUrl', (v) => v as String?),
          id: $checkedConvert('Id', (v) => v as String?),
          email: $checkedConvert('Email', (v) => v as String?),
          mobilePhone: $checkedConvert('MobilePhone', (v) => v as String?),
          title: $checkedConvert('Title', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'name': 'Name',
        'firstName': 'FirstName',
        'lastName': 'LastName',
        'photoUrl': 'PhotoUrl',
        'id': 'Id',
        'email': 'Email',
        'mobilePhone': 'MobilePhone',
        'title': 'Title'
      },
    );

Map<String, dynamic> _$SearchRecordToJson(_SearchRecord instance) =>
    <String, dynamic>{
      'attributes': instance.attributes.toJson(),
      if (instance.name case final value?) 'Name': value,
      if (instance.firstName case final value?) 'FirstName': value,
      if (instance.lastName case final value?) 'LastName': value,
      if (instance.photoUrl case final value?) 'PhotoUrl': value,
      if (instance.id case final value?) 'Id': value,
      if (instance.email case final value?) 'Email': value,
      if (instance.mobilePhone case final value?) 'MobilePhone': value,
      if (instance.title case final value?) 'Title': value,
    };

_Attributes _$AttributesFromJson(Map json) => $checkedCreate(
      '_Attributes',
      json,
      ($checkedConvert) {
        final val = _Attributes(
          type: $checkedConvert('type', (v) => v as String),
          url: $checkedConvert('url', (v) => v as String),
        );
        return val;
      },
    );

Map<String, dynamic> _$AttributesToJson(_Attributes instance) =>
    <String, dynamic>{
      'type': instance.type,
      'url': instance.url,
    };

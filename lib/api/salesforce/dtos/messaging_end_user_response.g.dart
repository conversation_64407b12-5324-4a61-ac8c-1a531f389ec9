// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_end_user_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingEndUserResponse _$MessagingEndUserResponseFromJson(Map json) =>
    $checkedCreate(
      '_MessagingEndUserResponse',
      json,
      ($checkedConvert) {
        final val = _MessagingEndUserResponse(
          id: $checkedConvert('id', (v) => v as String?),
          name: $checkedConvert('name', (v) => v as String?),
          email: $checkedConvert('email', (v) => v as String?),
          phone: $checkedConvert('phone', (v) => v as String?),
          address: $checkedConvert('address', (v) => v as String?),
          city: $checkedConvert('city', (v) => v as String?),
          state: $checkedConvert('state', (v) => v as String?),
          zip: $checkedConvert('zip', (v) => v as String?),
          country: $checkedConvert('country', (v) => v as String?),
          status: $checkedConvert('status', (v) => v as String?),
          createdDate: $checkedConvert('createdDate', (v) => v as String?),
          lastModifiedDate:
              $checkedConvert('lastModifiedDate', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$MessagingEndUserResponseToJson(
        _MessagingEndUserResponse instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.email case final value?) 'email': value,
      if (instance.phone case final value?) 'phone': value,
      if (instance.address case final value?) 'address': value,
      if (instance.city case final value?) 'city': value,
      if (instance.state case final value?) 'state': value,
      if (instance.zip case final value?) 'zip': value,
      if (instance.country case final value?) 'country': value,
      if (instance.status case final value?) 'status': value,
      if (instance.createdDate case final value?) 'createdDate': value,
      if (instance.lastModifiedDate case final value?)
        'lastModifiedDate': value,
    };

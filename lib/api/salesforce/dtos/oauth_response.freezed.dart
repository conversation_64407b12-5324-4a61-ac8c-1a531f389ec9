// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'oauth_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OAuthResponse {
  @JsonKey(name: 'access_token')
  String? get accessToken;
  @JsonKey(name: 'instance_url')
  String? get instanceUrl;
  @JsonKey(name: 'refresh_token')
  String? get refreshToken;
  String? get id;
  @JsonKey(name: 'issued_at')
  String? get issuedAt;
  String? get signature;
  String? get scope;
  @JsonKey(name: 'token_type')
  String? get tokenType;
  String? get orgId;
  String? get userId;

  /// Create a copy of OAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OAuthResponseCopyWith<OAuthResponse> get copyWith =>
      _$OAuthResponseCopyWithImpl<OAuthResponse>(
          this as OAuthResponse, _$identity);

  /// Serializes this OAuthResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OAuthResponse &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.issuedAt, issuedAt) ||
                other.issuedAt == issuedAt) &&
            (identical(other.signature, signature) ||
                other.signature == signature) &&
            (identical(other.scope, scope) || other.scope == scope) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, accessToken, instanceUrl,
      refreshToken, id, issuedAt, signature, scope, tokenType, orgId, userId);

  @override
  String toString() {
    return 'OAuthResponse(accessToken: $accessToken, instanceUrl: $instanceUrl, refreshToken: $refreshToken, id: $id, issuedAt: $issuedAt, signature: $signature, scope: $scope, tokenType: $tokenType, orgId: $orgId, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class $OAuthResponseCopyWith<$Res> {
  factory $OAuthResponseCopyWith(
          OAuthResponse value, $Res Function(OAuthResponse) _then) =
      _$OAuthResponseCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'access_token') String? accessToken,
      @JsonKey(name: 'instance_url') String? instanceUrl,
      @JsonKey(name: 'refresh_token') String? refreshToken,
      String? id,
      @JsonKey(name: 'issued_at') String? issuedAt,
      String? signature,
      String? scope,
      @JsonKey(name: 'token_type') String? tokenType,
      String? orgId,
      String? userId});
}

/// @nodoc
class _$OAuthResponseCopyWithImpl<$Res>
    implements $OAuthResponseCopyWith<$Res> {
  _$OAuthResponseCopyWithImpl(this._self, this._then);

  final OAuthResponse _self;
  final $Res Function(OAuthResponse) _then;

  /// Create a copy of OAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? instanceUrl = freezed,
    Object? refreshToken = freezed,
    Object? id = freezed,
    Object? issuedAt = freezed,
    Object? signature = freezed,
    Object? scope = freezed,
    Object? tokenType = freezed,
    Object? orgId = freezed,
    Object? userId = freezed,
  }) {
    return _then(_self.copyWith(
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      issuedAt: freezed == issuedAt
          ? _self.issuedAt
          : issuedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      signature: freezed == signature
          ? _self.signature
          : signature // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _self.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: freezed == tokenType
          ? _self.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [OAuthResponse].
extension OAuthResponsePatterns on OAuthResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OAuthResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OAuthResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OAuthResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OAuthResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OAuthResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OAuthResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'access_token') String? accessToken,
            @JsonKey(name: 'instance_url') String? instanceUrl,
            @JsonKey(name: 'refresh_token') String? refreshToken,
            String? id,
            @JsonKey(name: 'issued_at') String? issuedAt,
            String? signature,
            String? scope,
            @JsonKey(name: 'token_type') String? tokenType,
            String? orgId,
            String? userId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OAuthResponse() when $default != null:
        return $default(
            _that.accessToken,
            _that.instanceUrl,
            _that.refreshToken,
            _that.id,
            _that.issuedAt,
            _that.signature,
            _that.scope,
            _that.tokenType,
            _that.orgId,
            _that.userId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'access_token') String? accessToken,
            @JsonKey(name: 'instance_url') String? instanceUrl,
            @JsonKey(name: 'refresh_token') String? refreshToken,
            String? id,
            @JsonKey(name: 'issued_at') String? issuedAt,
            String? signature,
            String? scope,
            @JsonKey(name: 'token_type') String? tokenType,
            String? orgId,
            String? userId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OAuthResponse():
        return $default(
            _that.accessToken,
            _that.instanceUrl,
            _that.refreshToken,
            _that.id,
            _that.issuedAt,
            _that.signature,
            _that.scope,
            _that.tokenType,
            _that.orgId,
            _that.userId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'access_token') String? accessToken,
            @JsonKey(name: 'instance_url') String? instanceUrl,
            @JsonKey(name: 'refresh_token') String? refreshToken,
            String? id,
            @JsonKey(name: 'issued_at') String? issuedAt,
            String? signature,
            String? scope,
            @JsonKey(name: 'token_type') String? tokenType,
            String? orgId,
            String? userId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OAuthResponse() when $default != null:
        return $default(
            _that.accessToken,
            _that.instanceUrl,
            _that.refreshToken,
            _that.id,
            _that.issuedAt,
            _that.signature,
            _that.scope,
            _that.tokenType,
            _that.orgId,
            _that.userId);
      case _:
        return null;
    }
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _OAuthResponse implements OAuthResponse {
  const _OAuthResponse(
      {@JsonKey(name: 'access_token') this.accessToken,
      @JsonKey(name: 'instance_url') this.instanceUrl,
      @JsonKey(name: 'refresh_token') this.refreshToken,
      this.id,
      @JsonKey(name: 'issued_at') this.issuedAt,
      this.signature,
      this.scope,
      @JsonKey(name: 'token_type') this.tokenType,
      this.orgId,
      this.userId});
  factory _OAuthResponse.fromJson(Map<String, dynamic> json) =>
      _$OAuthResponseFromJson(json);

  @override
  @JsonKey(name: 'access_token')
  final String? accessToken;
  @override
  @JsonKey(name: 'instance_url')
  final String? instanceUrl;
  @override
  @JsonKey(name: 'refresh_token')
  final String? refreshToken;
  @override
  final String? id;
  @override
  @JsonKey(name: 'issued_at')
  final String? issuedAt;
  @override
  final String? signature;
  @override
  final String? scope;
  @override
  @JsonKey(name: 'token_type')
  final String? tokenType;
  @override
  final String? orgId;
  @override
  final String? userId;

  /// Create a copy of OAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OAuthResponseCopyWith<_OAuthResponse> get copyWith =>
      __$OAuthResponseCopyWithImpl<_OAuthResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OAuthResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OAuthResponse &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.issuedAt, issuedAt) ||
                other.issuedAt == issuedAt) &&
            (identical(other.signature, signature) ||
                other.signature == signature) &&
            (identical(other.scope, scope) || other.scope == scope) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, accessToken, instanceUrl,
      refreshToken, id, issuedAt, signature, scope, tokenType, orgId, userId);

  @override
  String toString() {
    return 'OAuthResponse(accessToken: $accessToken, instanceUrl: $instanceUrl, refreshToken: $refreshToken, id: $id, issuedAt: $issuedAt, signature: $signature, scope: $scope, tokenType: $tokenType, orgId: $orgId, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class _$OAuthResponseCopyWith<$Res>
    implements $OAuthResponseCopyWith<$Res> {
  factory _$OAuthResponseCopyWith(
          _OAuthResponse value, $Res Function(_OAuthResponse) _then) =
      __$OAuthResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'access_token') String? accessToken,
      @JsonKey(name: 'instance_url') String? instanceUrl,
      @JsonKey(name: 'refresh_token') String? refreshToken,
      String? id,
      @JsonKey(name: 'issued_at') String? issuedAt,
      String? signature,
      String? scope,
      @JsonKey(name: 'token_type') String? tokenType,
      String? orgId,
      String? userId});
}

/// @nodoc
class __$OAuthResponseCopyWithImpl<$Res>
    implements _$OAuthResponseCopyWith<$Res> {
  __$OAuthResponseCopyWithImpl(this._self, this._then);

  final _OAuthResponse _self;
  final $Res Function(_OAuthResponse) _then;

  /// Create a copy of OAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? accessToken = freezed,
    Object? instanceUrl = freezed,
    Object? refreshToken = freezed,
    Object? id = freezed,
    Object? issuedAt = freezed,
    Object? signature = freezed,
    Object? scope = freezed,
    Object? tokenType = freezed,
    Object? orgId = freezed,
    Object? userId = freezed,
  }) {
    return _then(_OAuthResponse(
      accessToken: freezed == accessToken
          ? _self.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _self.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _self.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      issuedAt: freezed == issuedAt
          ? _self.issuedAt
          : issuedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      signature: freezed == signature
          ? _self.signature
          : signature // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _self.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: freezed == tokenType
          ? _self.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _self.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

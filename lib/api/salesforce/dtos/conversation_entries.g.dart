// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_entries.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ConversationEntries _$ConversationEntriesFromJson(Map json) => $checkedCreate(
      '_ConversationEntries',
      json,
      ($checkedConvert) {
        final val = _ConversationEntries(
          id: $checkedConvert('Id', (v) => v as String?),
          conversationIdentifier:
              $checkedConvert('ConversationIdentifier', (v) => v as String?),
          conversationChannelId:
              $checkedConvert('ConversationChannelId', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'id': 'Id',
        'conversationIdentifier': 'ConversationIdentifier',
        'conversationChannelId': 'ConversationChannelId'
      },
    );

Map<String, dynamic> _$ConversationEntriesToJson(
        _ConversationEntries instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'Id': value,
      if (instance.conversationIdentifier case final value?)
        'ConversationIdentifier': value,
      if (instance.conversationChannelId case final value?)
        'ConversationChannelId': value,
    };

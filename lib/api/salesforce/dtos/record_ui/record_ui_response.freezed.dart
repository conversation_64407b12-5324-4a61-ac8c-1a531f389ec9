// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_ui_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RecordUiResponse {
  Map<String, RecordLayoutSectionUserState> get layoutUserStates;
  Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> get layouts;
  Map<String, ObjectInfo> get objectInfos;
  Map<String, dynamic> get records;

  /// Create a copy of RecordUiResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RecordUiResponseCopyWith<RecordUiResponse> get copyWith =>
      _$RecordUiResponseCopyWithImpl<RecordUiResponse>(
          this as RecordUiResponse, _$identity);

  /// Serializes this RecordUiResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RecordUiResponse &&
            const DeepCollectionEquality()
                .equals(other.layoutUserStates, layoutUserStates) &&
            const DeepCollectionEquality().equals(other.layouts, layouts) &&
            const DeepCollectionEquality()
                .equals(other.objectInfos, objectInfos) &&
            const DeepCollectionEquality().equals(other.records, records));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(layoutUserStates),
      const DeepCollectionEquality().hash(layouts),
      const DeepCollectionEquality().hash(objectInfos),
      const DeepCollectionEquality().hash(records));

  @override
  String toString() {
    return 'RecordUiResponse(layoutUserStates: $layoutUserStates, layouts: $layouts, objectInfos: $objectInfos, records: $records)';
  }
}

/// @nodoc
abstract mixin class $RecordUiResponseCopyWith<$Res> {
  factory $RecordUiResponseCopyWith(
          RecordUiResponse value, $Res Function(RecordUiResponse) _then) =
      _$RecordUiResponseCopyWithImpl;
  @useResult
  $Res call(
      {Map<String, RecordLayoutSectionUserState> layoutUserStates,
      Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> layouts,
      Map<String, ObjectInfo> objectInfos,
      Map<String, dynamic> records});
}

/// @nodoc
class _$RecordUiResponseCopyWithImpl<$Res>
    implements $RecordUiResponseCopyWith<$Res> {
  _$RecordUiResponseCopyWithImpl(this._self, this._then);

  final RecordUiResponse _self;
  final $Res Function(RecordUiResponse) _then;

  /// Create a copy of RecordUiResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? layoutUserStates = null,
    Object? layouts = null,
    Object? objectInfos = null,
    Object? records = null,
  }) {
    return _then(_self.copyWith(
      layoutUserStates: null == layoutUserStates
          ? _self.layoutUserStates
          : layoutUserStates // ignore: cast_nullable_to_non_nullable
              as Map<String, RecordLayoutSectionUserState>,
      layouts: null == layouts
          ? _self.layouts
          : layouts // ignore: cast_nullable_to_non_nullable
              as Map<String,
                  Map<String, Map<String, Map<String, RecordLayout>>>>,
      objectInfos: null == objectInfos
          ? _self.objectInfos
          : objectInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, ObjectInfo>,
      records: null == records
          ? _self.records
          : records // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// Adds pattern-matching-related methods to [RecordUiResponse].
extension RecordUiResponsePatterns on RecordUiResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RecordUiResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RecordUiResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RecordUiResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordUiResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RecordUiResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordUiResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Map<String, RecordLayoutSectionUserState> layoutUserStates,
            Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
                layouts,
            Map<String, ObjectInfo> objectInfos,
            Map<String, dynamic> records)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RecordUiResponse() when $default != null:
        return $default(_that.layoutUserStates, _that.layouts,
            _that.objectInfos, _that.records);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Map<String, RecordLayoutSectionUserState> layoutUserStates,
            Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
                layouts,
            Map<String, ObjectInfo> objectInfos,
            Map<String, dynamic> records)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordUiResponse():
        return $default(_that.layoutUserStates, _that.layouts,
            _that.objectInfos, _that.records);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Map<String, RecordLayoutSectionUserState> layoutUserStates,
            Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
                layouts,
            Map<String, ObjectInfo> objectInfos,
            Map<String, dynamic> records)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordUiResponse() when $default != null:
        return $default(_that.layoutUserStates, _that.layouts,
            _that.objectInfos, _that.records);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RecordUiResponse implements RecordUiResponse {
  const _RecordUiResponse(
      {final Map<String, RecordLayoutSectionUserState> layoutUserStates =
          const {},
      final Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
          layouts = const {},
      final Map<String, ObjectInfo> objectInfos = const {},
      final Map<String, dynamic> records = const {}})
      : _layoutUserStates = layoutUserStates,
        _layouts = layouts,
        _objectInfos = objectInfos,
        _records = records;
  factory _RecordUiResponse.fromJson(Map<String, dynamic> json) =>
      _$RecordUiResponseFromJson(json);

  final Map<String, RecordLayoutSectionUserState> _layoutUserStates;
  @override
  @JsonKey()
  Map<String, RecordLayoutSectionUserState> get layoutUserStates {
    if (_layoutUserStates is EqualUnmodifiableMapView) return _layoutUserStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_layoutUserStates);
  }

  final Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
      _layouts;
  @override
  @JsonKey()
  Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> get layouts {
    if (_layouts is EqualUnmodifiableMapView) return _layouts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_layouts);
  }

  final Map<String, ObjectInfo> _objectInfos;
  @override
  @JsonKey()
  Map<String, ObjectInfo> get objectInfos {
    if (_objectInfos is EqualUnmodifiableMapView) return _objectInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_objectInfos);
  }

  final Map<String, dynamic> _records;
  @override
  @JsonKey()
  Map<String, dynamic> get records {
    if (_records is EqualUnmodifiableMapView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_records);
  }

  /// Create a copy of RecordUiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RecordUiResponseCopyWith<_RecordUiResponse> get copyWith =>
      __$RecordUiResponseCopyWithImpl<_RecordUiResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RecordUiResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RecordUiResponse &&
            const DeepCollectionEquality()
                .equals(other._layoutUserStates, _layoutUserStates) &&
            const DeepCollectionEquality().equals(other._layouts, _layouts) &&
            const DeepCollectionEquality()
                .equals(other._objectInfos, _objectInfos) &&
            const DeepCollectionEquality().equals(other._records, _records));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_layoutUserStates),
      const DeepCollectionEquality().hash(_layouts),
      const DeepCollectionEquality().hash(_objectInfos),
      const DeepCollectionEquality().hash(_records));

  @override
  String toString() {
    return 'RecordUiResponse(layoutUserStates: $layoutUserStates, layouts: $layouts, objectInfos: $objectInfos, records: $records)';
  }
}

/// @nodoc
abstract mixin class _$RecordUiResponseCopyWith<$Res>
    implements $RecordUiResponseCopyWith<$Res> {
  factory _$RecordUiResponseCopyWith(
          _RecordUiResponse value, $Res Function(_RecordUiResponse) _then) =
      __$RecordUiResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Map<String, RecordLayoutSectionUserState> layoutUserStates,
      Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> layouts,
      Map<String, ObjectInfo> objectInfos,
      Map<String, dynamic> records});
}

/// @nodoc
class __$RecordUiResponseCopyWithImpl<$Res>
    implements _$RecordUiResponseCopyWith<$Res> {
  __$RecordUiResponseCopyWithImpl(this._self, this._then);

  final _RecordUiResponse _self;
  final $Res Function(_RecordUiResponse) _then;

  /// Create a copy of RecordUiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? layoutUserStates = null,
    Object? layouts = null,
    Object? objectInfos = null,
    Object? records = null,
  }) {
    return _then(_RecordUiResponse(
      layoutUserStates: null == layoutUserStates
          ? _self._layoutUserStates
          : layoutUserStates // ignore: cast_nullable_to_non_nullable
              as Map<String, RecordLayoutSectionUserState>,
      layouts: null == layouts
          ? _self._layouts
          : layouts // ignore: cast_nullable_to_non_nullable
              as Map<String,
                  Map<String, Map<String, Map<String, RecordLayout>>>>,
      objectInfos: null == objectInfos
          ? _self._objectInfos
          : objectInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, ObjectInfo>,
      records: null == records
          ? _self._records
          : records // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on

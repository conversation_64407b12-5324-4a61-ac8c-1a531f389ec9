// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_or_update_contact_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreateOrUpdateContactBody {
  String get lastName;
  String get firstName;
  String? get email;
  @ParsePhoneNumberConverter()
  String get mobilePhone;

  /// Create a copy of CreateOrUpdateContactBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateOrUpdateContactBodyCopyWith<CreateOrUpdateContactBody> get copyWith =>
      _$CreateOrUpdateContactBodyCopyWithImpl<CreateOrUpdateContactBody>(
          this as CreateOrUpdateContactBody, _$identity);

  /// Serializes this CreateOrUpdateContactBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateOrUpdateContactBody &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, lastName, firstName, email, mobilePhone);

  @override
  String toString() {
    return 'CreateOrUpdateContactBody(lastName: $lastName, firstName: $firstName, email: $email, mobilePhone: $mobilePhone)';
  }
}

/// @nodoc
abstract mixin class $CreateOrUpdateContactBodyCopyWith<$Res> {
  factory $CreateOrUpdateContactBodyCopyWith(CreateOrUpdateContactBody value,
          $Res Function(CreateOrUpdateContactBody) _then) =
      _$CreateOrUpdateContactBodyCopyWithImpl;
  @useResult
  $Res call(
      {String lastName,
      String firstName,
      String? email,
      @ParsePhoneNumberConverter() String mobilePhone});
}

/// @nodoc
class _$CreateOrUpdateContactBodyCopyWithImpl<$Res>
    implements $CreateOrUpdateContactBodyCopyWith<$Res> {
  _$CreateOrUpdateContactBodyCopyWithImpl(this._self, this._then);

  final CreateOrUpdateContactBody _self;
  final $Res Function(CreateOrUpdateContactBody) _then;

  /// Create a copy of CreateOrUpdateContactBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastName = null,
    Object? firstName = null,
    Object? email = freezed,
    Object? mobilePhone = null,
  }) {
    return _then(_self.copyWith(
      lastName: null == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: null == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [CreateOrUpdateContactBody].
extension CreateOrUpdateContactBodyPatterns on CreateOrUpdateContactBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CreateOrUpdateContactBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CreateOrUpdateContactBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CreateOrUpdateContactBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateOrUpdateContactBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CreateOrUpdateContactBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateOrUpdateContactBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String lastName, String firstName, String? email,
            @ParsePhoneNumberConverter() String mobilePhone)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CreateOrUpdateContactBody() when $default != null:
        return $default(
            _that.lastName, _that.firstName, _that.email, _that.mobilePhone);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String lastName, String firstName, String? email,
            @ParsePhoneNumberConverter() String mobilePhone)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateOrUpdateContactBody():
        return $default(
            _that.lastName, _that.firstName, _that.email, _that.mobilePhone);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String lastName, String firstName, String? email,
            @ParsePhoneNumberConverter() String mobilePhone)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateOrUpdateContactBody() when $default != null:
        return $default(
            _that.lastName, _that.firstName, _that.email, _that.mobilePhone);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CreateOrUpdateContactBody implements CreateOrUpdateContactBody {
  const _CreateOrUpdateContactBody(
      {required this.lastName,
      required this.firstName,
      this.email,
      @ParsePhoneNumberConverter() required this.mobilePhone});
  factory _CreateOrUpdateContactBody.fromJson(Map<String, dynamic> json) =>
      _$CreateOrUpdateContactBodyFromJson(json);

  @override
  final String lastName;
  @override
  final String firstName;
  @override
  final String? email;
  @override
  @ParsePhoneNumberConverter()
  final String mobilePhone;

  /// Create a copy of CreateOrUpdateContactBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateOrUpdateContactBodyCopyWith<_CreateOrUpdateContactBody>
      get copyWith =>
          __$CreateOrUpdateContactBodyCopyWithImpl<_CreateOrUpdateContactBody>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateOrUpdateContactBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateOrUpdateContactBody &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, lastName, firstName, email, mobilePhone);

  @override
  String toString() {
    return 'CreateOrUpdateContactBody(lastName: $lastName, firstName: $firstName, email: $email, mobilePhone: $mobilePhone)';
  }
}

/// @nodoc
abstract mixin class _$CreateOrUpdateContactBodyCopyWith<$Res>
    implements $CreateOrUpdateContactBodyCopyWith<$Res> {
  factory _$CreateOrUpdateContactBodyCopyWith(_CreateOrUpdateContactBody value,
          $Res Function(_CreateOrUpdateContactBody) _then) =
      __$CreateOrUpdateContactBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String lastName,
      String firstName,
      String? email,
      @ParsePhoneNumberConverter() String mobilePhone});
}

/// @nodoc
class __$CreateOrUpdateContactBodyCopyWithImpl<$Res>
    implements _$CreateOrUpdateContactBodyCopyWith<$Res> {
  __$CreateOrUpdateContactBodyCopyWithImpl(this._self, this._then);

  final _CreateOrUpdateContactBody _self;
  final $Res Function(_CreateOrUpdateContactBody) _then;

  /// Create a copy of CreateOrUpdateContactBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? lastName = null,
    Object? firstName = null,
    Object? email = freezed,
    Object? mobilePhone = null,
  }) {
    return _then(_CreateOrUpdateContactBody(
      lastName: null == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: null == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

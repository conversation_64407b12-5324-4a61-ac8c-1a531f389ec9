// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_messaging_end_user_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UpdateMessagingEndUserBody {
// @J<PERSON><PERSON>ey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId get contactId;

  /// Create a copy of UpdateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateMessagingEndUserBodyCopyWith<UpdateMessagingEndUserBody>
      get copyWith =>
          _$UpdateMessagingEndUserBodyCopyWithImpl<UpdateMessagingEndUserBody>(
              this as UpdateMessagingEndUserBody, _$identity);

  /// Serializes this UpdateMessagingEndUserBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateMessagingEndUserBody &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactId);

  @override
  String toString() {
    return 'UpdateMessagingEndUserBody(contactId: $contactId)';
  }
}

/// @nodoc
abstract mixin class $UpdateMessagingEndUserBodyCopyWith<$Res> {
  factory $UpdateMessagingEndUserBodyCopyWith(UpdateMessagingEndUserBody value,
          $Res Function(UpdateMessagingEndUserBody) _then) =
      _$UpdateMessagingEndUserBodyCopyWithImpl;
  @useResult
  $Res call({@ParseSfIdConverter() SfId contactId});

  $SfIdCopyWith<$Res> get contactId;
}

/// @nodoc
class _$UpdateMessagingEndUserBodyCopyWithImpl<$Res>
    implements $UpdateMessagingEndUserBodyCopyWith<$Res> {
  _$UpdateMessagingEndUserBodyCopyWithImpl(this._self, this._then);

  final UpdateMessagingEndUserBody _self;
  final $Res Function(UpdateMessagingEndUserBody) _then;

  /// Create a copy of UpdateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = null,
  }) {
    return _then(_self.copyWith(
      contactId: null == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of UpdateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get contactId {
    return $SfIdCopyWith<$Res>(_self.contactId, (value) {
      return _then(_self.copyWith(contactId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [UpdateMessagingEndUserBody].
extension UpdateMessagingEndUserBodyPatterns on UpdateMessagingEndUserBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UpdateMessagingEndUserBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UpdateMessagingEndUserBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UpdateMessagingEndUserBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateMessagingEndUserBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UpdateMessagingEndUserBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateMessagingEndUserBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId contactId)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UpdateMessagingEndUserBody() when $default != null:
        return $default(_that.contactId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId contactId) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateMessagingEndUserBody():
        return $default(_that.contactId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@ParseSfIdConverter() SfId contactId)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateMessagingEndUserBody() when $default != null:
        return $default(_that.contactId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UpdateMessagingEndUserBody implements UpdateMessagingEndUserBody {
  const _UpdateMessagingEndUserBody(
      {@ParseSfIdConverter() required this.contactId});
  factory _UpdateMessagingEndUserBody.fromJson(Map<String, dynamic> json) =>
      _$UpdateMessagingEndUserBodyFromJson(json);

// @JsonKey(name: 'ContactId')
  @override
  @ParseSfIdConverter()
  final SfId contactId;

  /// Create a copy of UpdateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateMessagingEndUserBodyCopyWith<_UpdateMessagingEndUserBody>
      get copyWith => __$UpdateMessagingEndUserBodyCopyWithImpl<
          _UpdateMessagingEndUserBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UpdateMessagingEndUserBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateMessagingEndUserBody &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactId);

  @override
  String toString() {
    return 'UpdateMessagingEndUserBody(contactId: $contactId)';
  }
}

/// @nodoc
abstract mixin class _$UpdateMessagingEndUserBodyCopyWith<$Res>
    implements $UpdateMessagingEndUserBodyCopyWith<$Res> {
  factory _$UpdateMessagingEndUserBodyCopyWith(
          _UpdateMessagingEndUserBody value,
          $Res Function(_UpdateMessagingEndUserBody) _then) =
      __$UpdateMessagingEndUserBodyCopyWithImpl;
  @override
  @useResult
  $Res call({@ParseSfIdConverter() SfId contactId});

  @override
  $SfIdCopyWith<$Res> get contactId;
}

/// @nodoc
class __$UpdateMessagingEndUserBodyCopyWithImpl<$Res>
    implements _$UpdateMessagingEndUserBodyCopyWith<$Res> {
  __$UpdateMessagingEndUserBodyCopyWithImpl(this._self, this._then);

  final _UpdateMessagingEndUserBody _self;
  final $Res Function(_UpdateMessagingEndUserBody) _then;

  /// Create a copy of UpdateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contactId = null,
  }) {
    return _then(_UpdateMessagingEndUserBody(
      contactId: null == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }

  /// Create a copy of UpdateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get contactId {
    return $SfIdCopyWith<$Res>(_self.contactId, (value) {
      return _then(_self.copyWith(contactId: value));
    });
  }
}

// dart format on

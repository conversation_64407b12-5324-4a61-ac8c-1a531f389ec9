// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_layout.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RecordLayout {
  String? get id;
  String? get name;
  List<Map<String, dynamic>> get sections;

  /// Create a copy of RecordLayout
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RecordLayoutCopyWith<RecordLayout> get copyWith =>
      _$RecordLayoutCopyWithImpl<RecordLayout>(
          this as RecordLayout, _$identity);

  /// Serializes this RecordLayout to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RecordLayout &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other.sections, sections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, const DeepCollectionEquality().hash(sections));

  @override
  String toString() {
    return 'RecordLayout(id: $id, name: $name, sections: $sections)';
  }
}

/// @nodoc
abstract mixin class $RecordLayoutCopyWith<$Res> {
  factory $RecordLayoutCopyWith(
          RecordLayout value, $Res Function(RecordLayout) _then) =
      _$RecordLayoutCopyWithImpl;
  @useResult
  $Res call({String? id, String? name, List<Map<String, dynamic>> sections});
}

/// @nodoc
class _$RecordLayoutCopyWithImpl<$Res> implements $RecordLayoutCopyWith<$Res> {
  _$RecordLayoutCopyWithImpl(this._self, this._then);

  final RecordLayout _self;
  final $Res Function(RecordLayout) _then;

  /// Create a copy of RecordLayout
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? sections = null,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      sections: null == sections
          ? _self.sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ));
  }
}

/// Adds pattern-matching-related methods to [RecordLayout].
extension RecordLayoutPatterns on RecordLayout {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RecordLayout value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RecordLayout() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RecordLayout value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayout():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RecordLayout value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayout() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? id, String? name, List<Map<String, dynamic>> sections)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RecordLayout() when $default != null:
        return $default(_that.id, _that.name, _that.sections);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? id, String? name, List<Map<String, dynamic>> sections)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayout():
        return $default(_that.id, _that.name, _that.sections);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? id, String? name, List<Map<String, dynamic>> sections)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayout() when $default != null:
        return $default(_that.id, _that.name, _that.sections);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RecordLayout implements RecordLayout {
  const _RecordLayout(
      {this.id,
      this.name,
      final List<Map<String, dynamic>> sections = const []})
      : _sections = sections;
  factory _RecordLayout.fromJson(Map<String, dynamic> json) =>
      _$RecordLayoutFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  final List<Map<String, dynamic>> _sections;
  @override
  @JsonKey()
  List<Map<String, dynamic>> get sections {
    if (_sections is EqualUnmodifiableListView) return _sections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sections);
  }

  /// Create a copy of RecordLayout
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RecordLayoutCopyWith<_RecordLayout> get copyWith =>
      __$RecordLayoutCopyWithImpl<_RecordLayout>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RecordLayoutToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RecordLayout &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._sections, _sections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, const DeepCollectionEquality().hash(_sections));

  @override
  String toString() {
    return 'RecordLayout(id: $id, name: $name, sections: $sections)';
  }
}

/// @nodoc
abstract mixin class _$RecordLayoutCopyWith<$Res>
    implements $RecordLayoutCopyWith<$Res> {
  factory _$RecordLayoutCopyWith(
          _RecordLayout value, $Res Function(_RecordLayout) _then) =
      __$RecordLayoutCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, String? name, List<Map<String, dynamic>> sections});
}

/// @nodoc
class __$RecordLayoutCopyWithImpl<$Res>
    implements _$RecordLayoutCopyWith<$Res> {
  __$RecordLayoutCopyWithImpl(this._self, this._then);

  final _RecordLayout _self;
  final $Res Function(_RecordLayout) _then;

  /// Create a copy of RecordLayout
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? sections = null,
  }) {
    return _then(_RecordLayout(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      sections: null == sections
          ? _self._sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ));
  }
}

// dart format on

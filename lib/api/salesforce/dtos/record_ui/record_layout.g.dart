// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_layout.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RecordLayout _$RecordLayoutFromJson(Map json) => $checkedCreate(
      '_RecordLayout',
      json,
      ($checkedConvert) {
        final val = _RecordLayout(
          id: $checkedConvert('id', (v) => v as String?),
          name: $checkedConvert('name', (v) => v as String?),
          sections: $checkedConvert(
              'sections',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => Map<String, dynamic>.from(e as Map))
                      .toList() ??
                  const []),
        );
        return val;
      },
    );

Map<String, dynamic> _$RecordLayoutToJson(_RecordLayout instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      'sections': instance.sections,
    };

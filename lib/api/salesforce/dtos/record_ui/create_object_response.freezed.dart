// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_object_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreateSfObjectReponse {
  String get id;
  bool get success;
  List<String>? get errors;

  /// Create a copy of CreateSfObjectReponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateSfObjectReponseCopyWith<CreateSfObjectReponse> get copyWith =>
      _$CreateSfObjectReponseCopyWithImpl<CreateSfObjectReponse>(
          this as CreateSfObjectReponse, _$identity);

  /// Serializes this CreateSfObjectReponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateSfObjectReponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.success, success) || other.success == success) &&
            const DeepCollectionEquality().equals(other.errors, errors));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, success, const DeepCollectionEquality().hash(errors));

  @override
  String toString() {
    return 'CreateSfObjectReponse(id: $id, success: $success, errors: $errors)';
  }
}

/// @nodoc
abstract mixin class $CreateSfObjectReponseCopyWith<$Res> {
  factory $CreateSfObjectReponseCopyWith(CreateSfObjectReponse value,
          $Res Function(CreateSfObjectReponse) _then) =
      _$CreateSfObjectReponseCopyWithImpl;
  @useResult
  $Res call({String id, bool success, List<String>? errors});
}

/// @nodoc
class _$CreateSfObjectReponseCopyWithImpl<$Res>
    implements $CreateSfObjectReponseCopyWith<$Res> {
  _$CreateSfObjectReponseCopyWithImpl(this._self, this._then);

  final CreateSfObjectReponse _self;
  final $Res Function(CreateSfObjectReponse) _then;

  /// Create a copy of CreateSfObjectReponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? success = null,
    Object? errors = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      errors: freezed == errors
          ? _self.errors
          : errors // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [CreateSfObjectReponse].
extension CreateSfObjectReponsePatterns on CreateSfObjectReponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CreateSfObjectReponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CreateSfObjectReponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CreateSfObjectReponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateSfObjectReponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CreateSfObjectReponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateSfObjectReponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, bool success, List<String>? errors)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CreateSfObjectReponse() when $default != null:
        return $default(_that.id, _that.success, _that.errors);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, bool success, List<String>? errors) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateSfObjectReponse():
        return $default(_that.id, _that.success, _that.errors);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, bool success, List<String>? errors)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateSfObjectReponse() when $default != null:
        return $default(_that.id, _that.success, _that.errors);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CreateSfObjectReponse implements CreateSfObjectReponse {
  const _CreateSfObjectReponse(
      {required this.id,
      required this.success,
      required final List<String>? errors})
      : _errors = errors;
  factory _CreateSfObjectReponse.fromJson(Map<String, dynamic> json) =>
      _$CreateSfObjectReponseFromJson(json);

  @override
  final String id;
  @override
  final bool success;
  final List<String>? _errors;
  @override
  List<String>? get errors {
    final value = _errors;
    if (value == null) return null;
    if (_errors is EqualUnmodifiableListView) return _errors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of CreateSfObjectReponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateSfObjectReponseCopyWith<_CreateSfObjectReponse> get copyWith =>
      __$CreateSfObjectReponseCopyWithImpl<_CreateSfObjectReponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateSfObjectReponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateSfObjectReponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.success, success) || other.success == success) &&
            const DeepCollectionEquality().equals(other._errors, _errors));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, success, const DeepCollectionEquality().hash(_errors));

  @override
  String toString() {
    return 'CreateSfObjectReponse(id: $id, success: $success, errors: $errors)';
  }
}

/// @nodoc
abstract mixin class _$CreateSfObjectReponseCopyWith<$Res>
    implements $CreateSfObjectReponseCopyWith<$Res> {
  factory _$CreateSfObjectReponseCopyWith(_CreateSfObjectReponse value,
          $Res Function(_CreateSfObjectReponse) _then) =
      __$CreateSfObjectReponseCopyWithImpl;
  @override
  @useResult
  $Res call({String id, bool success, List<String>? errors});
}

/// @nodoc
class __$CreateSfObjectReponseCopyWithImpl<$Res>
    implements _$CreateSfObjectReponseCopyWith<$Res> {
  __$CreateSfObjectReponseCopyWithImpl(this._self, this._then);

  final _CreateSfObjectReponse _self;
  final $Res Function(_CreateSfObjectReponse) _then;

  /// Create a copy of CreateSfObjectReponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? success = null,
    Object? errors = freezed,
  }) {
    return _then(_CreateSfObjectReponse(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      errors: freezed == errors
          ? _self._errors
          : errors // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

// dart format on

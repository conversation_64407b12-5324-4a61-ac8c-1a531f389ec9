// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_object_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CreateSfObjectReponse _$CreateSfObjectReponseFromJson(Map json) =>
    $checkedCreate(
      '_CreateSfObjectReponse',
      json,
      ($checkedConvert) {
        final val = _CreateSfObjectReponse(
          id: $checkedConvert('id', (v) => v as String),
          success: $checkedConvert('success', (v) => v as bool),
          errors: $checkedConvert('errors',
              (v) => (v as List<dynamic>?)?.map((e) => e as String).toList()),
        );
        return val;
      },
    );

Map<String, dynamic> _$CreateSfObjectReponseToJson(
        _CreateSfObjectReponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'success': instance.success,
      if (instance.errors case final value?) 'errors': value,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_layout_section_user_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RecordLayoutSectionUserState {
  String? get id;
  bool get collapsed;

  /// Create a copy of RecordLayoutSectionUserState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RecordLayoutSectionUserStateCopyWith<RecordLayoutSectionUserState>
      get copyWith => _$RecordLayoutSectionUserStateCopyWithImpl<
              RecordLayoutSectionUserState>(
          this as RecordLayoutSectionUserState, _$identity);

  /// Serializes this RecordLayoutSectionUserState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RecordLayoutSectionUserState &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.collapsed, collapsed) ||
                other.collapsed == collapsed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, collapsed);

  @override
  String toString() {
    return 'RecordLayoutSectionUserState(id: $id, collapsed: $collapsed)';
  }
}

/// @nodoc
abstract mixin class $RecordLayoutSectionUserStateCopyWith<$Res> {
  factory $RecordLayoutSectionUserStateCopyWith(
          RecordLayoutSectionUserState value,
          $Res Function(RecordLayoutSectionUserState) _then) =
      _$RecordLayoutSectionUserStateCopyWithImpl;
  @useResult
  $Res call({String? id, bool collapsed});
}

/// @nodoc
class _$RecordLayoutSectionUserStateCopyWithImpl<$Res>
    implements $RecordLayoutSectionUserStateCopyWith<$Res> {
  _$RecordLayoutSectionUserStateCopyWithImpl(this._self, this._then);

  final RecordLayoutSectionUserState _self;
  final $Res Function(RecordLayoutSectionUserState) _then;

  /// Create a copy of RecordLayoutSectionUserState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? collapsed = null,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      collapsed: null == collapsed
          ? _self.collapsed
          : collapsed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [RecordLayoutSectionUserState].
extension RecordLayoutSectionUserStatePatterns on RecordLayoutSectionUserState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RecordLayoutSectionUserState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RecordLayoutSectionUserState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RecordLayoutSectionUserState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayoutSectionUserState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RecordLayoutSectionUserState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayoutSectionUserState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? id, bool collapsed)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RecordLayoutSectionUserState() when $default != null:
        return $default(_that.id, _that.collapsed);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? id, bool collapsed) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayoutSectionUserState():
        return $default(_that.id, _that.collapsed);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? id, bool collapsed)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RecordLayoutSectionUserState() when $default != null:
        return $default(_that.id, _that.collapsed);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RecordLayoutSectionUserState implements RecordLayoutSectionUserState {
  const _RecordLayoutSectionUserState({this.id, this.collapsed = true});
  factory _RecordLayoutSectionUserState.fromJson(Map<String, dynamic> json) =>
      _$RecordLayoutSectionUserStateFromJson(json);

  @override
  final String? id;
  @override
  @JsonKey()
  final bool collapsed;

  /// Create a copy of RecordLayoutSectionUserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RecordLayoutSectionUserStateCopyWith<_RecordLayoutSectionUserState>
      get copyWith => __$RecordLayoutSectionUserStateCopyWithImpl<
          _RecordLayoutSectionUserState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RecordLayoutSectionUserStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RecordLayoutSectionUserState &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.collapsed, collapsed) ||
                other.collapsed == collapsed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, collapsed);

  @override
  String toString() {
    return 'RecordLayoutSectionUserState(id: $id, collapsed: $collapsed)';
  }
}

/// @nodoc
abstract mixin class _$RecordLayoutSectionUserStateCopyWith<$Res>
    implements $RecordLayoutSectionUserStateCopyWith<$Res> {
  factory _$RecordLayoutSectionUserStateCopyWith(
          _RecordLayoutSectionUserState value,
          $Res Function(_RecordLayoutSectionUserState) _then) =
      __$RecordLayoutSectionUserStateCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, bool collapsed});
}

/// @nodoc
class __$RecordLayoutSectionUserStateCopyWithImpl<$Res>
    implements _$RecordLayoutSectionUserStateCopyWith<$Res> {
  __$RecordLayoutSectionUserStateCopyWithImpl(this._self, this._then);

  final _RecordLayoutSectionUserState _self;
  final $Res Function(_RecordLayoutSectionUserState) _then;

  /// Create a copy of RecordLayoutSectionUserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? collapsed = null,
  }) {
    return _then(_RecordLayoutSectionUserState(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      collapsed: null == collapsed
          ? _self.collapsed
          : collapsed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

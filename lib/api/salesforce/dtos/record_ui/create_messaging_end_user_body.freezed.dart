// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_messaging_end_user_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreateMessagingEndUserBody {
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId get contactId;
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  SfId get messagingChannelId;
  @JsonKey(name: 'MessagingConsentStatus')
  String get messagingConsentStatus;
  @JsonKey(name: 'MessagingPlatformKey')
  String get messagingPlatformKey;
  @JsonKey(name: 'MessageType')
  String get messageType;
  @JsonKey(name: 'Name')
  String get name;

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateMessagingEndUserBodyCopyWith<CreateMessagingEndUserBody>
      get copyWith =>
          _$CreateMessagingEndUserBodyCopyWithImpl<CreateMessagingEndUserBody>(
              this as CreateMessagingEndUserBody, _$identity);

  /// Serializes this CreateMessagingEndUserBody to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateMessagingEndUserBody &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingConsentStatus, messagingConsentStatus) ||
                other.messagingConsentStatus == messagingConsentStatus) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactId, messagingChannelId,
      messagingConsentStatus, messagingPlatformKey, messageType, name);

  @override
  String toString() {
    return 'CreateMessagingEndUserBody(contactId: $contactId, messagingChannelId: $messagingChannelId, messagingConsentStatus: $messagingConsentStatus, messagingPlatformKey: $messagingPlatformKey, messageType: $messageType, name: $name)';
  }
}

/// @nodoc
abstract mixin class $CreateMessagingEndUserBodyCopyWith<$Res> {
  factory $CreateMessagingEndUserBodyCopyWith(CreateMessagingEndUserBody value,
          $Res Function(CreateMessagingEndUserBody) _then) =
      _$CreateMessagingEndUserBodyCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId messagingChannelId,
      @JsonKey(name: 'MessagingConsentStatus') String messagingConsentStatus,
      @JsonKey(name: 'MessagingPlatformKey') String messagingPlatformKey,
      @JsonKey(name: 'MessageType') String messageType,
      @JsonKey(name: 'Name') String name});

  $SfIdCopyWith<$Res> get contactId;
  $SfIdCopyWith<$Res> get messagingChannelId;
}

/// @nodoc
class _$CreateMessagingEndUserBodyCopyWithImpl<$Res>
    implements $CreateMessagingEndUserBodyCopyWith<$Res> {
  _$CreateMessagingEndUserBodyCopyWithImpl(this._self, this._then);

  final CreateMessagingEndUserBody _self;
  final $Res Function(CreateMessagingEndUserBody) _then;

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = null,
    Object? messagingChannelId = null,
    Object? messagingConsentStatus = null,
    Object? messagingPlatformKey = null,
    Object? messageType = null,
    Object? name = null,
  }) {
    return _then(_self.copyWith(
      contactId: null == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingChannelId: null == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingConsentStatus: null == messagingConsentStatus
          ? _self.messagingConsentStatus
          : messagingConsentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      messagingPlatformKey: null == messagingPlatformKey
          ? _self.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String,
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get contactId {
    return $SfIdCopyWith<$Res>(_self.contactId, (value) {
      return _then(_self.copyWith(contactId: value));
    });
  }

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingChannelId {
    return $SfIdCopyWith<$Res>(_self.messagingChannelId, (value) {
      return _then(_self.copyWith(messagingChannelId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [CreateMessagingEndUserBody].
extension CreateMessagingEndUserBodyPatterns on CreateMessagingEndUserBody {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CreateMessagingEndUserBody value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CreateMessagingEndUserBody() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CreateMessagingEndUserBody value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateMessagingEndUserBody():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CreateMessagingEndUserBody value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateMessagingEndUserBody() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId contactId,
            @JsonKey(name: 'MessagingChannelId')
            @ParseSfIdConverter()
            SfId messagingChannelId,
            @JsonKey(name: 'MessagingConsentStatus')
            String messagingConsentStatus,
            @JsonKey(name: 'MessagingPlatformKey') String messagingPlatformKey,
            @JsonKey(name: 'MessageType') String messageType,
            @JsonKey(name: 'Name') String name)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CreateMessagingEndUserBody() when $default != null:
        return $default(
            _that.contactId,
            _that.messagingChannelId,
            _that.messagingConsentStatus,
            _that.messagingPlatformKey,
            _that.messageType,
            _that.name);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId contactId,
            @JsonKey(name: 'MessagingChannelId')
            @ParseSfIdConverter()
            SfId messagingChannelId,
            @JsonKey(name: 'MessagingConsentStatus')
            String messagingConsentStatus,
            @JsonKey(name: 'MessagingPlatformKey') String messagingPlatformKey,
            @JsonKey(name: 'MessageType') String messageType,
            @JsonKey(name: 'Name') String name)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateMessagingEndUserBody():
        return $default(
            _that.contactId,
            _that.messagingChannelId,
            _that.messagingConsentStatus,
            _that.messagingPlatformKey,
            _that.messageType,
            _that.name);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId contactId,
            @JsonKey(name: 'MessagingChannelId')
            @ParseSfIdConverter()
            SfId messagingChannelId,
            @JsonKey(name: 'MessagingConsentStatus')
            String messagingConsentStatus,
            @JsonKey(name: 'MessagingPlatformKey') String messagingPlatformKey,
            @JsonKey(name: 'MessageType') String messageType,
            @JsonKey(name: 'Name') String name)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CreateMessagingEndUserBody() when $default != null:
        return $default(
            _that.contactId,
            _that.messagingChannelId,
            _that.messagingConsentStatus,
            _that.messagingPlatformKey,
            _that.messageType,
            _that.name);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _CreateMessagingEndUserBody implements CreateMessagingEndUserBody {
  const _CreateMessagingEndUserBody(
      {@JsonKey(name: 'ContactId')
      @ParseSfIdConverter()
      required this.contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      required this.messagingChannelId,
      @JsonKey(name: 'MessagingConsentStatus')
      this.messagingConsentStatus = 'ExplicitlyOptedIn',
      @JsonKey(name: 'MessagingPlatformKey') required this.messagingPlatformKey,
      @JsonKey(name: 'MessageType') this.messageType = 'Text',
      @JsonKey(name: 'Name') this.name = 'Text'});
  factory _CreateMessagingEndUserBody.fromJson(Map<String, dynamic> json) =>
      _$CreateMessagingEndUserBodyFromJson(json);

  @override
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  final SfId contactId;
  @override
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  final SfId messagingChannelId;
  @override
  @JsonKey(name: 'MessagingConsentStatus')
  final String messagingConsentStatus;
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  final String messagingPlatformKey;
  @override
  @JsonKey(name: 'MessageType')
  final String messageType;
  @override
  @JsonKey(name: 'Name')
  final String name;

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateMessagingEndUserBodyCopyWith<_CreateMessagingEndUserBody>
      get copyWith => __$CreateMessagingEndUserBodyCopyWithImpl<
          _CreateMessagingEndUserBody>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateMessagingEndUserBodyToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateMessagingEndUserBody &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingConsentStatus, messagingConsentStatus) ||
                other.messagingConsentStatus == messagingConsentStatus) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contactId, messagingChannelId,
      messagingConsentStatus, messagingPlatformKey, messageType, name);

  @override
  String toString() {
    return 'CreateMessagingEndUserBody(contactId: $contactId, messagingChannelId: $messagingChannelId, messagingConsentStatus: $messagingConsentStatus, messagingPlatformKey: $messagingPlatformKey, messageType: $messageType, name: $name)';
  }
}

/// @nodoc
abstract mixin class _$CreateMessagingEndUserBodyCopyWith<$Res>
    implements $CreateMessagingEndUserBodyCopyWith<$Res> {
  factory _$CreateMessagingEndUserBodyCopyWith(
          _CreateMessagingEndUserBody value,
          $Res Function(_CreateMessagingEndUserBody) _then) =
      __$CreateMessagingEndUserBodyCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId messagingChannelId,
      @JsonKey(name: 'MessagingConsentStatus') String messagingConsentStatus,
      @JsonKey(name: 'MessagingPlatformKey') String messagingPlatformKey,
      @JsonKey(name: 'MessageType') String messageType,
      @JsonKey(name: 'Name') String name});

  @override
  $SfIdCopyWith<$Res> get contactId;
  @override
  $SfIdCopyWith<$Res> get messagingChannelId;
}

/// @nodoc
class __$CreateMessagingEndUserBodyCopyWithImpl<$Res>
    implements _$CreateMessagingEndUserBodyCopyWith<$Res> {
  __$CreateMessagingEndUserBodyCopyWithImpl(this._self, this._then);

  final _CreateMessagingEndUserBody _self;
  final $Res Function(_CreateMessagingEndUserBody) _then;

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contactId = null,
    Object? messagingChannelId = null,
    Object? messagingConsentStatus = null,
    Object? messagingPlatformKey = null,
    Object? messageType = null,
    Object? name = null,
  }) {
    return _then(_CreateMessagingEndUserBody(
      contactId: null == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingChannelId: null == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingConsentStatus: null == messagingConsentStatus
          ? _self.messagingConsentStatus
          : messagingConsentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      messagingPlatformKey: null == messagingPlatformKey
          ? _self.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String,
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get contactId {
    return $SfIdCopyWith<$Res>(_self.contactId, (value) {
      return _then(_self.copyWith(contactId: value));
    });
  }

  /// Create a copy of CreateMessagingEndUserBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingChannelId {
    return $SfIdCopyWith<$Res>(_self.messagingChannelId, (value) {
      return _then(_self.copyWith(messagingChannelId: value));
    });
  }
}

// dart format on

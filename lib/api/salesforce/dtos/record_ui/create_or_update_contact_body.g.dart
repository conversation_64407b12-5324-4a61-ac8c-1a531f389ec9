// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_or_update_contact_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CreateOrUpdateContactBody _$CreateOrUpdateContactBodyFromJson(Map json) =>
    $checkedCreate(
      '_CreateOrUpdateContactBody',
      json,
      ($checkedConvert) {
        final val = _CreateOrUpdateContactBody(
          lastName: $checkedConvert('lastName', (v) => v as String),
          firstName: $checkedConvert('firstName', (v) => v as String),
          email: $checkedConvert('email', (v) => v as String?),
          mobilePhone: $checkedConvert('mobilePhone',
              (v) => const ParsePhoneNumberConverter().fromJson(v)),
        );
        return val;
      },
    );

Map<String, dynamic> _$CreateOrUpdateContactBodyToJson(
        _CreateOrUpdateContactBody instance) =>
    <String, dynamic>{
      'lastName': instance.lastName,
      'firstName': instance.firstName,
      if (instance.email case final value?) 'email': value,
      if (const ParsePhoneNumberConverter().toJson(instance.mobilePhone)
          case final value?)
        'mobilePhone': value,
    };

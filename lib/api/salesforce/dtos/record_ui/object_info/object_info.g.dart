// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'object_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ObjectInfo _$ObjectInfoFromJson(Map json) => $checkedCreate(
      '_ObjectInfo',
      json,
      ($checkedConvert) {
        final val = _ObjectInfo(
          apiName: $checkedConvert('apiName', (v) => v as String?),
          associateEntityType:
              $checkedConvert('associateEntityType', (v) => v as String?),
          associateParentEntity:
              $checkedConvert('associateParentEntity', (v) => v as String?),
          childRelationships: $checkedConvert(
              'childRelationships',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => ChildRelationship.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const []),
          compactLayoutable:
              $checkedConvert('compactLayoutable', (v) => v as bool? ?? false),
          createable: $checkedConvert('createable', (v) => v as bool? ?? false),
          custom: $checkedConvert('custom', (v) => v as bool? ?? false),
          defaultRecordTypeId:
              $checkedConvert('defaultRecordTypeId', (v) => v as String?),
          deleteable: $checkedConvert('deleteable', (v) => v as bool? ?? false),
          dependentFields: $checkedConvert(
              'dependentFields',
              (v) =>
                  (v as Map?)?.map(
                    (k, e) => MapEntry(k as String, e as Object),
                  ) ??
                  const {}),
          feedEnabled:
              $checkedConvert('feedEnabled', (v) => v as bool? ?? false),
          fields: $checkedConvert(
              'fields',
              (v) =>
                  (v as Map?)?.map(
                    (k, e) => MapEntry(k as String, e),
                  ) ??
                  const {}),
          keyPrefix: $checkedConvert('keyPrefix', (v) => v as String?),
          label: $checkedConvert('label', (v) => v as String?),
          labelPlural: $checkedConvert('labelPlural', (v) => v as String?),
          layoutable: $checkedConvert('layoutable', (v) => v as bool? ?? false),
          mruEnabled: $checkedConvert('mruEnabled', (v) => v as bool? ?? false),
          nameFields: $checkedConvert(
              'nameFields',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String?).toList() ??
                  const []),
          queryable: $checkedConvert('queryable', (v) => v as bool? ?? false),
          searchable: $checkedConvert('searchable', (v) => v as bool? ?? false),
          searchLayoutable:
              $checkedConvert('searchLayoutable', (v) => v as bool? ?? false),
          updateable: $checkedConvert('updateable', (v) => v as bool? ?? false),
        );
        return val;
      },
    );

Map<String, dynamic> _$ObjectInfoToJson(_ObjectInfo instance) =>
    <String, dynamic>{
      if (instance.apiName case final value?) 'apiName': value,
      if (instance.associateEntityType case final value?)
        'associateEntityType': value,
      if (instance.associateParentEntity case final value?)
        'associateParentEntity': value,
      'childRelationships':
          instance.childRelationships.map((e) => e.toJson()).toList(),
      'compactLayoutable': instance.compactLayoutable,
      'createable': instance.createable,
      'custom': instance.custom,
      if (instance.defaultRecordTypeId case final value?)
        'defaultRecordTypeId': value,
      'deleteable': instance.deleteable,
      'dependentFields': instance.dependentFields,
      'feedEnabled': instance.feedEnabled,
      'fields': instance.fields,
      if (instance.keyPrefix case final value?) 'keyPrefix': value,
      if (instance.label case final value?) 'label': value,
      if (instance.labelPlural case final value?) 'labelPlural': value,
      'layoutable': instance.layoutable,
      'mruEnabled': instance.mruEnabled,
      'nameFields': instance.nameFields,
      'queryable': instance.queryable,
      'searchable': instance.searchable,
      'searchLayoutable': instance.searchLayoutable,
      'updateable': instance.updateable,
    };

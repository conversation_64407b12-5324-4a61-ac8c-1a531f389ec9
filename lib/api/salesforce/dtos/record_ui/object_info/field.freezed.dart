// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'field.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Field {
  String? get apiName;
  bool get calculated;
  bool get compound;
  String? get compoundComponentName;
  String? get compoundFieldName;
  String? get controllerName;
  List<String> get controllingFields;
  bool get createable;
  bool get custom;
  String? get dataType;

  /// this can come as bool:false
// String? externalId,
  String? get extraTypeInfo;
  bool
      get filterable; // filteredLookupInfo; SF Type: Filtered Lookup Info: https://developer.salesforce.com/docs/atlas.en-us.uiapi.meta/uiapi/ui_api_responses_filtered_lookup_info.htm#ui_api_responses_filtered_lookup_info
  bool get highScaleNumber;
  bool get htmlFormatted;
  String? get inlineHelpText;
  String? get label;
  int? get length;
  String? get maskType;
  bool get nameField;
  bool get polymorphicForeignKey;
  int? get precision;
  bool get reference;
  String? get referenceTargetField;
  Map<String, dynamic> get referenceToInfos;
  String? get relationshipName;
  bool get required;
  int? get scale;
  bool get searchPrefilterable;
  bool get sortable;
  bool get unique;
  bool get updateable;

  /// Create a copy of Field
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FieldCopyWith<Field> get copyWith =>
      _$FieldCopyWithImpl<Field>(this as Field, _$identity);

  /// Serializes this Field to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Field &&
            (identical(other.apiName, apiName) || other.apiName == apiName) &&
            (identical(other.calculated, calculated) ||
                other.calculated == calculated) &&
            (identical(other.compound, compound) ||
                other.compound == compound) &&
            (identical(other.compoundComponentName, compoundComponentName) ||
                other.compoundComponentName == compoundComponentName) &&
            (identical(other.compoundFieldName, compoundFieldName) ||
                other.compoundFieldName == compoundFieldName) &&
            (identical(other.controllerName, controllerName) ||
                other.controllerName == controllerName) &&
            const DeepCollectionEquality()
                .equals(other.controllingFields, controllingFields) &&
            (identical(other.createable, createable) ||
                other.createable == createable) &&
            (identical(other.custom, custom) || other.custom == custom) &&
            (identical(other.dataType, dataType) ||
                other.dataType == dataType) &&
            (identical(other.extraTypeInfo, extraTypeInfo) ||
                other.extraTypeInfo == extraTypeInfo) &&
            (identical(other.filterable, filterable) ||
                other.filterable == filterable) &&
            (identical(other.highScaleNumber, highScaleNumber) ||
                other.highScaleNumber == highScaleNumber) &&
            (identical(other.htmlFormatted, htmlFormatted) ||
                other.htmlFormatted == htmlFormatted) &&
            (identical(other.inlineHelpText, inlineHelpText) ||
                other.inlineHelpText == inlineHelpText) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.maskType, maskType) ||
                other.maskType == maskType) &&
            (identical(other.nameField, nameField) ||
                other.nameField == nameField) &&
            (identical(other.polymorphicForeignKey, polymorphicForeignKey) ||
                other.polymorphicForeignKey == polymorphicForeignKey) &&
            (identical(other.precision, precision) ||
                other.precision == precision) &&
            (identical(other.reference, reference) ||
                other.reference == reference) &&
            (identical(other.referenceTargetField, referenceTargetField) ||
                other.referenceTargetField == referenceTargetField) &&
            const DeepCollectionEquality()
                .equals(other.referenceToInfos, referenceToInfos) &&
            (identical(other.relationshipName, relationshipName) ||
                other.relationshipName == relationshipName) &&
            (identical(other.required, required) ||
                other.required == required) &&
            (identical(other.scale, scale) || other.scale == scale) &&
            (identical(other.searchPrefilterable, searchPrefilterable) ||
                other.searchPrefilterable == searchPrefilterable) &&
            (identical(other.sortable, sortable) ||
                other.sortable == sortable) &&
            (identical(other.unique, unique) || other.unique == unique) &&
            (identical(other.updateable, updateable) ||
                other.updateable == updateable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        apiName,
        calculated,
        compound,
        compoundComponentName,
        compoundFieldName,
        controllerName,
        const DeepCollectionEquality().hash(controllingFields),
        createable,
        custom,
        dataType,
        extraTypeInfo,
        filterable,
        highScaleNumber,
        htmlFormatted,
        inlineHelpText,
        label,
        length,
        maskType,
        nameField,
        polymorphicForeignKey,
        precision,
        reference,
        referenceTargetField,
        const DeepCollectionEquality().hash(referenceToInfos),
        relationshipName,
        required,
        scale,
        searchPrefilterable,
        sortable,
        unique,
        updateable
      ]);

  @override
  String toString() {
    return 'Field(apiName: $apiName, calculated: $calculated, compound: $compound, compoundComponentName: $compoundComponentName, compoundFieldName: $compoundFieldName, controllerName: $controllerName, controllingFields: $controllingFields, createable: $createable, custom: $custom, dataType: $dataType, extraTypeInfo: $extraTypeInfo, filterable: $filterable, highScaleNumber: $highScaleNumber, htmlFormatted: $htmlFormatted, inlineHelpText: $inlineHelpText, label: $label, length: $length, maskType: $maskType, nameField: $nameField, polymorphicForeignKey: $polymorphicForeignKey, precision: $precision, reference: $reference, referenceTargetField: $referenceTargetField, referenceToInfos: $referenceToInfos, relationshipName: $relationshipName, required: $required, scale: $scale, searchPrefilterable: $searchPrefilterable, sortable: $sortable, unique: $unique, updateable: $updateable)';
  }
}

/// @nodoc
abstract mixin class $FieldCopyWith<$Res> {
  factory $FieldCopyWith(Field value, $Res Function(Field) _then) =
      _$FieldCopyWithImpl;
  @useResult
  $Res call(
      {String? apiName,
      bool calculated,
      bool compound,
      String? compoundComponentName,
      String? compoundFieldName,
      String? controllerName,
      List<String> controllingFields,
      bool createable,
      bool custom,
      String? dataType,
      String? extraTypeInfo,
      bool filterable,
      bool highScaleNumber,
      bool htmlFormatted,
      String? inlineHelpText,
      String? label,
      int? length,
      String? maskType,
      bool nameField,
      bool polymorphicForeignKey,
      int? precision,
      bool reference,
      String? referenceTargetField,
      Map<String, dynamic> referenceToInfos,
      String? relationshipName,
      bool required,
      int? scale,
      bool searchPrefilterable,
      bool sortable,
      bool unique,
      bool updateable});
}

/// @nodoc
class _$FieldCopyWithImpl<$Res> implements $FieldCopyWith<$Res> {
  _$FieldCopyWithImpl(this._self, this._then);

  final Field _self;
  final $Res Function(Field) _then;

  /// Create a copy of Field
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiName = freezed,
    Object? calculated = null,
    Object? compound = null,
    Object? compoundComponentName = freezed,
    Object? compoundFieldName = freezed,
    Object? controllerName = freezed,
    Object? controllingFields = null,
    Object? createable = null,
    Object? custom = null,
    Object? dataType = freezed,
    Object? extraTypeInfo = freezed,
    Object? filterable = null,
    Object? highScaleNumber = null,
    Object? htmlFormatted = null,
    Object? inlineHelpText = freezed,
    Object? label = freezed,
    Object? length = freezed,
    Object? maskType = freezed,
    Object? nameField = null,
    Object? polymorphicForeignKey = null,
    Object? precision = freezed,
    Object? reference = null,
    Object? referenceTargetField = freezed,
    Object? referenceToInfos = null,
    Object? relationshipName = freezed,
    Object? required = null,
    Object? scale = freezed,
    Object? searchPrefilterable = null,
    Object? sortable = null,
    Object? unique = null,
    Object? updateable = null,
  }) {
    return _then(_self.copyWith(
      apiName: freezed == apiName
          ? _self.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      calculated: null == calculated
          ? _self.calculated
          : calculated // ignore: cast_nullable_to_non_nullable
              as bool,
      compound: null == compound
          ? _self.compound
          : compound // ignore: cast_nullable_to_non_nullable
              as bool,
      compoundComponentName: freezed == compoundComponentName
          ? _self.compoundComponentName
          : compoundComponentName // ignore: cast_nullable_to_non_nullable
              as String?,
      compoundFieldName: freezed == compoundFieldName
          ? _self.compoundFieldName
          : compoundFieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllerName: freezed == controllerName
          ? _self.controllerName
          : controllerName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllingFields: null == controllingFields
          ? _self.controllingFields
          : controllingFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createable: null == createable
          ? _self.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _self.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      dataType: freezed == dataType
          ? _self.dataType
          : dataType // ignore: cast_nullable_to_non_nullable
              as String?,
      extraTypeInfo: freezed == extraTypeInfo
          ? _self.extraTypeInfo
          : extraTypeInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      filterable: null == filterable
          ? _self.filterable
          : filterable // ignore: cast_nullable_to_non_nullable
              as bool,
      highScaleNumber: null == highScaleNumber
          ? _self.highScaleNumber
          : highScaleNumber // ignore: cast_nullable_to_non_nullable
              as bool,
      htmlFormatted: null == htmlFormatted
          ? _self.htmlFormatted
          : htmlFormatted // ignore: cast_nullable_to_non_nullable
              as bool,
      inlineHelpText: freezed == inlineHelpText
          ? _self.inlineHelpText
          : inlineHelpText // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      length: freezed == length
          ? _self.length
          : length // ignore: cast_nullable_to_non_nullable
              as int?,
      maskType: freezed == maskType
          ? _self.maskType
          : maskType // ignore: cast_nullable_to_non_nullable
              as String?,
      nameField: null == nameField
          ? _self.nameField
          : nameField // ignore: cast_nullable_to_non_nullable
              as bool,
      polymorphicForeignKey: null == polymorphicForeignKey
          ? _self.polymorphicForeignKey
          : polymorphicForeignKey // ignore: cast_nullable_to_non_nullable
              as bool,
      precision: freezed == precision
          ? _self.precision
          : precision // ignore: cast_nullable_to_non_nullable
              as int?,
      reference: null == reference
          ? _self.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as bool,
      referenceTargetField: freezed == referenceTargetField
          ? _self.referenceTargetField
          : referenceTargetField // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceToInfos: null == referenceToInfos
          ? _self.referenceToInfos
          : referenceToInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      relationshipName: freezed == relationshipName
          ? _self.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
      required: null == required
          ? _self.required
          : required // ignore: cast_nullable_to_non_nullable
              as bool,
      scale: freezed == scale
          ? _self.scale
          : scale // ignore: cast_nullable_to_non_nullable
              as int?,
      searchPrefilterable: null == searchPrefilterable
          ? _self.searchPrefilterable
          : searchPrefilterable // ignore: cast_nullable_to_non_nullable
              as bool,
      sortable: null == sortable
          ? _self.sortable
          : sortable // ignore: cast_nullable_to_non_nullable
              as bool,
      unique: null == unique
          ? _self.unique
          : unique // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _self.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [Field].
extension FieldPatterns on Field {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Field value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Field() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Field value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Field():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Field value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Field() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? apiName,
            bool calculated,
            bool compound,
            String? compoundComponentName,
            String? compoundFieldName,
            String? controllerName,
            List<String> controllingFields,
            bool createable,
            bool custom,
            String? dataType,
            String? extraTypeInfo,
            bool filterable,
            bool highScaleNumber,
            bool htmlFormatted,
            String? inlineHelpText,
            String? label,
            int? length,
            String? maskType,
            bool nameField,
            bool polymorphicForeignKey,
            int? precision,
            bool reference,
            String? referenceTargetField,
            Map<String, dynamic> referenceToInfos,
            String? relationshipName,
            bool required,
            int? scale,
            bool searchPrefilterable,
            bool sortable,
            bool unique,
            bool updateable)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Field() when $default != null:
        return $default(
            _that.apiName,
            _that.calculated,
            _that.compound,
            _that.compoundComponentName,
            _that.compoundFieldName,
            _that.controllerName,
            _that.controllingFields,
            _that.createable,
            _that.custom,
            _that.dataType,
            _that.extraTypeInfo,
            _that.filterable,
            _that.highScaleNumber,
            _that.htmlFormatted,
            _that.inlineHelpText,
            _that.label,
            _that.length,
            _that.maskType,
            _that.nameField,
            _that.polymorphicForeignKey,
            _that.precision,
            _that.reference,
            _that.referenceTargetField,
            _that.referenceToInfos,
            _that.relationshipName,
            _that.required,
            _that.scale,
            _that.searchPrefilterable,
            _that.sortable,
            _that.unique,
            _that.updateable);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? apiName,
            bool calculated,
            bool compound,
            String? compoundComponentName,
            String? compoundFieldName,
            String? controllerName,
            List<String> controllingFields,
            bool createable,
            bool custom,
            String? dataType,
            String? extraTypeInfo,
            bool filterable,
            bool highScaleNumber,
            bool htmlFormatted,
            String? inlineHelpText,
            String? label,
            int? length,
            String? maskType,
            bool nameField,
            bool polymorphicForeignKey,
            int? precision,
            bool reference,
            String? referenceTargetField,
            Map<String, dynamic> referenceToInfos,
            String? relationshipName,
            bool required,
            int? scale,
            bool searchPrefilterable,
            bool sortable,
            bool unique,
            bool updateable)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Field():
        return $default(
            _that.apiName,
            _that.calculated,
            _that.compound,
            _that.compoundComponentName,
            _that.compoundFieldName,
            _that.controllerName,
            _that.controllingFields,
            _that.createable,
            _that.custom,
            _that.dataType,
            _that.extraTypeInfo,
            _that.filterable,
            _that.highScaleNumber,
            _that.htmlFormatted,
            _that.inlineHelpText,
            _that.label,
            _that.length,
            _that.maskType,
            _that.nameField,
            _that.polymorphicForeignKey,
            _that.precision,
            _that.reference,
            _that.referenceTargetField,
            _that.referenceToInfos,
            _that.relationshipName,
            _that.required,
            _that.scale,
            _that.searchPrefilterable,
            _that.sortable,
            _that.unique,
            _that.updateable);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? apiName,
            bool calculated,
            bool compound,
            String? compoundComponentName,
            String? compoundFieldName,
            String? controllerName,
            List<String> controllingFields,
            bool createable,
            bool custom,
            String? dataType,
            String? extraTypeInfo,
            bool filterable,
            bool highScaleNumber,
            bool htmlFormatted,
            String? inlineHelpText,
            String? label,
            int? length,
            String? maskType,
            bool nameField,
            bool polymorphicForeignKey,
            int? precision,
            bool reference,
            String? referenceTargetField,
            Map<String, dynamic> referenceToInfos,
            String? relationshipName,
            bool required,
            int? scale,
            bool searchPrefilterable,
            bool sortable,
            bool unique,
            bool updateable)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _Field() when $default != null:
        return $default(
            _that.apiName,
            _that.calculated,
            _that.compound,
            _that.compoundComponentName,
            _that.compoundFieldName,
            _that.controllerName,
            _that.controllingFields,
            _that.createable,
            _that.custom,
            _that.dataType,
            _that.extraTypeInfo,
            _that.filterable,
            _that.highScaleNumber,
            _that.htmlFormatted,
            _that.inlineHelpText,
            _that.label,
            _that.length,
            _that.maskType,
            _that.nameField,
            _that.polymorphicForeignKey,
            _that.precision,
            _that.reference,
            _that.referenceTargetField,
            _that.referenceToInfos,
            _that.relationshipName,
            _that.required,
            _that.scale,
            _that.searchPrefilterable,
            _that.sortable,
            _that.unique,
            _that.updateable);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _Field implements Field {
  const _Field(
      {this.apiName,
      this.calculated = false,
      this.compound = false,
      this.compoundComponentName,
      this.compoundFieldName,
      this.controllerName,
      final List<String> controllingFields = const [],
      this.createable = false,
      this.custom = false,
      this.dataType,
      this.extraTypeInfo,
      this.filterable = false,
      this.highScaleNumber = false,
      this.htmlFormatted = false,
      this.inlineHelpText,
      this.label,
      this.length,
      this.maskType,
      this.nameField = false,
      this.polymorphicForeignKey = false,
      this.precision,
      this.reference = false,
      this.referenceTargetField,
      final Map<String, dynamic> referenceToInfos = const {},
      this.relationshipName,
      this.required = false,
      this.scale,
      this.searchPrefilterable = false,
      this.sortable = false,
      this.unique = false,
      this.updateable = false})
      : _controllingFields = controllingFields,
        _referenceToInfos = referenceToInfos;
  factory _Field.fromJson(Map<String, dynamic> json) => _$FieldFromJson(json);

  @override
  final String? apiName;
  @override
  @JsonKey()
  final bool calculated;
  @override
  @JsonKey()
  final bool compound;
  @override
  final String? compoundComponentName;
  @override
  final String? compoundFieldName;
  @override
  final String? controllerName;
  final List<String> _controllingFields;
  @override
  @JsonKey()
  List<String> get controllingFields {
    if (_controllingFields is EqualUnmodifiableListView)
      return _controllingFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_controllingFields);
  }

  @override
  @JsonKey()
  final bool createable;
  @override
  @JsonKey()
  final bool custom;
  @override
  final String? dataType;

  /// this can come as bool:false
// String? externalId,
  @override
  final String? extraTypeInfo;
  @override
  @JsonKey()
  final bool filterable;
// filteredLookupInfo; SF Type: Filtered Lookup Info: https://developer.salesforce.com/docs/atlas.en-us.uiapi.meta/uiapi/ui_api_responses_filtered_lookup_info.htm#ui_api_responses_filtered_lookup_info
  @override
  @JsonKey()
  final bool highScaleNumber;
  @override
  @JsonKey()
  final bool htmlFormatted;
  @override
  final String? inlineHelpText;
  @override
  final String? label;
  @override
  final int? length;
  @override
  final String? maskType;
  @override
  @JsonKey()
  final bool nameField;
  @override
  @JsonKey()
  final bool polymorphicForeignKey;
  @override
  final int? precision;
  @override
  @JsonKey()
  final bool reference;
  @override
  final String? referenceTargetField;
  final Map<String, dynamic> _referenceToInfos;
  @override
  @JsonKey()
  Map<String, dynamic> get referenceToInfos {
    if (_referenceToInfos is EqualUnmodifiableMapView) return _referenceToInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_referenceToInfos);
  }

  @override
  final String? relationshipName;
  @override
  @JsonKey()
  final bool required;
  @override
  final int? scale;
  @override
  @JsonKey()
  final bool searchPrefilterable;
  @override
  @JsonKey()
  final bool sortable;
  @override
  @JsonKey()
  final bool unique;
  @override
  @JsonKey()
  final bool updateable;

  /// Create a copy of Field
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FieldCopyWith<_Field> get copyWith =>
      __$FieldCopyWithImpl<_Field>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FieldToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Field &&
            (identical(other.apiName, apiName) || other.apiName == apiName) &&
            (identical(other.calculated, calculated) ||
                other.calculated == calculated) &&
            (identical(other.compound, compound) ||
                other.compound == compound) &&
            (identical(other.compoundComponentName, compoundComponentName) ||
                other.compoundComponentName == compoundComponentName) &&
            (identical(other.compoundFieldName, compoundFieldName) ||
                other.compoundFieldName == compoundFieldName) &&
            (identical(other.controllerName, controllerName) ||
                other.controllerName == controllerName) &&
            const DeepCollectionEquality()
                .equals(other._controllingFields, _controllingFields) &&
            (identical(other.createable, createable) ||
                other.createable == createable) &&
            (identical(other.custom, custom) || other.custom == custom) &&
            (identical(other.dataType, dataType) ||
                other.dataType == dataType) &&
            (identical(other.extraTypeInfo, extraTypeInfo) ||
                other.extraTypeInfo == extraTypeInfo) &&
            (identical(other.filterable, filterable) ||
                other.filterable == filterable) &&
            (identical(other.highScaleNumber, highScaleNumber) ||
                other.highScaleNumber == highScaleNumber) &&
            (identical(other.htmlFormatted, htmlFormatted) ||
                other.htmlFormatted == htmlFormatted) &&
            (identical(other.inlineHelpText, inlineHelpText) ||
                other.inlineHelpText == inlineHelpText) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.maskType, maskType) ||
                other.maskType == maskType) &&
            (identical(other.nameField, nameField) ||
                other.nameField == nameField) &&
            (identical(other.polymorphicForeignKey, polymorphicForeignKey) ||
                other.polymorphicForeignKey == polymorphicForeignKey) &&
            (identical(other.precision, precision) ||
                other.precision == precision) &&
            (identical(other.reference, reference) ||
                other.reference == reference) &&
            (identical(other.referenceTargetField, referenceTargetField) ||
                other.referenceTargetField == referenceTargetField) &&
            const DeepCollectionEquality()
                .equals(other._referenceToInfos, _referenceToInfos) &&
            (identical(other.relationshipName, relationshipName) ||
                other.relationshipName == relationshipName) &&
            (identical(other.required, required) ||
                other.required == required) &&
            (identical(other.scale, scale) || other.scale == scale) &&
            (identical(other.searchPrefilterable, searchPrefilterable) ||
                other.searchPrefilterable == searchPrefilterable) &&
            (identical(other.sortable, sortable) ||
                other.sortable == sortable) &&
            (identical(other.unique, unique) || other.unique == unique) &&
            (identical(other.updateable, updateable) ||
                other.updateable == updateable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        apiName,
        calculated,
        compound,
        compoundComponentName,
        compoundFieldName,
        controllerName,
        const DeepCollectionEquality().hash(_controllingFields),
        createable,
        custom,
        dataType,
        extraTypeInfo,
        filterable,
        highScaleNumber,
        htmlFormatted,
        inlineHelpText,
        label,
        length,
        maskType,
        nameField,
        polymorphicForeignKey,
        precision,
        reference,
        referenceTargetField,
        const DeepCollectionEquality().hash(_referenceToInfos),
        relationshipName,
        required,
        scale,
        searchPrefilterable,
        sortable,
        unique,
        updateable
      ]);

  @override
  String toString() {
    return 'Field(apiName: $apiName, calculated: $calculated, compound: $compound, compoundComponentName: $compoundComponentName, compoundFieldName: $compoundFieldName, controllerName: $controllerName, controllingFields: $controllingFields, createable: $createable, custom: $custom, dataType: $dataType, extraTypeInfo: $extraTypeInfo, filterable: $filterable, highScaleNumber: $highScaleNumber, htmlFormatted: $htmlFormatted, inlineHelpText: $inlineHelpText, label: $label, length: $length, maskType: $maskType, nameField: $nameField, polymorphicForeignKey: $polymorphicForeignKey, precision: $precision, reference: $reference, referenceTargetField: $referenceTargetField, referenceToInfos: $referenceToInfos, relationshipName: $relationshipName, required: $required, scale: $scale, searchPrefilterable: $searchPrefilterable, sortable: $sortable, unique: $unique, updateable: $updateable)';
  }
}

/// @nodoc
abstract mixin class _$FieldCopyWith<$Res> implements $FieldCopyWith<$Res> {
  factory _$FieldCopyWith(_Field value, $Res Function(_Field) _then) =
      __$FieldCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? apiName,
      bool calculated,
      bool compound,
      String? compoundComponentName,
      String? compoundFieldName,
      String? controllerName,
      List<String> controllingFields,
      bool createable,
      bool custom,
      String? dataType,
      String? extraTypeInfo,
      bool filterable,
      bool highScaleNumber,
      bool htmlFormatted,
      String? inlineHelpText,
      String? label,
      int? length,
      String? maskType,
      bool nameField,
      bool polymorphicForeignKey,
      int? precision,
      bool reference,
      String? referenceTargetField,
      Map<String, dynamic> referenceToInfos,
      String? relationshipName,
      bool required,
      int? scale,
      bool searchPrefilterable,
      bool sortable,
      bool unique,
      bool updateable});
}

/// @nodoc
class __$FieldCopyWithImpl<$Res> implements _$FieldCopyWith<$Res> {
  __$FieldCopyWithImpl(this._self, this._then);

  final _Field _self;
  final $Res Function(_Field) _then;

  /// Create a copy of Field
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? apiName = freezed,
    Object? calculated = null,
    Object? compound = null,
    Object? compoundComponentName = freezed,
    Object? compoundFieldName = freezed,
    Object? controllerName = freezed,
    Object? controllingFields = null,
    Object? createable = null,
    Object? custom = null,
    Object? dataType = freezed,
    Object? extraTypeInfo = freezed,
    Object? filterable = null,
    Object? highScaleNumber = null,
    Object? htmlFormatted = null,
    Object? inlineHelpText = freezed,
    Object? label = freezed,
    Object? length = freezed,
    Object? maskType = freezed,
    Object? nameField = null,
    Object? polymorphicForeignKey = null,
    Object? precision = freezed,
    Object? reference = null,
    Object? referenceTargetField = freezed,
    Object? referenceToInfos = null,
    Object? relationshipName = freezed,
    Object? required = null,
    Object? scale = freezed,
    Object? searchPrefilterable = null,
    Object? sortable = null,
    Object? unique = null,
    Object? updateable = null,
  }) {
    return _then(_Field(
      apiName: freezed == apiName
          ? _self.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      calculated: null == calculated
          ? _self.calculated
          : calculated // ignore: cast_nullable_to_non_nullable
              as bool,
      compound: null == compound
          ? _self.compound
          : compound // ignore: cast_nullable_to_non_nullable
              as bool,
      compoundComponentName: freezed == compoundComponentName
          ? _self.compoundComponentName
          : compoundComponentName // ignore: cast_nullable_to_non_nullable
              as String?,
      compoundFieldName: freezed == compoundFieldName
          ? _self.compoundFieldName
          : compoundFieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllerName: freezed == controllerName
          ? _self.controllerName
          : controllerName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllingFields: null == controllingFields
          ? _self._controllingFields
          : controllingFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createable: null == createable
          ? _self.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _self.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      dataType: freezed == dataType
          ? _self.dataType
          : dataType // ignore: cast_nullable_to_non_nullable
              as String?,
      extraTypeInfo: freezed == extraTypeInfo
          ? _self.extraTypeInfo
          : extraTypeInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      filterable: null == filterable
          ? _self.filterable
          : filterable // ignore: cast_nullable_to_non_nullable
              as bool,
      highScaleNumber: null == highScaleNumber
          ? _self.highScaleNumber
          : highScaleNumber // ignore: cast_nullable_to_non_nullable
              as bool,
      htmlFormatted: null == htmlFormatted
          ? _self.htmlFormatted
          : htmlFormatted // ignore: cast_nullable_to_non_nullable
              as bool,
      inlineHelpText: freezed == inlineHelpText
          ? _self.inlineHelpText
          : inlineHelpText // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      length: freezed == length
          ? _self.length
          : length // ignore: cast_nullable_to_non_nullable
              as int?,
      maskType: freezed == maskType
          ? _self.maskType
          : maskType // ignore: cast_nullable_to_non_nullable
              as String?,
      nameField: null == nameField
          ? _self.nameField
          : nameField // ignore: cast_nullable_to_non_nullable
              as bool,
      polymorphicForeignKey: null == polymorphicForeignKey
          ? _self.polymorphicForeignKey
          : polymorphicForeignKey // ignore: cast_nullable_to_non_nullable
              as bool,
      precision: freezed == precision
          ? _self.precision
          : precision // ignore: cast_nullable_to_non_nullable
              as int?,
      reference: null == reference
          ? _self.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as bool,
      referenceTargetField: freezed == referenceTargetField
          ? _self.referenceTargetField
          : referenceTargetField // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceToInfos: null == referenceToInfos
          ? _self._referenceToInfos
          : referenceToInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      relationshipName: freezed == relationshipName
          ? _self.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
      required: null == required
          ? _self.required
          : required // ignore: cast_nullable_to_non_nullable
              as bool,
      scale: freezed == scale
          ? _self.scale
          : scale // ignore: cast_nullable_to_non_nullable
              as int?,
      searchPrefilterable: null == searchPrefilterable
          ? _self.searchPrefilterable
          : searchPrefilterable // ignore: cast_nullable_to_non_nullable
              as bool,
      sortable: null == sortable
          ? _self.sortable
          : sortable // ignore: cast_nullable_to_non_nullable
              as bool,
      unique: null == unique
          ? _self.unique
          : unique // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _self.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

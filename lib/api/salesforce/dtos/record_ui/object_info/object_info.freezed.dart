// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'object_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ObjectInfo {
  String? get apiName;
  String? get associateEntityType;
  String? get associateParentEntity;
  List<ChildRelationship> get childRelationships;
  bool get compactLayoutable;
  bool get createable;
  bool get custom;
  String? get defaultRecordTypeId;
  bool get deleteable;
  Map<String, Object> get dependentFields;
  bool get feedEnabled; // TODO: get Field working ... had issues parsing
// @Default({}) Map<String, Field> fields,
  Map<String, dynamic> get fields;
  String? get keyPrefix;
  String? get label;
  String? get labelPlural;
  bool get layoutable;
  bool get mruEnabled;
  List<String?> get nameFields;
  bool get queryable; // Map<String, RecordTypeInfo> recordTypeInfos,
  bool get searchable;
  bool get searchLayoutable; // ThemeInfo themeInfo,
  bool get updateable;

  /// Create a copy of ObjectInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ObjectInfoCopyWith<ObjectInfo> get copyWith =>
      _$ObjectInfoCopyWithImpl<ObjectInfo>(this as ObjectInfo, _$identity);

  /// Serializes this ObjectInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ObjectInfo &&
            (identical(other.apiName, apiName) || other.apiName == apiName) &&
            (identical(other.associateEntityType, associateEntityType) ||
                other.associateEntityType == associateEntityType) &&
            (identical(other.associateParentEntity, associateParentEntity) ||
                other.associateParentEntity == associateParentEntity) &&
            const DeepCollectionEquality()
                .equals(other.childRelationships, childRelationships) &&
            (identical(other.compactLayoutable, compactLayoutable) ||
                other.compactLayoutable == compactLayoutable) &&
            (identical(other.createable, createable) ||
                other.createable == createable) &&
            (identical(other.custom, custom) || other.custom == custom) &&
            (identical(other.defaultRecordTypeId, defaultRecordTypeId) ||
                other.defaultRecordTypeId == defaultRecordTypeId) &&
            (identical(other.deleteable, deleteable) ||
                other.deleteable == deleteable) &&
            const DeepCollectionEquality()
                .equals(other.dependentFields, dependentFields) &&
            (identical(other.feedEnabled, feedEnabled) ||
                other.feedEnabled == feedEnabled) &&
            const DeepCollectionEquality().equals(other.fields, fields) &&
            (identical(other.keyPrefix, keyPrefix) ||
                other.keyPrefix == keyPrefix) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.labelPlural, labelPlural) ||
                other.labelPlural == labelPlural) &&
            (identical(other.layoutable, layoutable) ||
                other.layoutable == layoutable) &&
            (identical(other.mruEnabled, mruEnabled) ||
                other.mruEnabled == mruEnabled) &&
            const DeepCollectionEquality()
                .equals(other.nameFields, nameFields) &&
            (identical(other.queryable, queryable) ||
                other.queryable == queryable) &&
            (identical(other.searchable, searchable) ||
                other.searchable == searchable) &&
            (identical(other.searchLayoutable, searchLayoutable) ||
                other.searchLayoutable == searchLayoutable) &&
            (identical(other.updateable, updateable) ||
                other.updateable == updateable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        apiName,
        associateEntityType,
        associateParentEntity,
        const DeepCollectionEquality().hash(childRelationships),
        compactLayoutable,
        createable,
        custom,
        defaultRecordTypeId,
        deleteable,
        const DeepCollectionEquality().hash(dependentFields),
        feedEnabled,
        const DeepCollectionEquality().hash(fields),
        keyPrefix,
        label,
        labelPlural,
        layoutable,
        mruEnabled,
        const DeepCollectionEquality().hash(nameFields),
        queryable,
        searchable,
        searchLayoutable,
        updateable
      ]);

  @override
  String toString() {
    return 'ObjectInfo(apiName: $apiName, associateEntityType: $associateEntityType, associateParentEntity: $associateParentEntity, childRelationships: $childRelationships, compactLayoutable: $compactLayoutable, createable: $createable, custom: $custom, defaultRecordTypeId: $defaultRecordTypeId, deleteable: $deleteable, dependentFields: $dependentFields, feedEnabled: $feedEnabled, fields: $fields, keyPrefix: $keyPrefix, label: $label, labelPlural: $labelPlural, layoutable: $layoutable, mruEnabled: $mruEnabled, nameFields: $nameFields, queryable: $queryable, searchable: $searchable, searchLayoutable: $searchLayoutable, updateable: $updateable)';
  }
}

/// @nodoc
abstract mixin class $ObjectInfoCopyWith<$Res> {
  factory $ObjectInfoCopyWith(
          ObjectInfo value, $Res Function(ObjectInfo) _then) =
      _$ObjectInfoCopyWithImpl;
  @useResult
  $Res call(
      {String? apiName,
      String? associateEntityType,
      String? associateParentEntity,
      List<ChildRelationship> childRelationships,
      bool compactLayoutable,
      bool createable,
      bool custom,
      String? defaultRecordTypeId,
      bool deleteable,
      Map<String, Object> dependentFields,
      bool feedEnabled,
      Map<String, dynamic> fields,
      String? keyPrefix,
      String? label,
      String? labelPlural,
      bool layoutable,
      bool mruEnabled,
      List<String?> nameFields,
      bool queryable,
      bool searchable,
      bool searchLayoutable,
      bool updateable});
}

/// @nodoc
class _$ObjectInfoCopyWithImpl<$Res> implements $ObjectInfoCopyWith<$Res> {
  _$ObjectInfoCopyWithImpl(this._self, this._then);

  final ObjectInfo _self;
  final $Res Function(ObjectInfo) _then;

  /// Create a copy of ObjectInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiName = freezed,
    Object? associateEntityType = freezed,
    Object? associateParentEntity = freezed,
    Object? childRelationships = null,
    Object? compactLayoutable = null,
    Object? createable = null,
    Object? custom = null,
    Object? defaultRecordTypeId = freezed,
    Object? deleteable = null,
    Object? dependentFields = null,
    Object? feedEnabled = null,
    Object? fields = null,
    Object? keyPrefix = freezed,
    Object? label = freezed,
    Object? labelPlural = freezed,
    Object? layoutable = null,
    Object? mruEnabled = null,
    Object? nameFields = null,
    Object? queryable = null,
    Object? searchable = null,
    Object? searchLayoutable = null,
    Object? updateable = null,
  }) {
    return _then(_self.copyWith(
      apiName: freezed == apiName
          ? _self.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      associateEntityType: freezed == associateEntityType
          ? _self.associateEntityType
          : associateEntityType // ignore: cast_nullable_to_non_nullable
              as String?,
      associateParentEntity: freezed == associateParentEntity
          ? _self.associateParentEntity
          : associateParentEntity // ignore: cast_nullable_to_non_nullable
              as String?,
      childRelationships: null == childRelationships
          ? _self.childRelationships
          : childRelationships // ignore: cast_nullable_to_non_nullable
              as List<ChildRelationship>,
      compactLayoutable: null == compactLayoutable
          ? _self.compactLayoutable
          : compactLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      createable: null == createable
          ? _self.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _self.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultRecordTypeId: freezed == defaultRecordTypeId
          ? _self.defaultRecordTypeId
          : defaultRecordTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      deleteable: null == deleteable
          ? _self.deleteable
          : deleteable // ignore: cast_nullable_to_non_nullable
              as bool,
      dependentFields: null == dependentFields
          ? _self.dependentFields
          : dependentFields // ignore: cast_nullable_to_non_nullable
              as Map<String, Object>,
      feedEnabled: null == feedEnabled
          ? _self.feedEnabled
          : feedEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _self.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      keyPrefix: freezed == keyPrefix
          ? _self.keyPrefix
          : keyPrefix // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      labelPlural: freezed == labelPlural
          ? _self.labelPlural
          : labelPlural // ignore: cast_nullable_to_non_nullable
              as String?,
      layoutable: null == layoutable
          ? _self.layoutable
          : layoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      mruEnabled: null == mruEnabled
          ? _self.mruEnabled
          : mruEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      nameFields: null == nameFields
          ? _self.nameFields
          : nameFields // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      queryable: null == queryable
          ? _self.queryable
          : queryable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchable: null == searchable
          ? _self.searchable
          : searchable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchLayoutable: null == searchLayoutable
          ? _self.searchLayoutable
          : searchLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _self.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [ObjectInfo].
extension ObjectInfoPatterns on ObjectInfo {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ObjectInfo value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ObjectInfo() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ObjectInfo value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ObjectInfo():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ObjectInfo value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ObjectInfo() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? apiName,
            String? associateEntityType,
            String? associateParentEntity,
            List<ChildRelationship> childRelationships,
            bool compactLayoutable,
            bool createable,
            bool custom,
            String? defaultRecordTypeId,
            bool deleteable,
            Map<String, Object> dependentFields,
            bool feedEnabled,
            Map<String, dynamic> fields,
            String? keyPrefix,
            String? label,
            String? labelPlural,
            bool layoutable,
            bool mruEnabled,
            List<String?> nameFields,
            bool queryable,
            bool searchable,
            bool searchLayoutable,
            bool updateable)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ObjectInfo() when $default != null:
        return $default(
            _that.apiName,
            _that.associateEntityType,
            _that.associateParentEntity,
            _that.childRelationships,
            _that.compactLayoutable,
            _that.createable,
            _that.custom,
            _that.defaultRecordTypeId,
            _that.deleteable,
            _that.dependentFields,
            _that.feedEnabled,
            _that.fields,
            _that.keyPrefix,
            _that.label,
            _that.labelPlural,
            _that.layoutable,
            _that.mruEnabled,
            _that.nameFields,
            _that.queryable,
            _that.searchable,
            _that.searchLayoutable,
            _that.updateable);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? apiName,
            String? associateEntityType,
            String? associateParentEntity,
            List<ChildRelationship> childRelationships,
            bool compactLayoutable,
            bool createable,
            bool custom,
            String? defaultRecordTypeId,
            bool deleteable,
            Map<String, Object> dependentFields,
            bool feedEnabled,
            Map<String, dynamic> fields,
            String? keyPrefix,
            String? label,
            String? labelPlural,
            bool layoutable,
            bool mruEnabled,
            List<String?> nameFields,
            bool queryable,
            bool searchable,
            bool searchLayoutable,
            bool updateable)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ObjectInfo():
        return $default(
            _that.apiName,
            _that.associateEntityType,
            _that.associateParentEntity,
            _that.childRelationships,
            _that.compactLayoutable,
            _that.createable,
            _that.custom,
            _that.defaultRecordTypeId,
            _that.deleteable,
            _that.dependentFields,
            _that.feedEnabled,
            _that.fields,
            _that.keyPrefix,
            _that.label,
            _that.labelPlural,
            _that.layoutable,
            _that.mruEnabled,
            _that.nameFields,
            _that.queryable,
            _that.searchable,
            _that.searchLayoutable,
            _that.updateable);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? apiName,
            String? associateEntityType,
            String? associateParentEntity,
            List<ChildRelationship> childRelationships,
            bool compactLayoutable,
            bool createable,
            bool custom,
            String? defaultRecordTypeId,
            bool deleteable,
            Map<String, Object> dependentFields,
            bool feedEnabled,
            Map<String, dynamic> fields,
            String? keyPrefix,
            String? label,
            String? labelPlural,
            bool layoutable,
            bool mruEnabled,
            List<String?> nameFields,
            bool queryable,
            bool searchable,
            bool searchLayoutable,
            bool updateable)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ObjectInfo() when $default != null:
        return $default(
            _that.apiName,
            _that.associateEntityType,
            _that.associateParentEntity,
            _that.childRelationships,
            _that.compactLayoutable,
            _that.createable,
            _that.custom,
            _that.defaultRecordTypeId,
            _that.deleteable,
            _that.dependentFields,
            _that.feedEnabled,
            _that.fields,
            _that.keyPrefix,
            _that.label,
            _that.labelPlural,
            _that.layoutable,
            _that.mruEnabled,
            _that.nameFields,
            _that.queryable,
            _that.searchable,
            _that.searchLayoutable,
            _that.updateable);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ObjectInfo implements ObjectInfo {
  const _ObjectInfo(
      {this.apiName,
      this.associateEntityType,
      this.associateParentEntity,
      final List<ChildRelationship> childRelationships = const [],
      this.compactLayoutable = false,
      this.createable = false,
      this.custom = false,
      this.defaultRecordTypeId,
      this.deleteable = false,
      final Map<String, Object> dependentFields = const {},
      this.feedEnabled = false,
      final Map<String, dynamic> fields = const {},
      this.keyPrefix,
      this.label,
      this.labelPlural,
      this.layoutable = false,
      this.mruEnabled = false,
      final List<String?> nameFields = const [],
      this.queryable = false,
      this.searchable = false,
      this.searchLayoutable = false,
      this.updateable = false})
      : _childRelationships = childRelationships,
        _dependentFields = dependentFields,
        _fields = fields,
        _nameFields = nameFields;
  factory _ObjectInfo.fromJson(Map<String, dynamic> json) =>
      _$ObjectInfoFromJson(json);

  @override
  final String? apiName;
  @override
  final String? associateEntityType;
  @override
  final String? associateParentEntity;
  final List<ChildRelationship> _childRelationships;
  @override
  @JsonKey()
  List<ChildRelationship> get childRelationships {
    if (_childRelationships is EqualUnmodifiableListView)
      return _childRelationships;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_childRelationships);
  }

  @override
  @JsonKey()
  final bool compactLayoutable;
  @override
  @JsonKey()
  final bool createable;
  @override
  @JsonKey()
  final bool custom;
  @override
  final String? defaultRecordTypeId;
  @override
  @JsonKey()
  final bool deleteable;
  final Map<String, Object> _dependentFields;
  @override
  @JsonKey()
  Map<String, Object> get dependentFields {
    if (_dependentFields is EqualUnmodifiableMapView) return _dependentFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_dependentFields);
  }

  @override
  @JsonKey()
  final bool feedEnabled;
// TODO: get Field working ... had issues parsing
// @Default({}) Map<String, Field> fields,
  final Map<String, dynamic> _fields;
// TODO: get Field working ... had issues parsing
// @Default({}) Map<String, Field> fields,
  @override
  @JsonKey()
  Map<String, dynamic> get fields {
    if (_fields is EqualUnmodifiableMapView) return _fields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fields);
  }

  @override
  final String? keyPrefix;
  @override
  final String? label;
  @override
  final String? labelPlural;
  @override
  @JsonKey()
  final bool layoutable;
  @override
  @JsonKey()
  final bool mruEnabled;
  final List<String?> _nameFields;
  @override
  @JsonKey()
  List<String?> get nameFields {
    if (_nameFields is EqualUnmodifiableListView) return _nameFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_nameFields);
  }

  @override
  @JsonKey()
  final bool queryable;
// Map<String, RecordTypeInfo> recordTypeInfos,
  @override
  @JsonKey()
  final bool searchable;
  @override
  @JsonKey()
  final bool searchLayoutable;
// ThemeInfo themeInfo,
  @override
  @JsonKey()
  final bool updateable;

  /// Create a copy of ObjectInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ObjectInfoCopyWith<_ObjectInfo> get copyWith =>
      __$ObjectInfoCopyWithImpl<_ObjectInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ObjectInfoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ObjectInfo &&
            (identical(other.apiName, apiName) || other.apiName == apiName) &&
            (identical(other.associateEntityType, associateEntityType) ||
                other.associateEntityType == associateEntityType) &&
            (identical(other.associateParentEntity, associateParentEntity) ||
                other.associateParentEntity == associateParentEntity) &&
            const DeepCollectionEquality()
                .equals(other._childRelationships, _childRelationships) &&
            (identical(other.compactLayoutable, compactLayoutable) ||
                other.compactLayoutable == compactLayoutable) &&
            (identical(other.createable, createable) ||
                other.createable == createable) &&
            (identical(other.custom, custom) || other.custom == custom) &&
            (identical(other.defaultRecordTypeId, defaultRecordTypeId) ||
                other.defaultRecordTypeId == defaultRecordTypeId) &&
            (identical(other.deleteable, deleteable) ||
                other.deleteable == deleteable) &&
            const DeepCollectionEquality()
                .equals(other._dependentFields, _dependentFields) &&
            (identical(other.feedEnabled, feedEnabled) ||
                other.feedEnabled == feedEnabled) &&
            const DeepCollectionEquality().equals(other._fields, _fields) &&
            (identical(other.keyPrefix, keyPrefix) ||
                other.keyPrefix == keyPrefix) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.labelPlural, labelPlural) ||
                other.labelPlural == labelPlural) &&
            (identical(other.layoutable, layoutable) ||
                other.layoutable == layoutable) &&
            (identical(other.mruEnabled, mruEnabled) ||
                other.mruEnabled == mruEnabled) &&
            const DeepCollectionEquality()
                .equals(other._nameFields, _nameFields) &&
            (identical(other.queryable, queryable) ||
                other.queryable == queryable) &&
            (identical(other.searchable, searchable) ||
                other.searchable == searchable) &&
            (identical(other.searchLayoutable, searchLayoutable) ||
                other.searchLayoutable == searchLayoutable) &&
            (identical(other.updateable, updateable) ||
                other.updateable == updateable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        apiName,
        associateEntityType,
        associateParentEntity,
        const DeepCollectionEquality().hash(_childRelationships),
        compactLayoutable,
        createable,
        custom,
        defaultRecordTypeId,
        deleteable,
        const DeepCollectionEquality().hash(_dependentFields),
        feedEnabled,
        const DeepCollectionEquality().hash(_fields),
        keyPrefix,
        label,
        labelPlural,
        layoutable,
        mruEnabled,
        const DeepCollectionEquality().hash(_nameFields),
        queryable,
        searchable,
        searchLayoutable,
        updateable
      ]);

  @override
  String toString() {
    return 'ObjectInfo(apiName: $apiName, associateEntityType: $associateEntityType, associateParentEntity: $associateParentEntity, childRelationships: $childRelationships, compactLayoutable: $compactLayoutable, createable: $createable, custom: $custom, defaultRecordTypeId: $defaultRecordTypeId, deleteable: $deleteable, dependentFields: $dependentFields, feedEnabled: $feedEnabled, fields: $fields, keyPrefix: $keyPrefix, label: $label, labelPlural: $labelPlural, layoutable: $layoutable, mruEnabled: $mruEnabled, nameFields: $nameFields, queryable: $queryable, searchable: $searchable, searchLayoutable: $searchLayoutable, updateable: $updateable)';
  }
}

/// @nodoc
abstract mixin class _$ObjectInfoCopyWith<$Res>
    implements $ObjectInfoCopyWith<$Res> {
  factory _$ObjectInfoCopyWith(
          _ObjectInfo value, $Res Function(_ObjectInfo) _then) =
      __$ObjectInfoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? apiName,
      String? associateEntityType,
      String? associateParentEntity,
      List<ChildRelationship> childRelationships,
      bool compactLayoutable,
      bool createable,
      bool custom,
      String? defaultRecordTypeId,
      bool deleteable,
      Map<String, Object> dependentFields,
      bool feedEnabled,
      Map<String, dynamic> fields,
      String? keyPrefix,
      String? label,
      String? labelPlural,
      bool layoutable,
      bool mruEnabled,
      List<String?> nameFields,
      bool queryable,
      bool searchable,
      bool searchLayoutable,
      bool updateable});
}

/// @nodoc
class __$ObjectInfoCopyWithImpl<$Res> implements _$ObjectInfoCopyWith<$Res> {
  __$ObjectInfoCopyWithImpl(this._self, this._then);

  final _ObjectInfo _self;
  final $Res Function(_ObjectInfo) _then;

  /// Create a copy of ObjectInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? apiName = freezed,
    Object? associateEntityType = freezed,
    Object? associateParentEntity = freezed,
    Object? childRelationships = null,
    Object? compactLayoutable = null,
    Object? createable = null,
    Object? custom = null,
    Object? defaultRecordTypeId = freezed,
    Object? deleteable = null,
    Object? dependentFields = null,
    Object? feedEnabled = null,
    Object? fields = null,
    Object? keyPrefix = freezed,
    Object? label = freezed,
    Object? labelPlural = freezed,
    Object? layoutable = null,
    Object? mruEnabled = null,
    Object? nameFields = null,
    Object? queryable = null,
    Object? searchable = null,
    Object? searchLayoutable = null,
    Object? updateable = null,
  }) {
    return _then(_ObjectInfo(
      apiName: freezed == apiName
          ? _self.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      associateEntityType: freezed == associateEntityType
          ? _self.associateEntityType
          : associateEntityType // ignore: cast_nullable_to_non_nullable
              as String?,
      associateParentEntity: freezed == associateParentEntity
          ? _self.associateParentEntity
          : associateParentEntity // ignore: cast_nullable_to_non_nullable
              as String?,
      childRelationships: null == childRelationships
          ? _self._childRelationships
          : childRelationships // ignore: cast_nullable_to_non_nullable
              as List<ChildRelationship>,
      compactLayoutable: null == compactLayoutable
          ? _self.compactLayoutable
          : compactLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      createable: null == createable
          ? _self.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _self.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultRecordTypeId: freezed == defaultRecordTypeId
          ? _self.defaultRecordTypeId
          : defaultRecordTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      deleteable: null == deleteable
          ? _self.deleteable
          : deleteable // ignore: cast_nullable_to_non_nullable
              as bool,
      dependentFields: null == dependentFields
          ? _self._dependentFields
          : dependentFields // ignore: cast_nullable_to_non_nullable
              as Map<String, Object>,
      feedEnabled: null == feedEnabled
          ? _self.feedEnabled
          : feedEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _self._fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      keyPrefix: freezed == keyPrefix
          ? _self.keyPrefix
          : keyPrefix // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      labelPlural: freezed == labelPlural
          ? _self.labelPlural
          : labelPlural // ignore: cast_nullable_to_non_nullable
              as String?,
      layoutable: null == layoutable
          ? _self.layoutable
          : layoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      mruEnabled: null == mruEnabled
          ? _self.mruEnabled
          : mruEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      nameFields: null == nameFields
          ? _self._nameFields
          : nameFields // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      queryable: null == queryable
          ? _self.queryable
          : queryable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchable: null == searchable
          ? _self.searchable
          : searchable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchLayoutable: null == searchLayoutable
          ? _self.searchLayoutable
          : searchLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _self.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'child_relationship.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Child<PERSON>elationship {
  String? get childObjectApiName;
  String? get fieldName;
  List<String> get junctionIdListNames;
  List<String> get junctionReferenceTo;
  String? get relationshipName;

  /// Create a copy of ChildRelationship
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChildRelationshipCopyWith<ChildRelationship> get copyWith =>
      _$ChildRelationshipCopyWithImpl<ChildRelationship>(
          this as ChildRelationship, _$identity);

  /// Serializes this ChildRelationship to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChildRelationship &&
            (identical(other.childObjectApiName, childObjectApiName) ||
                other.childObjectApiName == childObjectApiName) &&
            (identical(other.fieldName, fieldName) ||
                other.fieldName == fieldName) &&
            const DeepCollectionEquality()
                .equals(other.junctionIdListNames, junctionIdListNames) &&
            const DeepCollectionEquality()
                .equals(other.junctionReferenceTo, junctionReferenceTo) &&
            (identical(other.relationshipName, relationshipName) ||
                other.relationshipName == relationshipName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      childObjectApiName,
      fieldName,
      const DeepCollectionEquality().hash(junctionIdListNames),
      const DeepCollectionEquality().hash(junctionReferenceTo),
      relationshipName);

  @override
  String toString() {
    return 'ChildRelationship(childObjectApiName: $childObjectApiName, fieldName: $fieldName, junctionIdListNames: $junctionIdListNames, junctionReferenceTo: $junctionReferenceTo, relationshipName: $relationshipName)';
  }
}

/// @nodoc
abstract mixin class $ChildRelationshipCopyWith<$Res> {
  factory $ChildRelationshipCopyWith(
          ChildRelationship value, $Res Function(ChildRelationship) _then) =
      _$ChildRelationshipCopyWithImpl;
  @useResult
  $Res call(
      {String? childObjectApiName,
      String? fieldName,
      List<String> junctionIdListNames,
      List<String> junctionReferenceTo,
      String? relationshipName});
}

/// @nodoc
class _$ChildRelationshipCopyWithImpl<$Res>
    implements $ChildRelationshipCopyWith<$Res> {
  _$ChildRelationshipCopyWithImpl(this._self, this._then);

  final ChildRelationship _self;
  final $Res Function(ChildRelationship) _then;

  /// Create a copy of ChildRelationship
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? childObjectApiName = freezed,
    Object? fieldName = freezed,
    Object? junctionIdListNames = null,
    Object? junctionReferenceTo = null,
    Object? relationshipName = freezed,
  }) {
    return _then(_self.copyWith(
      childObjectApiName: freezed == childObjectApiName
          ? _self.childObjectApiName
          : childObjectApiName // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldName: freezed == fieldName
          ? _self.fieldName
          : fieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      junctionIdListNames: null == junctionIdListNames
          ? _self.junctionIdListNames
          : junctionIdListNames // ignore: cast_nullable_to_non_nullable
              as List<String>,
      junctionReferenceTo: null == junctionReferenceTo
          ? _self.junctionReferenceTo
          : junctionReferenceTo // ignore: cast_nullable_to_non_nullable
              as List<String>,
      relationshipName: freezed == relationshipName
          ? _self.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ChildRelationship].
extension ChildRelationshipPatterns on ChildRelationship {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ChildRelationship value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChildRelationship() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ChildRelationship value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChildRelationship():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ChildRelationship value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChildRelationship() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? childObjectApiName,
            String? fieldName,
            List<String> junctionIdListNames,
            List<String> junctionReferenceTo,
            String? relationshipName)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChildRelationship() when $default != null:
        return $default(
            _that.childObjectApiName,
            _that.fieldName,
            _that.junctionIdListNames,
            _that.junctionReferenceTo,
            _that.relationshipName);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? childObjectApiName,
            String? fieldName,
            List<String> junctionIdListNames,
            List<String> junctionReferenceTo,
            String? relationshipName)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChildRelationship():
        return $default(
            _that.childObjectApiName,
            _that.fieldName,
            _that.junctionIdListNames,
            _that.junctionReferenceTo,
            _that.relationshipName);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? childObjectApiName,
            String? fieldName,
            List<String> junctionIdListNames,
            List<String> junctionReferenceTo,
            String? relationshipName)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChildRelationship() when $default != null:
        return $default(
            _that.childObjectApiName,
            _that.fieldName,
            _that.junctionIdListNames,
            _that.junctionReferenceTo,
            _that.relationshipName);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ChildRelationship implements ChildRelationship {
  const _ChildRelationship(
      {this.childObjectApiName,
      this.fieldName,
      final List<String> junctionIdListNames = const [],
      final List<String> junctionReferenceTo = const [],
      this.relationshipName})
      : _junctionIdListNames = junctionIdListNames,
        _junctionReferenceTo = junctionReferenceTo;
  factory _ChildRelationship.fromJson(Map<String, dynamic> json) =>
      _$ChildRelationshipFromJson(json);

  @override
  final String? childObjectApiName;
  @override
  final String? fieldName;
  final List<String> _junctionIdListNames;
  @override
  @JsonKey()
  List<String> get junctionIdListNames {
    if (_junctionIdListNames is EqualUnmodifiableListView)
      return _junctionIdListNames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_junctionIdListNames);
  }

  final List<String> _junctionReferenceTo;
  @override
  @JsonKey()
  List<String> get junctionReferenceTo {
    if (_junctionReferenceTo is EqualUnmodifiableListView)
      return _junctionReferenceTo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_junctionReferenceTo);
  }

  @override
  final String? relationshipName;

  /// Create a copy of ChildRelationship
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ChildRelationshipCopyWith<_ChildRelationship> get copyWith =>
      __$ChildRelationshipCopyWithImpl<_ChildRelationship>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ChildRelationshipToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChildRelationship &&
            (identical(other.childObjectApiName, childObjectApiName) ||
                other.childObjectApiName == childObjectApiName) &&
            (identical(other.fieldName, fieldName) ||
                other.fieldName == fieldName) &&
            const DeepCollectionEquality()
                .equals(other._junctionIdListNames, _junctionIdListNames) &&
            const DeepCollectionEquality()
                .equals(other._junctionReferenceTo, _junctionReferenceTo) &&
            (identical(other.relationshipName, relationshipName) ||
                other.relationshipName == relationshipName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      childObjectApiName,
      fieldName,
      const DeepCollectionEquality().hash(_junctionIdListNames),
      const DeepCollectionEquality().hash(_junctionReferenceTo),
      relationshipName);

  @override
  String toString() {
    return 'ChildRelationship(childObjectApiName: $childObjectApiName, fieldName: $fieldName, junctionIdListNames: $junctionIdListNames, junctionReferenceTo: $junctionReferenceTo, relationshipName: $relationshipName)';
  }
}

/// @nodoc
abstract mixin class _$ChildRelationshipCopyWith<$Res>
    implements $ChildRelationshipCopyWith<$Res> {
  factory _$ChildRelationshipCopyWith(
          _ChildRelationship value, $Res Function(_ChildRelationship) _then) =
      __$ChildRelationshipCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? childObjectApiName,
      String? fieldName,
      List<String> junctionIdListNames,
      List<String> junctionReferenceTo,
      String? relationshipName});
}

/// @nodoc
class __$ChildRelationshipCopyWithImpl<$Res>
    implements _$ChildRelationshipCopyWith<$Res> {
  __$ChildRelationshipCopyWithImpl(this._self, this._then);

  final _ChildRelationship _self;
  final $Res Function(_ChildRelationship) _then;

  /// Create a copy of ChildRelationship
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? childObjectApiName = freezed,
    Object? fieldName = freezed,
    Object? junctionIdListNames = null,
    Object? junctionReferenceTo = null,
    Object? relationshipName = freezed,
  }) {
    return _then(_ChildRelationship(
      childObjectApiName: freezed == childObjectApiName
          ? _self.childObjectApiName
          : childObjectApiName // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldName: freezed == fieldName
          ? _self.fieldName
          : fieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      junctionIdListNames: null == junctionIdListNames
          ? _self._junctionIdListNames
          : junctionIdListNames // ignore: cast_nullable_to_non_nullable
              as List<String>,
      junctionReferenceTo: null == junctionReferenceTo
          ? _self._junctionReferenceTo
          : junctionReferenceTo // ignore: cast_nullable_to_non_nullable
              as List<String>,
      relationshipName: freezed == relationshipName
          ? _self.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

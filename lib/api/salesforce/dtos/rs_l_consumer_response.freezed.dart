// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rs_l_consumer_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RSLConsumerResponse {
  String? get id;
  String? get name;
  String? get email;
  String? get phone;
  String? get address;
  String? get city;
  String? get state;
  String? get zip;
  String? get country;
  String? get status;
  String? get createdDate;
  String? get lastModifiedDate;

  /// Create a copy of RSLConsumerResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RSLConsumerResponseCopyWith<RSLConsumerResponse> get copyWith =>
      _$RSLConsumerResponseCopyWithImpl<RSLConsumerResponse>(
          this as RSLConsumerResponse, _$identity);

  /// Serializes this RSLConsumerResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RSLConsumerResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.zip, zip) || other.zip == zip) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, email, phone, address,
      city, state, zip, country, status, createdDate, lastModifiedDate);

  @override
  String toString() {
    return 'RSLConsumerResponse(id: $id, name: $name, email: $email, phone: $phone, address: $address, city: $city, state: $state, zip: $zip, country: $country, status: $status, createdDate: $createdDate, lastModifiedDate: $lastModifiedDate)';
  }
}

/// @nodoc
abstract mixin class $RSLConsumerResponseCopyWith<$Res> {
  factory $RSLConsumerResponseCopyWith(
          RSLConsumerResponse value, $Res Function(RSLConsumerResponse) _then) =
      _$RSLConsumerResponseCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      String? name,
      String? email,
      String? phone,
      String? address,
      String? city,
      String? state,
      String? zip,
      String? country,
      String? status,
      String? createdDate,
      String? lastModifiedDate});
}

/// @nodoc
class _$RSLConsumerResponseCopyWithImpl<$Res>
    implements $RSLConsumerResponseCopyWith<$Res> {
  _$RSLConsumerResponseCopyWithImpl(this._self, this._then);

  final RSLConsumerResponse _self;
  final $Res Function(RSLConsumerResponse) _then;

  /// Create a copy of RSLConsumerResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? zip = freezed,
    Object? country = freezed,
    Object? status = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _self.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      zip: freezed == zip
          ? _self.zip
          : zip // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [RSLConsumerResponse].
extension RSLConsumerResponsePatterns on RSLConsumerResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RSLConsumerResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RSLConsumerResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RSLConsumerResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RSLConsumerResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RSLConsumerResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RSLConsumerResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? id,
            String? name,
            String? email,
            String? phone,
            String? address,
            String? city,
            String? state,
            String? zip,
            String? country,
            String? status,
            String? createdDate,
            String? lastModifiedDate)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RSLConsumerResponse() when $default != null:
        return $default(
            _that.id,
            _that.name,
            _that.email,
            _that.phone,
            _that.address,
            _that.city,
            _that.state,
            _that.zip,
            _that.country,
            _that.status,
            _that.createdDate,
            _that.lastModifiedDate);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? id,
            String? name,
            String? email,
            String? phone,
            String? address,
            String? city,
            String? state,
            String? zip,
            String? country,
            String? status,
            String? createdDate,
            String? lastModifiedDate)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RSLConsumerResponse():
        return $default(
            _that.id,
            _that.name,
            _that.email,
            _that.phone,
            _that.address,
            _that.city,
            _that.state,
            _that.zip,
            _that.country,
            _that.status,
            _that.createdDate,
            _that.lastModifiedDate);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? id,
            String? name,
            String? email,
            String? phone,
            String? address,
            String? city,
            String? state,
            String? zip,
            String? country,
            String? status,
            String? createdDate,
            String? lastModifiedDate)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RSLConsumerResponse() when $default != null:
        return $default(
            _that.id,
            _that.name,
            _that.email,
            _that.phone,
            _that.address,
            _that.city,
            _that.state,
            _that.zip,
            _that.country,
            _that.status,
            _that.createdDate,
            _that.lastModifiedDate);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RSLConsumerResponse implements RSLConsumerResponse {
  const _RSLConsumerResponse(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.address,
      this.city,
      this.state,
      this.zip,
      this.country,
      this.status,
      this.createdDate,
      this.lastModifiedDate});
  factory _RSLConsumerResponse.fromJson(Map<String, dynamic> json) =>
      _$RSLConsumerResponseFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? email;
  @override
  final String? phone;
  @override
  final String? address;
  @override
  final String? city;
  @override
  final String? state;
  @override
  final String? zip;
  @override
  final String? country;
  @override
  final String? status;
  @override
  final String? createdDate;
  @override
  final String? lastModifiedDate;

  /// Create a copy of RSLConsumerResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RSLConsumerResponseCopyWith<_RSLConsumerResponse> get copyWith =>
      __$RSLConsumerResponseCopyWithImpl<_RSLConsumerResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RSLConsumerResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RSLConsumerResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.zip, zip) || other.zip == zip) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, email, phone, address,
      city, state, zip, country, status, createdDate, lastModifiedDate);

  @override
  String toString() {
    return 'RSLConsumerResponse(id: $id, name: $name, email: $email, phone: $phone, address: $address, city: $city, state: $state, zip: $zip, country: $country, status: $status, createdDate: $createdDate, lastModifiedDate: $lastModifiedDate)';
  }
}

/// @nodoc
abstract mixin class _$RSLConsumerResponseCopyWith<$Res>
    implements $RSLConsumerResponseCopyWith<$Res> {
  factory _$RSLConsumerResponseCopyWith(_RSLConsumerResponse value,
          $Res Function(_RSLConsumerResponse) _then) =
      __$RSLConsumerResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      String? email,
      String? phone,
      String? address,
      String? city,
      String? state,
      String? zip,
      String? country,
      String? status,
      String? createdDate,
      String? lastModifiedDate});
}

/// @nodoc
class __$RSLConsumerResponseCopyWithImpl<$Res>
    implements _$RSLConsumerResponseCopyWith<$Res> {
  __$RSLConsumerResponseCopyWithImpl(this._self, this._then);

  final _RSLConsumerResponse _self;
  final $Res Function(_RSLConsumerResponse) _then;

  /// Create a copy of RSLConsumerResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? zip = freezed,
    Object? country = freezed,
    Object? status = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
  }) {
    return _then(_RSLConsumerResponse(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _self.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      zip: freezed == zip
          ? _self.zip
          : zip // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'typing_indicator_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TypingIndicatorState _$TypingIndicatorStateFromJson(Map json) =>
    $checkedCreate(
      '_TypingIndicatorState',
      json,
      ($checkedConvert) {
        final val = _TypingIndicatorState(
          activeEndUserTypingIndicatorsByConversationScrtUuId: $checkedConvert(
              'activeEndUserTypingIndicatorsByConversationScrtUuId',
              (v) =>
                  (v as Map?)?.map(
                    (k, e) => MapEntry(k as String, e as bool),
                  ) ??
                  const {}),
        );
        return val;
      },
    );

Map<String, dynamic> _$TypingIndicatorStateToJson(
        _TypingIndicatorState instance) =>
    <String, dynamic>{
      'activeEndUserTypingIndicatorsByConversationScrtUuId':
          instance.activeEndUserTypingIndicatorsByConversationScrtUuId,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'typing_indicator_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TypingIndicatorState {
  Map<String, bool> get activeEndUserTypingIndicatorsByConversationScrtUuId;

  /// Create a copy of TypingIndicatorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TypingIndicatorStateCopyWith<TypingIndicatorState> get copyWith =>
      _$TypingIndicatorStateCopyWithImpl<TypingIndicatorState>(
          this as TypingIndicatorState, _$identity);

  /// Serializes this TypingIndicatorState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TypingIndicatorState &&
            const DeepCollectionEquality().equals(
                other.activeEndUserTypingIndicatorsByConversationScrtUuId,
                activeEndUserTypingIndicatorsByConversationScrtUuId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality()
          .hash(activeEndUserTypingIndicatorsByConversationScrtUuId));

  @override
  String toString() {
    return 'TypingIndicatorState(activeEndUserTypingIndicatorsByConversationScrtUuId: $activeEndUserTypingIndicatorsByConversationScrtUuId)';
  }
}

/// @nodoc
abstract mixin class $TypingIndicatorStateCopyWith<$Res> {
  factory $TypingIndicatorStateCopyWith(TypingIndicatorState value,
          $Res Function(TypingIndicatorState) _then) =
      _$TypingIndicatorStateCopyWithImpl;
  @useResult
  $Res call(
      {Map<String, bool> activeEndUserTypingIndicatorsByConversationScrtUuId});
}

/// @nodoc
class _$TypingIndicatorStateCopyWithImpl<$Res>
    implements $TypingIndicatorStateCopyWith<$Res> {
  _$TypingIndicatorStateCopyWithImpl(this._self, this._then);

  final TypingIndicatorState _self;
  final $Res Function(TypingIndicatorState) _then;

  /// Create a copy of TypingIndicatorState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeEndUserTypingIndicatorsByConversationScrtUuId = null,
  }) {
    return _then(_self.copyWith(
      activeEndUserTypingIndicatorsByConversationScrtUuId: null ==
              activeEndUserTypingIndicatorsByConversationScrtUuId
          ? _self.activeEndUserTypingIndicatorsByConversationScrtUuId
          : activeEndUserTypingIndicatorsByConversationScrtUuId // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ));
  }
}

/// Adds pattern-matching-related methods to [TypingIndicatorState].
extension TypingIndicatorStatePatterns on TypingIndicatorState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_TypingIndicatorState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TypingIndicatorState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_TypingIndicatorState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TypingIndicatorState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_TypingIndicatorState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TypingIndicatorState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Map<String, bool>
                activeEndUserTypingIndicatorsByConversationScrtUuId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TypingIndicatorState() when $default != null:
        return $default(
            _that.activeEndUserTypingIndicatorsByConversationScrtUuId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Map<String, bool>
                activeEndUserTypingIndicatorsByConversationScrtUuId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TypingIndicatorState():
        return $default(
            _that.activeEndUserTypingIndicatorsByConversationScrtUuId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Map<String, bool>
                activeEndUserTypingIndicatorsByConversationScrtUuId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TypingIndicatorState() when $default != null:
        return $default(
            _that.activeEndUserTypingIndicatorsByConversationScrtUuId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _TypingIndicatorState implements TypingIndicatorState {
  const _TypingIndicatorState(
      {final Map<String, bool>
          activeEndUserTypingIndicatorsByConversationScrtUuId = const {}})
      : _activeEndUserTypingIndicatorsByConversationScrtUuId =
            activeEndUserTypingIndicatorsByConversationScrtUuId;
  factory _TypingIndicatorState.fromJson(Map<String, dynamic> json) =>
      _$TypingIndicatorStateFromJson(json);

  final Map<String, bool> _activeEndUserTypingIndicatorsByConversationScrtUuId;
  @override
  @JsonKey()
  Map<String, bool> get activeEndUserTypingIndicatorsByConversationScrtUuId {
    if (_activeEndUserTypingIndicatorsByConversationScrtUuId
        is EqualUnmodifiableMapView)
      return _activeEndUserTypingIndicatorsByConversationScrtUuId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(
        _activeEndUserTypingIndicatorsByConversationScrtUuId);
  }

  /// Create a copy of TypingIndicatorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TypingIndicatorStateCopyWith<_TypingIndicatorState> get copyWith =>
      __$TypingIndicatorStateCopyWithImpl<_TypingIndicatorState>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TypingIndicatorStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TypingIndicatorState &&
            const DeepCollectionEquality().equals(
                other._activeEndUserTypingIndicatorsByConversationScrtUuId,
                _activeEndUserTypingIndicatorsByConversationScrtUuId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality()
          .hash(_activeEndUserTypingIndicatorsByConversationScrtUuId));

  @override
  String toString() {
    return 'TypingIndicatorState(activeEndUserTypingIndicatorsByConversationScrtUuId: $activeEndUserTypingIndicatorsByConversationScrtUuId)';
  }
}

/// @nodoc
abstract mixin class _$TypingIndicatorStateCopyWith<$Res>
    implements $TypingIndicatorStateCopyWith<$Res> {
  factory _$TypingIndicatorStateCopyWith(_TypingIndicatorState value,
          $Res Function(_TypingIndicatorState) _then) =
      __$TypingIndicatorStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Map<String, bool> activeEndUserTypingIndicatorsByConversationScrtUuId});
}

/// @nodoc
class __$TypingIndicatorStateCopyWithImpl<$Res>
    implements _$TypingIndicatorStateCopyWith<$Res> {
  __$TypingIndicatorStateCopyWithImpl(this._self, this._then);

  final _TypingIndicatorState _self;
  final $Res Function(_TypingIndicatorState) _then;

  /// Create a copy of TypingIndicatorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activeEndUserTypingIndicatorsByConversationScrtUuId = null,
  }) {
    return _then(_TypingIndicatorState(
      activeEndUserTypingIndicatorsByConversationScrtUuId: null ==
              activeEndUserTypingIndicatorsByConversationScrtUuId
          ? _self._activeEndUserTypingIndicatorsByConversationScrtUuId
          : activeEndUserTypingIndicatorsByConversationScrtUuId // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ));
  }
}

// dart format on

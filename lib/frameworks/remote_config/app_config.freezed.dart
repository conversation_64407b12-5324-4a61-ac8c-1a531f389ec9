// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppConfig {
  List<String> get excludedLogs;
  String get shimServiceUrl;
  SalesforceConfig? get productionSalesforceConfig;
  SalesforceConfig? get sandboxSalesforceConfig;
  GlobalConfig? get globalConfig;

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AppConfigCopyWith<AppConfig> get copyWith =>
      _$AppConfigCopyWithImpl<AppConfig>(this as AppConfig, _$identity);

  /// Serializes this AppConfig to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppConfig &&
            const DeepCollectionEquality()
                .equals(other.excludedLogs, excludedLogs) &&
            (identical(other.shimServiceUrl, shimServiceUrl) ||
                other.shimServiceUrl == shimServiceUrl) &&
            (identical(other.productionSalesforceConfig,
                    productionSalesforceConfig) ||
                other.productionSalesforceConfig ==
                    productionSalesforceConfig) &&
            (identical(
                    other.sandboxSalesforceConfig, sandboxSalesforceConfig) ||
                other.sandboxSalesforceConfig == sandboxSalesforceConfig) &&
            (identical(other.globalConfig, globalConfig) ||
                other.globalConfig == globalConfig));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(excludedLogs),
      shimServiceUrl,
      productionSalesforceConfig,
      sandboxSalesforceConfig,
      globalConfig);

  @override
  String toString() {
    return 'AppConfig(excludedLogs: $excludedLogs, shimServiceUrl: $shimServiceUrl, productionSalesforceConfig: $productionSalesforceConfig, sandboxSalesforceConfig: $sandboxSalesforceConfig, globalConfig: $globalConfig)';
  }
}

/// @nodoc
abstract mixin class $AppConfigCopyWith<$Res> {
  factory $AppConfigCopyWith(AppConfig value, $Res Function(AppConfig) _then) =
      _$AppConfigCopyWithImpl;
  @useResult
  $Res call(
      {List<String> excludedLogs,
      String shimServiceUrl,
      SalesforceConfig? productionSalesforceConfig,
      SalesforceConfig? sandboxSalesforceConfig,
      GlobalConfig? globalConfig});

  $SalesforceConfigCopyWith<$Res>? get productionSalesforceConfig;
  $SalesforceConfigCopyWith<$Res>? get sandboxSalesforceConfig;
  $GlobalConfigCopyWith<$Res>? get globalConfig;
}

/// @nodoc
class _$AppConfigCopyWithImpl<$Res> implements $AppConfigCopyWith<$Res> {
  _$AppConfigCopyWithImpl(this._self, this._then);

  final AppConfig _self;
  final $Res Function(AppConfig) _then;

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? excludedLogs = null,
    Object? shimServiceUrl = null,
    Object? productionSalesforceConfig = freezed,
    Object? sandboxSalesforceConfig = freezed,
    Object? globalConfig = freezed,
  }) {
    return _then(_self.copyWith(
      excludedLogs: null == excludedLogs
          ? _self.excludedLogs
          : excludedLogs // ignore: cast_nullable_to_non_nullable
              as List<String>,
      shimServiceUrl: null == shimServiceUrl
          ? _self.shimServiceUrl
          : shimServiceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productionSalesforceConfig: freezed == productionSalesforceConfig
          ? _self.productionSalesforceConfig
          : productionSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      sandboxSalesforceConfig: freezed == sandboxSalesforceConfig
          ? _self.sandboxSalesforceConfig
          : sandboxSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      globalConfig: freezed == globalConfig
          ? _self.globalConfig
          : globalConfig // ignore: cast_nullable_to_non_nullable
              as GlobalConfig?,
    ));
  }

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SalesforceConfigCopyWith<$Res>? get productionSalesforceConfig {
    if (_self.productionSalesforceConfig == null) {
      return null;
    }

    return $SalesforceConfigCopyWith<$Res>(_self.productionSalesforceConfig!,
        (value) {
      return _then(_self.copyWith(productionSalesforceConfig: value));
    });
  }

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SalesforceConfigCopyWith<$Res>? get sandboxSalesforceConfig {
    if (_self.sandboxSalesforceConfig == null) {
      return null;
    }

    return $SalesforceConfigCopyWith<$Res>(_self.sandboxSalesforceConfig!,
        (value) {
      return _then(_self.copyWith(sandboxSalesforceConfig: value));
    });
  }

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GlobalConfigCopyWith<$Res>? get globalConfig {
    if (_self.globalConfig == null) {
      return null;
    }

    return $GlobalConfigCopyWith<$Res>(_self.globalConfig!, (value) {
      return _then(_self.copyWith(globalConfig: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AppConfig].
extension AppConfigPatterns on AppConfig {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AppConfig value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppConfig() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AppConfig value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppConfig():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AppConfig value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppConfig() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            List<String> excludedLogs,
            String shimServiceUrl,
            SalesforceConfig? productionSalesforceConfig,
            SalesforceConfig? sandboxSalesforceConfig,
            GlobalConfig? globalConfig)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppConfig() when $default != null:
        return $default(
            _that.excludedLogs,
            _that.shimServiceUrl,
            _that.productionSalesforceConfig,
            _that.sandboxSalesforceConfig,
            _that.globalConfig);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            List<String> excludedLogs,
            String shimServiceUrl,
            SalesforceConfig? productionSalesforceConfig,
            SalesforceConfig? sandboxSalesforceConfig,
            GlobalConfig? globalConfig)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppConfig():
        return $default(
            _that.excludedLogs,
            _that.shimServiceUrl,
            _that.productionSalesforceConfig,
            _that.sandboxSalesforceConfig,
            _that.globalConfig);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            List<String> excludedLogs,
            String shimServiceUrl,
            SalesforceConfig? productionSalesforceConfig,
            SalesforceConfig? sandboxSalesforceConfig,
            GlobalConfig? globalConfig)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppConfig() when $default != null:
        return $default(
            _that.excludedLogs,
            _that.shimServiceUrl,
            _that.productionSalesforceConfig,
            _that.sandboxSalesforceConfig,
            _that.globalConfig);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AppConfig implements AppConfig {
  _AppConfig(
      {final List<String> excludedLogs = const [],
      required this.shimServiceUrl,
      required this.productionSalesforceConfig,
      required this.sandboxSalesforceConfig,
      required this.globalConfig})
      : _excludedLogs = excludedLogs;
  factory _AppConfig.fromJson(Map<String, dynamic> json) =>
      _$AppConfigFromJson(json);

  final List<String> _excludedLogs;
  @override
  @JsonKey()
  List<String> get excludedLogs {
    if (_excludedLogs is EqualUnmodifiableListView) return _excludedLogs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_excludedLogs);
  }

  @override
  final String shimServiceUrl;
  @override
  final SalesforceConfig? productionSalesforceConfig;
  @override
  final SalesforceConfig? sandboxSalesforceConfig;
  @override
  final GlobalConfig? globalConfig;

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AppConfigCopyWith<_AppConfig> get copyWith =>
      __$AppConfigCopyWithImpl<_AppConfig>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AppConfigToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AppConfig &&
            const DeepCollectionEquality()
                .equals(other._excludedLogs, _excludedLogs) &&
            (identical(other.shimServiceUrl, shimServiceUrl) ||
                other.shimServiceUrl == shimServiceUrl) &&
            (identical(other.productionSalesforceConfig,
                    productionSalesforceConfig) ||
                other.productionSalesforceConfig ==
                    productionSalesforceConfig) &&
            (identical(
                    other.sandboxSalesforceConfig, sandboxSalesforceConfig) ||
                other.sandboxSalesforceConfig == sandboxSalesforceConfig) &&
            (identical(other.globalConfig, globalConfig) ||
                other.globalConfig == globalConfig));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_excludedLogs),
      shimServiceUrl,
      productionSalesforceConfig,
      sandboxSalesforceConfig,
      globalConfig);

  @override
  String toString() {
    return 'AppConfig(excludedLogs: $excludedLogs, shimServiceUrl: $shimServiceUrl, productionSalesforceConfig: $productionSalesforceConfig, sandboxSalesforceConfig: $sandboxSalesforceConfig, globalConfig: $globalConfig)';
  }
}

/// @nodoc
abstract mixin class _$AppConfigCopyWith<$Res>
    implements $AppConfigCopyWith<$Res> {
  factory _$AppConfigCopyWith(
          _AppConfig value, $Res Function(_AppConfig) _then) =
      __$AppConfigCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<String> excludedLogs,
      String shimServiceUrl,
      SalesforceConfig? productionSalesforceConfig,
      SalesforceConfig? sandboxSalesforceConfig,
      GlobalConfig? globalConfig});

  @override
  $SalesforceConfigCopyWith<$Res>? get productionSalesforceConfig;
  @override
  $SalesforceConfigCopyWith<$Res>? get sandboxSalesforceConfig;
  @override
  $GlobalConfigCopyWith<$Res>? get globalConfig;
}

/// @nodoc
class __$AppConfigCopyWithImpl<$Res> implements _$AppConfigCopyWith<$Res> {
  __$AppConfigCopyWithImpl(this._self, this._then);

  final _AppConfig _self;
  final $Res Function(_AppConfig) _then;

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? excludedLogs = null,
    Object? shimServiceUrl = null,
    Object? productionSalesforceConfig = freezed,
    Object? sandboxSalesforceConfig = freezed,
    Object? globalConfig = freezed,
  }) {
    return _then(_AppConfig(
      excludedLogs: null == excludedLogs
          ? _self._excludedLogs
          : excludedLogs // ignore: cast_nullable_to_non_nullable
              as List<String>,
      shimServiceUrl: null == shimServiceUrl
          ? _self.shimServiceUrl
          : shimServiceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productionSalesforceConfig: freezed == productionSalesforceConfig
          ? _self.productionSalesforceConfig
          : productionSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      sandboxSalesforceConfig: freezed == sandboxSalesforceConfig
          ? _self.sandboxSalesforceConfig
          : sandboxSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      globalConfig: freezed == globalConfig
          ? _self.globalConfig
          : globalConfig // ignore: cast_nullable_to_non_nullable
              as GlobalConfig?,
    ));
  }

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SalesforceConfigCopyWith<$Res>? get productionSalesforceConfig {
    if (_self.productionSalesforceConfig == null) {
      return null;
    }

    return $SalesforceConfigCopyWith<$Res>(_self.productionSalesforceConfig!,
        (value) {
      return _then(_self.copyWith(productionSalesforceConfig: value));
    });
  }

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SalesforceConfigCopyWith<$Res>? get sandboxSalesforceConfig {
    if (_self.sandboxSalesforceConfig == null) {
      return null;
    }

    return $SalesforceConfigCopyWith<$Res>(_self.sandboxSalesforceConfig!,
        (value) {
      return _then(_self.copyWith(sandboxSalesforceConfig: value));
    });
  }

  /// Create a copy of AppConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GlobalConfigCopyWith<$Res>? get globalConfig {
    if (_self.globalConfig == null) {
      return null;
    }

    return $GlobalConfigCopyWith<$Res>(_self.globalConfig!, (value) {
      return _then(_self.copyWith(globalConfig: value));
    });
  }
}

/// @nodoc
mixin _$SalesforceConfig {
  String get name;
  String get endPointBase;
  String get endPointPath;
  String get responseType;
  String get consumerKey;
  String get authRedirectUri;
  String get authCallbackUrlScheme;

  /// Create a copy of SalesforceConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SalesforceConfigCopyWith<SalesforceConfig> get copyWith =>
      _$SalesforceConfigCopyWithImpl<SalesforceConfig>(
          this as SalesforceConfig, _$identity);

  /// Serializes this SalesforceConfig to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SalesforceConfig &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.endPointBase, endPointBase) ||
                other.endPointBase == endPointBase) &&
            (identical(other.endPointPath, endPointPath) ||
                other.endPointPath == endPointPath) &&
            (identical(other.responseType, responseType) ||
                other.responseType == responseType) &&
            (identical(other.consumerKey, consumerKey) ||
                other.consumerKey == consumerKey) &&
            (identical(other.authRedirectUri, authRedirectUri) ||
                other.authRedirectUri == authRedirectUri) &&
            (identical(other.authCallbackUrlScheme, authCallbackUrlScheme) ||
                other.authCallbackUrlScheme == authCallbackUrlScheme));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, endPointBase, endPointPath,
      responseType, consumerKey, authRedirectUri, authCallbackUrlScheme);

  @override
  String toString() {
    return 'SalesforceConfig(name: $name, endPointBase: $endPointBase, endPointPath: $endPointPath, responseType: $responseType, consumerKey: $consumerKey, authRedirectUri: $authRedirectUri, authCallbackUrlScheme: $authCallbackUrlScheme)';
  }
}

/// @nodoc
abstract mixin class $SalesforceConfigCopyWith<$Res> {
  factory $SalesforceConfigCopyWith(
          SalesforceConfig value, $Res Function(SalesforceConfig) _then) =
      _$SalesforceConfigCopyWithImpl;
  @useResult
  $Res call(
      {String name,
      String endPointBase,
      String endPointPath,
      String responseType,
      String consumerKey,
      String authRedirectUri,
      String authCallbackUrlScheme});
}

/// @nodoc
class _$SalesforceConfigCopyWithImpl<$Res>
    implements $SalesforceConfigCopyWith<$Res> {
  _$SalesforceConfigCopyWithImpl(this._self, this._then);

  final SalesforceConfig _self;
  final $Res Function(SalesforceConfig) _then;

  /// Create a copy of SalesforceConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? endPointBase = null,
    Object? endPointPath = null,
    Object? responseType = null,
    Object? consumerKey = null,
    Object? authRedirectUri = null,
    Object? authCallbackUrlScheme = null,
  }) {
    return _then(_self.copyWith(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      endPointBase: null == endPointBase
          ? _self.endPointBase
          : endPointBase // ignore: cast_nullable_to_non_nullable
              as String,
      endPointPath: null == endPointPath
          ? _self.endPointPath
          : endPointPath // ignore: cast_nullable_to_non_nullable
              as String,
      responseType: null == responseType
          ? _self.responseType
          : responseType // ignore: cast_nullable_to_non_nullable
              as String,
      consumerKey: null == consumerKey
          ? _self.consumerKey
          : consumerKey // ignore: cast_nullable_to_non_nullable
              as String,
      authRedirectUri: null == authRedirectUri
          ? _self.authRedirectUri
          : authRedirectUri // ignore: cast_nullable_to_non_nullable
              as String,
      authCallbackUrlScheme: null == authCallbackUrlScheme
          ? _self.authCallbackUrlScheme
          : authCallbackUrlScheme // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [SalesforceConfig].
extension SalesforceConfigPatterns on SalesforceConfig {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SalesforceConfig value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceConfig() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SalesforceConfig value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceConfig():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SalesforceConfig value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceConfig() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String name,
            String endPointBase,
            String endPointPath,
            String responseType,
            String consumerKey,
            String authRedirectUri,
            String authCallbackUrlScheme)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SalesforceConfig() when $default != null:
        return $default(
            _that.name,
            _that.endPointBase,
            _that.endPointPath,
            _that.responseType,
            _that.consumerKey,
            _that.authRedirectUri,
            _that.authCallbackUrlScheme);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String name,
            String endPointBase,
            String endPointPath,
            String responseType,
            String consumerKey,
            String authRedirectUri,
            String authCallbackUrlScheme)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceConfig():
        return $default(
            _that.name,
            _that.endPointBase,
            _that.endPointPath,
            _that.responseType,
            _that.consumerKey,
            _that.authRedirectUri,
            _that.authCallbackUrlScheme);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String name,
            String endPointBase,
            String endPointPath,
            String responseType,
            String consumerKey,
            String authRedirectUri,
            String authCallbackUrlScheme)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SalesforceConfig() when $default != null:
        return $default(
            _that.name,
            _that.endPointBase,
            _that.endPointPath,
            _that.responseType,
            _that.consumerKey,
            _that.authRedirectUri,
            _that.authCallbackUrlScheme);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SalesforceConfig extends SalesforceConfig {
  _SalesforceConfig(
      {required this.name,
      required this.endPointBase,
      required this.endPointPath,
      required this.responseType,
      required this.consumerKey,
      required this.authRedirectUri,
      required this.authCallbackUrlScheme})
      : super._();
  factory _SalesforceConfig.fromJson(Map<String, dynamic> json) =>
      _$SalesforceConfigFromJson(json);

  @override
  final String name;
  @override
  final String endPointBase;
  @override
  final String endPointPath;
  @override
  final String responseType;
  @override
  final String consumerKey;
  @override
  final String authRedirectUri;
  @override
  final String authCallbackUrlScheme;

  /// Create a copy of SalesforceConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SalesforceConfigCopyWith<_SalesforceConfig> get copyWith =>
      __$SalesforceConfigCopyWithImpl<_SalesforceConfig>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SalesforceConfigToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SalesforceConfig &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.endPointBase, endPointBase) ||
                other.endPointBase == endPointBase) &&
            (identical(other.endPointPath, endPointPath) ||
                other.endPointPath == endPointPath) &&
            (identical(other.responseType, responseType) ||
                other.responseType == responseType) &&
            (identical(other.consumerKey, consumerKey) ||
                other.consumerKey == consumerKey) &&
            (identical(other.authRedirectUri, authRedirectUri) ||
                other.authRedirectUri == authRedirectUri) &&
            (identical(other.authCallbackUrlScheme, authCallbackUrlScheme) ||
                other.authCallbackUrlScheme == authCallbackUrlScheme));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, endPointBase, endPointPath,
      responseType, consumerKey, authRedirectUri, authCallbackUrlScheme);

  @override
  String toString() {
    return 'SalesforceConfig(name: $name, endPointBase: $endPointBase, endPointPath: $endPointPath, responseType: $responseType, consumerKey: $consumerKey, authRedirectUri: $authRedirectUri, authCallbackUrlScheme: $authCallbackUrlScheme)';
  }
}

/// @nodoc
abstract mixin class _$SalesforceConfigCopyWith<$Res>
    implements $SalesforceConfigCopyWith<$Res> {
  factory _$SalesforceConfigCopyWith(
          _SalesforceConfig value, $Res Function(_SalesforceConfig) _then) =
      __$SalesforceConfigCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String name,
      String endPointBase,
      String endPointPath,
      String responseType,
      String consumerKey,
      String authRedirectUri,
      String authCallbackUrlScheme});
}

/// @nodoc
class __$SalesforceConfigCopyWithImpl<$Res>
    implements _$SalesforceConfigCopyWith<$Res> {
  __$SalesforceConfigCopyWithImpl(this._self, this._then);

  final _SalesforceConfig _self;
  final $Res Function(_SalesforceConfig) _then;

  /// Create a copy of SalesforceConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = null,
    Object? endPointBase = null,
    Object? endPointPath = null,
    Object? responseType = null,
    Object? consumerKey = null,
    Object? authRedirectUri = null,
    Object? authCallbackUrlScheme = null,
  }) {
    return _then(_SalesforceConfig(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      endPointBase: null == endPointBase
          ? _self.endPointBase
          : endPointBase // ignore: cast_nullable_to_non_nullable
              as String,
      endPointPath: null == endPointPath
          ? _self.endPointPath
          : endPointPath // ignore: cast_nullable_to_non_nullable
              as String,
      responseType: null == responseType
          ? _self.responseType
          : responseType // ignore: cast_nullable_to_non_nullable
              as String,
      consumerKey: null == consumerKey
          ? _self.consumerKey
          : consumerKey // ignore: cast_nullable_to_non_nullable
              as String,
      authRedirectUri: null == authRedirectUri
          ? _self.authRedirectUri
          : authRedirectUri // ignore: cast_nullable_to_non_nullable
              as String,
      authCallbackUrlScheme: null == authCallbackUrlScheme
          ? _self.authCallbackUrlScheme
          : authCallbackUrlScheme // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$GlobalConfig {
  int? get shimServiceExpirationInSeconds;
  int? get sessionExpirationInSeconds;
  int get salesforceSearchResultLimit;
  List<String> get contactFields;

  /// Create a copy of GlobalConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GlobalConfigCopyWith<GlobalConfig> get copyWith =>
      _$GlobalConfigCopyWithImpl<GlobalConfig>(
          this as GlobalConfig, _$identity);

  /// Serializes this GlobalConfig to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GlobalConfig &&
            (identical(other.shimServiceExpirationInSeconds,
                    shimServiceExpirationInSeconds) ||
                other.shimServiceExpirationInSeconds ==
                    shimServiceExpirationInSeconds) &&
            (identical(other.sessionExpirationInSeconds,
                    sessionExpirationInSeconds) ||
                other.sessionExpirationInSeconds ==
                    sessionExpirationInSeconds) &&
            (identical(other.salesforceSearchResultLimit,
                    salesforceSearchResultLimit) ||
                other.salesforceSearchResultLimit ==
                    salesforceSearchResultLimit) &&
            const DeepCollectionEquality()
                .equals(other.contactFields, contactFields));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      shimServiceExpirationInSeconds,
      sessionExpirationInSeconds,
      salesforceSearchResultLimit,
      const DeepCollectionEquality().hash(contactFields));

  @override
  String toString() {
    return 'GlobalConfig(shimServiceExpirationInSeconds: $shimServiceExpirationInSeconds, sessionExpirationInSeconds: $sessionExpirationInSeconds, salesforceSearchResultLimit: $salesforceSearchResultLimit, contactFields: $contactFields)';
  }
}

/// @nodoc
abstract mixin class $GlobalConfigCopyWith<$Res> {
  factory $GlobalConfigCopyWith(
          GlobalConfig value, $Res Function(GlobalConfig) _then) =
      _$GlobalConfigCopyWithImpl;
  @useResult
  $Res call(
      {int? shimServiceExpirationInSeconds,
      int? sessionExpirationInSeconds,
      int salesforceSearchResultLimit,
      List<String> contactFields});
}

/// @nodoc
class _$GlobalConfigCopyWithImpl<$Res> implements $GlobalConfigCopyWith<$Res> {
  _$GlobalConfigCopyWithImpl(this._self, this._then);

  final GlobalConfig _self;
  final $Res Function(GlobalConfig) _then;

  /// Create a copy of GlobalConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shimServiceExpirationInSeconds = freezed,
    Object? sessionExpirationInSeconds = freezed,
    Object? salesforceSearchResultLimit = null,
    Object? contactFields = null,
  }) {
    return _then(_self.copyWith(
      shimServiceExpirationInSeconds: freezed == shimServiceExpirationInSeconds
          ? _self.shimServiceExpirationInSeconds
          : shimServiceExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationInSeconds: freezed == sessionExpirationInSeconds
          ? _self.sessionExpirationInSeconds
          : sessionExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      salesforceSearchResultLimit: null == salesforceSearchResultLimit
          ? _self.salesforceSearchResultLimit
          : salesforceSearchResultLimit // ignore: cast_nullable_to_non_nullable
              as int,
      contactFields: null == contactFields
          ? _self.contactFields
          : contactFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// Adds pattern-matching-related methods to [GlobalConfig].
extension GlobalConfigPatterns on GlobalConfig {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_GlobalConfig value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GlobalConfig() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_GlobalConfig value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GlobalConfig():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_GlobalConfig value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GlobalConfig() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int? shimServiceExpirationInSeconds,
            int? sessionExpirationInSeconds,
            int salesforceSearchResultLimit,
            List<String> contactFields)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GlobalConfig() when $default != null:
        return $default(
            _that.shimServiceExpirationInSeconds,
            _that.sessionExpirationInSeconds,
            _that.salesforceSearchResultLimit,
            _that.contactFields);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int? shimServiceExpirationInSeconds,
            int? sessionExpirationInSeconds,
            int salesforceSearchResultLimit,
            List<String> contactFields)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GlobalConfig():
        return $default(
            _that.shimServiceExpirationInSeconds,
            _that.sessionExpirationInSeconds,
            _that.salesforceSearchResultLimit,
            _that.contactFields);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int? shimServiceExpirationInSeconds,
            int? sessionExpirationInSeconds,
            int salesforceSearchResultLimit,
            List<String> contactFields)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GlobalConfig() when $default != null:
        return $default(
            _that.shimServiceExpirationInSeconds,
            _that.sessionExpirationInSeconds,
            _that.salesforceSearchResultLimit,
            _that.contactFields);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _GlobalConfig implements GlobalConfig {
  _GlobalConfig(
      {required this.shimServiceExpirationInSeconds,
      required this.sessionExpirationInSeconds,
      required this.salesforceSearchResultLimit,
      required final List<String> contactFields})
      : _contactFields = contactFields;
  factory _GlobalConfig.fromJson(Map<String, dynamic> json) =>
      _$GlobalConfigFromJson(json);

  @override
  final int? shimServiceExpirationInSeconds;
  @override
  final int? sessionExpirationInSeconds;
  @override
  final int salesforceSearchResultLimit;
  final List<String> _contactFields;
  @override
  List<String> get contactFields {
    if (_contactFields is EqualUnmodifiableListView) return _contactFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contactFields);
  }

  /// Create a copy of GlobalConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GlobalConfigCopyWith<_GlobalConfig> get copyWith =>
      __$GlobalConfigCopyWithImpl<_GlobalConfig>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GlobalConfigToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GlobalConfig &&
            (identical(other.shimServiceExpirationInSeconds,
                    shimServiceExpirationInSeconds) ||
                other.shimServiceExpirationInSeconds ==
                    shimServiceExpirationInSeconds) &&
            (identical(other.sessionExpirationInSeconds,
                    sessionExpirationInSeconds) ||
                other.sessionExpirationInSeconds ==
                    sessionExpirationInSeconds) &&
            (identical(other.salesforceSearchResultLimit,
                    salesforceSearchResultLimit) ||
                other.salesforceSearchResultLimit ==
                    salesforceSearchResultLimit) &&
            const DeepCollectionEquality()
                .equals(other._contactFields, _contactFields));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      shimServiceExpirationInSeconds,
      sessionExpirationInSeconds,
      salesforceSearchResultLimit,
      const DeepCollectionEquality().hash(_contactFields));

  @override
  String toString() {
    return 'GlobalConfig(shimServiceExpirationInSeconds: $shimServiceExpirationInSeconds, sessionExpirationInSeconds: $sessionExpirationInSeconds, salesforceSearchResultLimit: $salesforceSearchResultLimit, contactFields: $contactFields)';
  }
}

/// @nodoc
abstract mixin class _$GlobalConfigCopyWith<$Res>
    implements $GlobalConfigCopyWith<$Res> {
  factory _$GlobalConfigCopyWith(
          _GlobalConfig value, $Res Function(_GlobalConfig) _then) =
      __$GlobalConfigCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int? shimServiceExpirationInSeconds,
      int? sessionExpirationInSeconds,
      int salesforceSearchResultLimit,
      List<String> contactFields});
}

/// @nodoc
class __$GlobalConfigCopyWithImpl<$Res>
    implements _$GlobalConfigCopyWith<$Res> {
  __$GlobalConfigCopyWithImpl(this._self, this._then);

  final _GlobalConfig _self;
  final $Res Function(_GlobalConfig) _then;

  /// Create a copy of GlobalConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? shimServiceExpirationInSeconds = freezed,
    Object? sessionExpirationInSeconds = freezed,
    Object? salesforceSearchResultLimit = null,
    Object? contactFields = null,
  }) {
    return _then(_GlobalConfig(
      shimServiceExpirationInSeconds: freezed == shimServiceExpirationInSeconds
          ? _self.shimServiceExpirationInSeconds
          : shimServiceExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationInSeconds: freezed == sessionExpirationInSeconds
          ? _self.sessionExpirationInSeconds
          : sessionExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      salesforceSearchResultLimit: null == salesforceSearchResultLimit
          ? _self.salesforceSearchResultLimit
          : salesforceSearchResultLimit // ignore: cast_nullable_to_non_nullable
              as int,
      contactFields: null == contactFields
          ? _self._contactFields
          : contactFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on

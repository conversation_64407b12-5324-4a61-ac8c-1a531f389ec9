// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PresenceState {
  bool get statusIsUpdating;
  PresenceStatus get currentStatus;
  List<PresenceStatus> get availableStatuses;
  @JsonKey(ignore: true)
  UiEvent<Nothing>? get showPresenceModal;

  /// Create a copy of PresenceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PresenceStateCopyWith<PresenceState> get copyWith =>
      _$PresenceStateCopyWithImpl<PresenceState>(
          this as PresenceState, _$identity);

  /// Serializes this PresenceState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PresenceState &&
            (identical(other.statusIsUpdating, statusIsUpdating) ||
                other.statusIsUpdating == statusIsUpdating) &&
            (identical(other.currentStatus, currentStatus) ||
                other.currentStatus == currentStatus) &&
            const DeepCollectionEquality()
                .equals(other.availableStatuses, availableStatuses) &&
            (identical(other.showPresenceModal, showPresenceModal) ||
                other.showPresenceModal == showPresenceModal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      statusIsUpdating,
      currentStatus,
      const DeepCollectionEquality().hash(availableStatuses),
      showPresenceModal);

  @override
  String toString() {
    return 'PresenceState(statusIsUpdating: $statusIsUpdating, currentStatus: $currentStatus, availableStatuses: $availableStatuses, showPresenceModal: $showPresenceModal)';
  }
}

/// @nodoc
abstract mixin class $PresenceStateCopyWith<$Res> {
  factory $PresenceStateCopyWith(
          PresenceState value, $Res Function(PresenceState) _then) =
      _$PresenceStateCopyWithImpl;
  @useResult
  $Res call(
      {bool statusIsUpdating,
      PresenceStatus currentStatus,
      List<PresenceStatus> availableStatuses,
      @JsonKey(ignore: true) UiEvent<Nothing>? showPresenceModal});

  $PresenceStatusCopyWith<$Res> get currentStatus;
}

/// @nodoc
class _$PresenceStateCopyWithImpl<$Res>
    implements $PresenceStateCopyWith<$Res> {
  _$PresenceStateCopyWithImpl(this._self, this._then);

  final PresenceState _self;
  final $Res Function(PresenceState) _then;

  /// Create a copy of PresenceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusIsUpdating = null,
    Object? currentStatus = null,
    Object? availableStatuses = null,
    Object? showPresenceModal = freezed,
  }) {
    return _then(_self.copyWith(
      statusIsUpdating: null == statusIsUpdating
          ? _self.statusIsUpdating
          : statusIsUpdating // ignore: cast_nullable_to_non_nullable
              as bool,
      currentStatus: null == currentStatus
          ? _self.currentStatus
          : currentStatus // ignore: cast_nullable_to_non_nullable
              as PresenceStatus,
      availableStatuses: null == availableStatuses
          ? _self.availableStatuses
          : availableStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>,
      showPresenceModal: freezed == showPresenceModal
          ? _self.showPresenceModal
          : showPresenceModal // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }

  /// Create a copy of PresenceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PresenceStatusCopyWith<$Res> get currentStatus {
    return $PresenceStatusCopyWith<$Res>(_self.currentStatus, (value) {
      return _then(_self.copyWith(currentStatus: value));
    });
  }
}

/// Adds pattern-matching-related methods to [PresenceState].
extension PresenceStatePatterns on PresenceState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PresenceState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PresenceState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PresenceState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool statusIsUpdating,
            PresenceStatus currentStatus,
            List<PresenceStatus> availableStatuses,
            @JsonKey(ignore: true) UiEvent<Nothing>? showPresenceModal)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PresenceState() when $default != null:
        return $default(_that.statusIsUpdating, _that.currentStatus,
            _that.availableStatuses, _that.showPresenceModal);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool statusIsUpdating,
            PresenceStatus currentStatus,
            List<PresenceStatus> availableStatuses,
            @JsonKey(ignore: true) UiEvent<Nothing>? showPresenceModal)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceState():
        return $default(_that.statusIsUpdating, _that.currentStatus,
            _that.availableStatuses, _that.showPresenceModal);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool statusIsUpdating,
            PresenceStatus currentStatus,
            List<PresenceStatus> availableStatuses,
            @JsonKey(ignore: true) UiEvent<Nothing>? showPresenceModal)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _PresenceState() when $default != null:
        return $default(_that.statusIsUpdating, _that.currentStatus,
            _that.availableStatuses, _that.showPresenceModal);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _PresenceState extends PresenceState {
  const _PresenceState(
      {this.statusIsUpdating = false,
      this.currentStatus = const PresenceStatus(
          id: '', label: 'Offline', statusOption: PresenceStatusOption.offline),
      final List<PresenceStatus> availableStatuses = const [],
      @JsonKey(ignore: true) this.showPresenceModal})
      : _availableStatuses = availableStatuses,
        super._();
  factory _PresenceState.fromJson(Map<String, dynamic> json) =>
      _$PresenceStateFromJson(json);

  @override
  @JsonKey()
  final bool statusIsUpdating;
  @override
  @JsonKey()
  final PresenceStatus currentStatus;
  final List<PresenceStatus> _availableStatuses;
  @override
  @JsonKey()
  List<PresenceStatus> get availableStatuses {
    if (_availableStatuses is EqualUnmodifiableListView)
      return _availableStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableStatuses);
  }

  @override
  @JsonKey(ignore: true)
  final UiEvent<Nothing>? showPresenceModal;

  /// Create a copy of PresenceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PresenceStateCopyWith<_PresenceState> get copyWith =>
      __$PresenceStateCopyWithImpl<_PresenceState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PresenceStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PresenceState &&
            (identical(other.statusIsUpdating, statusIsUpdating) ||
                other.statusIsUpdating == statusIsUpdating) &&
            (identical(other.currentStatus, currentStatus) ||
                other.currentStatus == currentStatus) &&
            const DeepCollectionEquality()
                .equals(other._availableStatuses, _availableStatuses) &&
            (identical(other.showPresenceModal, showPresenceModal) ||
                other.showPresenceModal == showPresenceModal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      statusIsUpdating,
      currentStatus,
      const DeepCollectionEquality().hash(_availableStatuses),
      showPresenceModal);

  @override
  String toString() {
    return 'PresenceState(statusIsUpdating: $statusIsUpdating, currentStatus: $currentStatus, availableStatuses: $availableStatuses, showPresenceModal: $showPresenceModal)';
  }
}

/// @nodoc
abstract mixin class _$PresenceStateCopyWith<$Res>
    implements $PresenceStateCopyWith<$Res> {
  factory _$PresenceStateCopyWith(
          _PresenceState value, $Res Function(_PresenceState) _then) =
      __$PresenceStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool statusIsUpdating,
      PresenceStatus currentStatus,
      List<PresenceStatus> availableStatuses,
      @JsonKey(ignore: true) UiEvent<Nothing>? showPresenceModal});

  @override
  $PresenceStatusCopyWith<$Res> get currentStatus;
}

/// @nodoc
class __$PresenceStateCopyWithImpl<$Res>
    implements _$PresenceStateCopyWith<$Res> {
  __$PresenceStateCopyWithImpl(this._self, this._then);

  final _PresenceState _self;
  final $Res Function(_PresenceState) _then;

  /// Create a copy of PresenceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? statusIsUpdating = null,
    Object? currentStatus = null,
    Object? availableStatuses = null,
    Object? showPresenceModal = freezed,
  }) {
    return _then(_PresenceState(
      statusIsUpdating: null == statusIsUpdating
          ? _self.statusIsUpdating
          : statusIsUpdating // ignore: cast_nullable_to_non_nullable
              as bool,
      currentStatus: null == currentStatus
          ? _self.currentStatus
          : currentStatus // ignore: cast_nullable_to_non_nullable
              as PresenceStatus,
      availableStatuses: null == availableStatuses
          ? _self._availableStatuses
          : availableStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>,
      showPresenceModal: freezed == showPresenceModal
          ? _self.showPresenceModal
          : showPresenceModal // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }

  /// Create a copy of PresenceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PresenceStatusCopyWith<$Res> get currentStatus {
    return $PresenceStatusCopyWith<$Res>(_self.currentStatus, (value) {
      return _then(_self.copyWith(currentStatus: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presence_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PresenceState _$PresenceStateFromJson(Map json) => $checkedCreate(
      '_PresenceState',
      json,
      ($checkedConvert) {
        final val = _PresenceState(
          statusIsUpdating:
              $checkedConvert('statusIsUpdating', (v) => v as bool? ?? false),
          currentStatus: $checkedConvert(
              'currentStatus',
              (v) => v == null
                  ? const PresenceStatus(
                      id: '',
                      label: 'Offline',
                      statusOption: PresenceStatusOption.offline)
                  : PresenceStatus.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          availableStatuses: $checkedConvert(
              'availableStatuses',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => PresenceStatus.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const []),
        );
        return val;
      },
    );

Map<String, dynamic> _$PresenceStateToJson(_PresenceState instance) =>
    <String, dynamic>{
      'statusIsUpdating': instance.statusIsUpdating,
      'currentStatus': instance.currentStatus.toJson(),
      'availableStatuses':
          instance.availableStatuses.map((e) => e.toJson()).toList(),
    };

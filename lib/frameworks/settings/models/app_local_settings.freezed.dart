// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_local_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppLocalSettings {
  bool get showDevOptions;
  bool get hasRequestedPushPermission;
  bool get userRequestsAiSuggestions;
  bool get aiSuggestionsEnabled;
  SalesforceEnvironment get selectedEnvironment;

  /// Create a copy of AppLocalSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AppLocalSettingsCopyWith<AppLocalSettings> get copyWith =>
      _$AppLocalSettingsCopyWithImpl<AppLocalSettings>(
          this as AppLocalSettings, _$identity);

  /// Serializes this AppLocalSettings to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppLocalSettings &&
            (identical(other.showDevOptions, showDevOptions) ||
                other.showDevOptions == showDevOptions) &&
            (identical(other.hasRequestedPushPermission,
                    hasRequestedPushPermission) ||
                other.hasRequestedPushPermission ==
                    hasRequestedPushPermission) &&
            (identical(other.userRequestsAiSuggestions,
                    userRequestsAiSuggestions) ||
                other.userRequestsAiSuggestions == userRequestsAiSuggestions) &&
            (identical(other.aiSuggestionsEnabled, aiSuggestionsEnabled) ||
                other.aiSuggestionsEnabled == aiSuggestionsEnabled) &&
            (identical(other.selectedEnvironment, selectedEnvironment) ||
                other.selectedEnvironment == selectedEnvironment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showDevOptions,
      hasRequestedPushPermission,
      userRequestsAiSuggestions,
      aiSuggestionsEnabled,
      selectedEnvironment);

  @override
  String toString() {
    return 'AppLocalSettings(showDevOptions: $showDevOptions, hasRequestedPushPermission: $hasRequestedPushPermission, userRequestsAiSuggestions: $userRequestsAiSuggestions, aiSuggestionsEnabled: $aiSuggestionsEnabled, selectedEnvironment: $selectedEnvironment)';
  }
}

/// @nodoc
abstract mixin class $AppLocalSettingsCopyWith<$Res> {
  factory $AppLocalSettingsCopyWith(
          AppLocalSettings value, $Res Function(AppLocalSettings) _then) =
      _$AppLocalSettingsCopyWithImpl;
  @useResult
  $Res call(
      {bool showDevOptions,
      bool hasRequestedPushPermission,
      bool userRequestsAiSuggestions,
      bool aiSuggestionsEnabled,
      SalesforceEnvironment selectedEnvironment});

  $SalesforceEnvironmentCopyWith<$Res> get selectedEnvironment;
}

/// @nodoc
class _$AppLocalSettingsCopyWithImpl<$Res>
    implements $AppLocalSettingsCopyWith<$Res> {
  _$AppLocalSettingsCopyWithImpl(this._self, this._then);

  final AppLocalSettings _self;
  final $Res Function(AppLocalSettings) _then;

  /// Create a copy of AppLocalSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDevOptions = null,
    Object? hasRequestedPushPermission = null,
    Object? userRequestsAiSuggestions = null,
    Object? aiSuggestionsEnabled = null,
    Object? selectedEnvironment = null,
  }) {
    return _then(_self.copyWith(
      showDevOptions: null == showDevOptions
          ? _self.showDevOptions
          : showDevOptions // ignore: cast_nullable_to_non_nullable
              as bool,
      hasRequestedPushPermission: null == hasRequestedPushPermission
          ? _self.hasRequestedPushPermission
          : hasRequestedPushPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      userRequestsAiSuggestions: null == userRequestsAiSuggestions
          ? _self.userRequestsAiSuggestions
          : userRequestsAiSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      aiSuggestionsEnabled: null == aiSuggestionsEnabled
          ? _self.aiSuggestionsEnabled
          : aiSuggestionsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedEnvironment: null == selectedEnvironment
          ? _self.selectedEnvironment
          : selectedEnvironment // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironment,
    ));
  }

  /// Create a copy of AppLocalSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SalesforceEnvironmentCopyWith<$Res> get selectedEnvironment {
    return $SalesforceEnvironmentCopyWith<$Res>(_self.selectedEnvironment,
        (value) {
      return _then(_self.copyWith(selectedEnvironment: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AppLocalSettings].
extension AppLocalSettingsPatterns on AppLocalSettings {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AppLocalSettings value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppLocalSettings() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AppLocalSettings value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppLocalSettings():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AppLocalSettings value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppLocalSettings() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool showDevOptions,
            bool hasRequestedPushPermission,
            bool userRequestsAiSuggestions,
            bool aiSuggestionsEnabled,
            SalesforceEnvironment selectedEnvironment)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppLocalSettings() when $default != null:
        return $default(
            _that.showDevOptions,
            _that.hasRequestedPushPermission,
            _that.userRequestsAiSuggestions,
            _that.aiSuggestionsEnabled,
            _that.selectedEnvironment);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool showDevOptions,
            bool hasRequestedPushPermission,
            bool userRequestsAiSuggestions,
            bool aiSuggestionsEnabled,
            SalesforceEnvironment selectedEnvironment)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppLocalSettings():
        return $default(
            _that.showDevOptions,
            _that.hasRequestedPushPermission,
            _that.userRequestsAiSuggestions,
            _that.aiSuggestionsEnabled,
            _that.selectedEnvironment);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool showDevOptions,
            bool hasRequestedPushPermission,
            bool userRequestsAiSuggestions,
            bool aiSuggestionsEnabled,
            SalesforceEnvironment selectedEnvironment)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppLocalSettings() when $default != null:
        return $default(
            _that.showDevOptions,
            _that.hasRequestedPushPermission,
            _that.userRequestsAiSuggestions,
            _that.aiSuggestionsEnabled,
            _that.selectedEnvironment);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AppLocalSettings extends AppLocalSettings {
  _AppLocalSettings(
      {this.showDevOptions = true,
      this.hasRequestedPushPermission = false,
      this.userRequestsAiSuggestions = false,
      this.aiSuggestionsEnabled = true,
      this.selectedEnvironment = const SalesforceEnvironment(
          type: SalesforceEnvironmentType.production)})
      : super._();
  factory _AppLocalSettings.fromJson(Map<String, dynamic> json) =>
      _$AppLocalSettingsFromJson(json);

  @override
  @JsonKey()
  final bool showDevOptions;
  @override
  @JsonKey()
  final bool hasRequestedPushPermission;
  @override
  @JsonKey()
  final bool userRequestsAiSuggestions;
  @override
  @JsonKey()
  final bool aiSuggestionsEnabled;
  @override
  @JsonKey()
  final SalesforceEnvironment selectedEnvironment;

  /// Create a copy of AppLocalSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AppLocalSettingsCopyWith<_AppLocalSettings> get copyWith =>
      __$AppLocalSettingsCopyWithImpl<_AppLocalSettings>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AppLocalSettingsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AppLocalSettings &&
            (identical(other.showDevOptions, showDevOptions) ||
                other.showDevOptions == showDevOptions) &&
            (identical(other.hasRequestedPushPermission,
                    hasRequestedPushPermission) ||
                other.hasRequestedPushPermission ==
                    hasRequestedPushPermission) &&
            (identical(other.userRequestsAiSuggestions,
                    userRequestsAiSuggestions) ||
                other.userRequestsAiSuggestions == userRequestsAiSuggestions) &&
            (identical(other.aiSuggestionsEnabled, aiSuggestionsEnabled) ||
                other.aiSuggestionsEnabled == aiSuggestionsEnabled) &&
            (identical(other.selectedEnvironment, selectedEnvironment) ||
                other.selectedEnvironment == selectedEnvironment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showDevOptions,
      hasRequestedPushPermission,
      userRequestsAiSuggestions,
      aiSuggestionsEnabled,
      selectedEnvironment);

  @override
  String toString() {
    return 'AppLocalSettings(showDevOptions: $showDevOptions, hasRequestedPushPermission: $hasRequestedPushPermission, userRequestsAiSuggestions: $userRequestsAiSuggestions, aiSuggestionsEnabled: $aiSuggestionsEnabled, selectedEnvironment: $selectedEnvironment)';
  }
}

/// @nodoc
abstract mixin class _$AppLocalSettingsCopyWith<$Res>
    implements $AppLocalSettingsCopyWith<$Res> {
  factory _$AppLocalSettingsCopyWith(
          _AppLocalSettings value, $Res Function(_AppLocalSettings) _then) =
      __$AppLocalSettingsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool showDevOptions,
      bool hasRequestedPushPermission,
      bool userRequestsAiSuggestions,
      bool aiSuggestionsEnabled,
      SalesforceEnvironment selectedEnvironment});

  @override
  $SalesforceEnvironmentCopyWith<$Res> get selectedEnvironment;
}

/// @nodoc
class __$AppLocalSettingsCopyWithImpl<$Res>
    implements _$AppLocalSettingsCopyWith<$Res> {
  __$AppLocalSettingsCopyWithImpl(this._self, this._then);

  final _AppLocalSettings _self;
  final $Res Function(_AppLocalSettings) _then;

  /// Create a copy of AppLocalSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? showDevOptions = null,
    Object? hasRequestedPushPermission = null,
    Object? userRequestsAiSuggestions = null,
    Object? aiSuggestionsEnabled = null,
    Object? selectedEnvironment = null,
  }) {
    return _then(_AppLocalSettings(
      showDevOptions: null == showDevOptions
          ? _self.showDevOptions
          : showDevOptions // ignore: cast_nullable_to_non_nullable
              as bool,
      hasRequestedPushPermission: null == hasRequestedPushPermission
          ? _self.hasRequestedPushPermission
          : hasRequestedPushPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      userRequestsAiSuggestions: null == userRequestsAiSuggestions
          ? _self.userRequestsAiSuggestions
          : userRequestsAiSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      aiSuggestionsEnabled: null == aiSuggestionsEnabled
          ? _self.aiSuggestionsEnabled
          : aiSuggestionsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedEnvironment: null == selectedEnvironment
          ? _self.selectedEnvironment
          : selectedEnvironment // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironment,
    ));
  }

  /// Create a copy of AppLocalSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SalesforceEnvironmentCopyWith<$Res> get selectedEnvironment {
    return $SalesforceEnvironmentCopyWith<$Res>(_self.selectedEnvironment,
        (value) {
      return _then(_self.copyWith(selectedEnvironment: value));
    });
  }
}

// dart format on

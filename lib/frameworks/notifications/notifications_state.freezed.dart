// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notifications_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$NotificationsState {
  @JsonKey(ignore: true)
  List<InAppMessageNotification> get unacknowledgedNotifications;
  List<QueueReceiveMessage> get messagesWithUnprocessedActions;

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NotificationsStateCopyWith<NotificationsState> get copyWith =>
      _$NotificationsStateCopyWithImpl<NotificationsState>(
          this as NotificationsState, _$identity);

  /// Serializes this NotificationsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NotificationsState &&
            const DeepCollectionEquality().equals(
                other.unacknowledgedNotifications,
                unacknowledgedNotifications) &&
            const DeepCollectionEquality().equals(
                other.messagesWithUnprocessedActions,
                messagesWithUnprocessedActions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(unacknowledgedNotifications),
      const DeepCollectionEquality().hash(messagesWithUnprocessedActions));

  @override
  String toString() {
    return 'NotificationsState(unacknowledgedNotifications: $unacknowledgedNotifications, messagesWithUnprocessedActions: $messagesWithUnprocessedActions)';
  }
}

/// @nodoc
abstract mixin class $NotificationsStateCopyWith<$Res> {
  factory $NotificationsStateCopyWith(
          NotificationsState value, $Res Function(NotificationsState) _then) =
      _$NotificationsStateCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(ignore: true)
      List<InAppMessageNotification> unacknowledgedNotifications,
      List<QueueReceiveMessage> messagesWithUnprocessedActions});
}

/// @nodoc
class _$NotificationsStateCopyWithImpl<$Res>
    implements $NotificationsStateCopyWith<$Res> {
  _$NotificationsStateCopyWithImpl(this._self, this._then);

  final NotificationsState _self;
  final $Res Function(NotificationsState) _then;

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unacknowledgedNotifications = null,
    Object? messagesWithUnprocessedActions = null,
  }) {
    return _then(_self.copyWith(
      unacknowledgedNotifications: null == unacknowledgedNotifications
          ? _self.unacknowledgedNotifications
          : unacknowledgedNotifications // ignore: cast_nullable_to_non_nullable
              as List<InAppMessageNotification>,
      messagesWithUnprocessedActions: null == messagesWithUnprocessedActions
          ? _self.messagesWithUnprocessedActions
          : messagesWithUnprocessedActions // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
    ));
  }
}

/// Adds pattern-matching-related methods to [NotificationsState].
extension NotificationsStatePatterns on NotificationsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_NotificationsState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _NotificationsState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_NotificationsState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_NotificationsState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true)
            List<InAppMessageNotification> unacknowledgedNotifications,
            List<QueueReceiveMessage> messagesWithUnprocessedActions)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _NotificationsState() when $default != null:
        return $default(_that.unacknowledgedNotifications,
            _that.messagesWithUnprocessedActions);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true)
            List<InAppMessageNotification> unacknowledgedNotifications,
            List<QueueReceiveMessage> messagesWithUnprocessedActions)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsState():
        return $default(_that.unacknowledgedNotifications,
            _that.messagesWithUnprocessedActions);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(ignore: true)
            List<InAppMessageNotification> unacknowledgedNotifications,
            List<QueueReceiveMessage> messagesWithUnprocessedActions)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _NotificationsState() when $default != null:
        return $default(_that.unacknowledgedNotifications,
            _that.messagesWithUnprocessedActions);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _NotificationsState implements NotificationsState {
  const _NotificationsState(
      {@JsonKey(ignore: true)
      final List<InAppMessageNotification> unacknowledgedNotifications =
          const [],
      final List<QueueReceiveMessage> messagesWithUnprocessedActions =
          const []})
      : _unacknowledgedNotifications = unacknowledgedNotifications,
        _messagesWithUnprocessedActions = messagesWithUnprocessedActions;
  factory _NotificationsState.fromJson(Map<String, dynamic> json) =>
      _$NotificationsStateFromJson(json);

  final List<InAppMessageNotification> _unacknowledgedNotifications;
  @override
  @JsonKey(ignore: true)
  List<InAppMessageNotification> get unacknowledgedNotifications {
    if (_unacknowledgedNotifications is EqualUnmodifiableListView)
      return _unacknowledgedNotifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_unacknowledgedNotifications);
  }

  final List<QueueReceiveMessage> _messagesWithUnprocessedActions;
  @override
  @JsonKey()
  List<QueueReceiveMessage> get messagesWithUnprocessedActions {
    if (_messagesWithUnprocessedActions is EqualUnmodifiableListView)
      return _messagesWithUnprocessedActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messagesWithUnprocessedActions);
  }

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NotificationsStateCopyWith<_NotificationsState> get copyWith =>
      __$NotificationsStateCopyWithImpl<_NotificationsState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$NotificationsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NotificationsState &&
            const DeepCollectionEquality().equals(
                other._unacknowledgedNotifications,
                _unacknowledgedNotifications) &&
            const DeepCollectionEquality().equals(
                other._messagesWithUnprocessedActions,
                _messagesWithUnprocessedActions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_unacknowledgedNotifications),
      const DeepCollectionEquality().hash(_messagesWithUnprocessedActions));

  @override
  String toString() {
    return 'NotificationsState(unacknowledgedNotifications: $unacknowledgedNotifications, messagesWithUnprocessedActions: $messagesWithUnprocessedActions)';
  }
}

/// @nodoc
abstract mixin class _$NotificationsStateCopyWith<$Res>
    implements $NotificationsStateCopyWith<$Res> {
  factory _$NotificationsStateCopyWith(
          _NotificationsState value, $Res Function(_NotificationsState) _then) =
      __$NotificationsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(ignore: true)
      List<InAppMessageNotification> unacknowledgedNotifications,
      List<QueueReceiveMessage> messagesWithUnprocessedActions});
}

/// @nodoc
class __$NotificationsStateCopyWithImpl<$Res>
    implements _$NotificationsStateCopyWith<$Res> {
  __$NotificationsStateCopyWithImpl(this._self, this._then);

  final _NotificationsState _self;
  final $Res Function(_NotificationsState) _then;

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? unacknowledgedNotifications = null,
    Object? messagesWithUnprocessedActions = null,
  }) {
    return _then(_NotificationsState(
      unacknowledgedNotifications: null == unacknowledgedNotifications
          ? _self._unacknowledgedNotifications
          : unacknowledgedNotifications // ignore: cast_nullable_to_non_nullable
              as List<InAppMessageNotification>,
      messagesWithUnprocessedActions: null == messagesWithUnprocessedActions
          ? _self._messagesWithUnprocessedActions
          : messagesWithUnprocessedActions // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notifications_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NotificationsState _$NotificationsStateFromJson(Map json) => $checkedCreate(
      '_NotificationsState',
      json,
      ($checkedConvert) {
        final val = _NotificationsState(
          messagesWithUnprocessedActions: $checkedConvert(
              'messagesWithUnprocessedActions',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => QueueReceiveMessage.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const []),
        );
        return val;
      },
    );

Map<String, dynamic> _$NotificationsStateToJson(_NotificationsState instance) =>
    <String, dynamic>{
      'messagesWithUnprocessedActions': instance.messagesWithUnprocessedActions
          .map((e) => e.toJson())
          .toList(),
    };

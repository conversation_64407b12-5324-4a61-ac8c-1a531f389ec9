// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'in_app_message_notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_InAppMessageNotification _$InAppMessageNotificationFromJson(Map json) =>
    $checkedCreate(
      '_InAppMessageNotification',
      json,
      ($checkedConvert) {
        final val = _InAppMessageNotification(
          messageId: $checkedConvert('messageId', (v) => v as String),
          pushNotificationId:
              $checkedConvert('pushNotificationId', (v) => v as String?),
          conversationId: $checkedConvert(
              'conversationId', (v) => const ParseSfIdConverter().fromJson(v)),
          conversationScrtUuid:
              $checkedConvert('conversationScrtUuid', (v) => v as String?),
          messagingSessionId: $checkedConvert('messagingSessionId',
              (v) => const ParseSfIdConverter().fromJson(v)),
          title: $checkedConvert('title', (v) => v as String),
          body: $checkedConvert('body', (v) => v as String),
          username: $checkedConvert('username', (v) => v as String?),
          userPhotoUrl: $checkedConvert('userPhotoUrl', (v) => v as String?),
          workId: $checkedConvert(
              'workId', (v) => const ParseSfIdConverter().fromJson(v)),
          workTargetId: $checkedConvert(
              'workTargetId', (v) => const ParseSfIdConverter().fromJson(v)),
          notificationAction:
              $checkedConvert('notificationAction', (v) => v as String?),
          channelType: $checkedConvert(
              'channelType', (v) => $enumDecode(_$ChannelTypeEnumMap, v)),
          messageType: $checkedConvert(
              'messageType',
              (v) =>
                  $enumDecodeNullable(_$MessageTypeEnumMap, v) ??
                  MessageType.message),
          lastMessageTime:
              $checkedConvert('lastMessageTime', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$InAppMessageNotificationToJson(
        _InAppMessageNotification instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      if (instance.pushNotificationId case final value?)
        'pushNotificationId': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.conversationId, const ParseSfIdConverter().toJson)
          case final value?)
        'conversationId': value,
      if (instance.conversationScrtUuid case final value?)
        'conversationScrtUuid': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.messagingSessionId, const ParseSfIdConverter().toJson)
          case final value?)
        'messagingSessionId': value,
      'title': instance.title,
      'body': instance.body,
      if (instance.username case final value?) 'username': value,
      if (instance.userPhotoUrl case final value?) 'userPhotoUrl': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.workId, const ParseSfIdConverter().toJson)
          case final value?)
        'workId': value,
      if (_$JsonConverterToJson<Object?, SfId>(
              instance.workTargetId, const ParseSfIdConverter().toJson)
          case final value?)
        'workTargetId': value,
      if (instance.notificationAction case final value?)
        'notificationAction': value,
      'channelType': _$ChannelTypeEnumMap[instance.channelType]!,
      'messageType': _$MessageTypeEnumMap[instance.messageType]!,
      if (instance.lastMessageTime case final value?) 'lastMessageTime': value,
    };

const _$ChannelTypeEnumMap = {
  ChannelType.sfdcLiveagent: 'sfdcLiveagent',
  ChannelType.sfdcSms: 'sfdcSms',
  ChannelType.sfdcWhastappStandard: 'sfdcWhastappStandard',
  ChannelType.sfdcWhatsappEnhanced: 'sfdcWhatsappEnhanced',
  ChannelType.sfdcFacebookMessenger: 'sfdcFacebookMessenger',
  ChannelType.msGoogleBusinessMessages: 'msGoogleBusinessMessages',
  ChannelType.msFacebook: 'msFacebook',
  ChannelType.msInstagram: 'msInstagram',
  ChannelType.msTwitter: 'msTwitter',
  ChannelType.msTikTok: 'msTikTok',
  ChannelType.apple_business_chat: 'apple_business_chat',
};

const _$MessageTypeEnumMap = {
  MessageType.message: 'message',
  MessageType.work: 'work',
};

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);

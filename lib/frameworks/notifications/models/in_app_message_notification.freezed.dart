// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'in_app_message_notification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$InAppMessageNotification {
  String get messageId;
  String? get pushNotificationId;
  @ParseSfIdConverter()
  SfId? get conversationId;
  String? get conversationScrtUuid;
  @ParseSfIdConverter()
  SfId? get messagingSessionId;
  String get title;
  String get body;
  @Deprecated('do we still need this?')
  String? get username;
  String? get userPhotoUrl;
  @ParseSfIdConverter()
  SfId? get workId;
  @ParseSfIdConverter()
  SfId? get workTargetId;
  String? get notificationAction;
  ChannelType get channelType;
  MessageType get messageType;
  String? get lastMessageTime;

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InAppMessageNotificationCopyWith<InAppMessageNotification> get copyWith =>
      _$InAppMessageNotificationCopyWithImpl<InAppMessageNotification>(
          this as InAppMessageNotification, _$identity);

  /// Serializes this InAppMessageNotification to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InAppMessageNotification &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.pushNotificationId, pushNotificationId) ||
                other.pushNotificationId == pushNotificationId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.conversationScrtUuid, conversationScrtUuid) ||
                other.conversationScrtUuid == conversationScrtUuid) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userPhotoUrl, userPhotoUrl) ||
                other.userPhotoUrl == userPhotoUrl) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.notificationAction, notificationAction) ||
                other.notificationAction == notificationAction) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.lastMessageTime, lastMessageTime) ||
                other.lastMessageTime == lastMessageTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      messageId,
      pushNotificationId,
      conversationId,
      conversationScrtUuid,
      messagingSessionId,
      title,
      body,
      username,
      userPhotoUrl,
      workId,
      workTargetId,
      notificationAction,
      channelType,
      messageType,
      lastMessageTime);
}

/// @nodoc
abstract mixin class $InAppMessageNotificationCopyWith<$Res> {
  factory $InAppMessageNotificationCopyWith(InAppMessageNotification value,
          $Res Function(InAppMessageNotification) _then) =
      _$InAppMessageNotificationCopyWithImpl;
  @useResult
  $Res call(
      {String messageId,
      String? pushNotificationId,
      @ParseSfIdConverter() SfId? conversationId,
      String? conversationScrtUuid,
      @ParseSfIdConverter() SfId? messagingSessionId,
      String title,
      String body,
      @Deprecated('do we still need this?') String? username,
      String? userPhotoUrl,
      @ParseSfIdConverter() SfId? workId,
      @ParseSfIdConverter() SfId? workTargetId,
      String? notificationAction,
      ChannelType channelType,
      MessageType messageType,
      String? lastMessageTime});

  $SfIdCopyWith<$Res>? get conversationId;
  $SfIdCopyWith<$Res>? get messagingSessionId;
  $SfIdCopyWith<$Res>? get workId;
  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class _$InAppMessageNotificationCopyWithImpl<$Res>
    implements $InAppMessageNotificationCopyWith<$Res> {
  _$InAppMessageNotificationCopyWithImpl(this._self, this._then);

  final InAppMessageNotification _self;
  final $Res Function(InAppMessageNotification) _then;

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? pushNotificationId = freezed,
    Object? conversationId = freezed,
    Object? conversationScrtUuid = freezed,
    Object? messagingSessionId = freezed,
    Object? title = null,
    Object? body = null,
    Object? username = freezed,
    Object? userPhotoUrl = freezed,
    Object? workId = freezed,
    Object? workTargetId = freezed,
    Object? notificationAction = freezed,
    Object? channelType = null,
    Object? messageType = null,
    Object? lastMessageTime = freezed,
  }) {
    return _then(_self.copyWith(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      pushNotificationId: freezed == pushNotificationId
          ? _self.pushNotificationId
          : pushNotificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      conversationScrtUuid: freezed == conversationScrtUuid
          ? _self.conversationScrtUuid
          : conversationScrtUuid // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingSessionId: freezed == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _self.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _self.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      workId: freezed == workId
          ? _self.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      workTargetId: freezed == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      notificationAction: freezed == notificationAction
          ? _self.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as ChannelType,
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as MessageType,
      lastMessageTime: freezed == lastMessageTime
          ? _self.lastMessageTime
          : lastMessageTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get conversationId {
    if (_self.conversationId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.conversationId!, (value) {
      return _then(_self.copyWith(conversationId: value));
    });
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingSessionId {
    if (_self.messagingSessionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingSessionId!, (value) {
      return _then(_self.copyWith(messagingSessionId: value));
    });
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workId {
    if (_self.workId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workId!, (value) {
      return _then(_self.copyWith(workId: value));
    });
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_self.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workTargetId!, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [InAppMessageNotification].
extension InAppMessageNotificationPatterns on InAppMessageNotification {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_InAppMessageNotification value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InAppMessageNotification() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_InAppMessageNotification value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InAppMessageNotification():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_InAppMessageNotification value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InAppMessageNotification() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String messageId,
            String? pushNotificationId,
            @ParseSfIdConverter() SfId? conversationId,
            String? conversationScrtUuid,
            @ParseSfIdConverter() SfId? messagingSessionId,
            String title,
            String body,
            @Deprecated('do we still need this?') String? username,
            String? userPhotoUrl,
            @ParseSfIdConverter() SfId? workId,
            @ParseSfIdConverter() SfId? workTargetId,
            String? notificationAction,
            ChannelType channelType,
            MessageType messageType,
            String? lastMessageTime)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InAppMessageNotification() when $default != null:
        return $default(
            _that.messageId,
            _that.pushNotificationId,
            _that.conversationId,
            _that.conversationScrtUuid,
            _that.messagingSessionId,
            _that.title,
            _that.body,
            _that.username,
            _that.userPhotoUrl,
            _that.workId,
            _that.workTargetId,
            _that.notificationAction,
            _that.channelType,
            _that.messageType,
            _that.lastMessageTime);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String messageId,
            String? pushNotificationId,
            @ParseSfIdConverter() SfId? conversationId,
            String? conversationScrtUuid,
            @ParseSfIdConverter() SfId? messagingSessionId,
            String title,
            String body,
            @Deprecated('do we still need this?') String? username,
            String? userPhotoUrl,
            @ParseSfIdConverter() SfId? workId,
            @ParseSfIdConverter() SfId? workTargetId,
            String? notificationAction,
            ChannelType channelType,
            MessageType messageType,
            String? lastMessageTime)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InAppMessageNotification():
        return $default(
            _that.messageId,
            _that.pushNotificationId,
            _that.conversationId,
            _that.conversationScrtUuid,
            _that.messagingSessionId,
            _that.title,
            _that.body,
            _that.username,
            _that.userPhotoUrl,
            _that.workId,
            _that.workTargetId,
            _that.notificationAction,
            _that.channelType,
            _that.messageType,
            _that.lastMessageTime);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String messageId,
            String? pushNotificationId,
            @ParseSfIdConverter() SfId? conversationId,
            String? conversationScrtUuid,
            @ParseSfIdConverter() SfId? messagingSessionId,
            String title,
            String body,
            @Deprecated('do we still need this?') String? username,
            String? userPhotoUrl,
            @ParseSfIdConverter() SfId? workId,
            @ParseSfIdConverter() SfId? workTargetId,
            String? notificationAction,
            ChannelType channelType,
            MessageType messageType,
            String? lastMessageTime)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InAppMessageNotification() when $default != null:
        return $default(
            _that.messageId,
            _that.pushNotificationId,
            _that.conversationId,
            _that.conversationScrtUuid,
            _that.messagingSessionId,
            _that.title,
            _that.body,
            _that.username,
            _that.userPhotoUrl,
            _that.workId,
            _that.workTargetId,
            _that.notificationAction,
            _that.channelType,
            _that.messageType,
            _that.lastMessageTime);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _InAppMessageNotification implements InAppMessageNotification {
  const _InAppMessageNotification(
      {required this.messageId,
      this.pushNotificationId,
      @ParseSfIdConverter() this.conversationId,
      this.conversationScrtUuid,
      @ParseSfIdConverter() this.messagingSessionId,
      required this.title,
      required this.body,
      @Deprecated('do we still need this?') this.username,
      this.userPhotoUrl,
      @ParseSfIdConverter() this.workId,
      @ParseSfIdConverter() this.workTargetId,
      this.notificationAction,
      required this.channelType,
      this.messageType = MessageType.message,
      this.lastMessageTime});
  factory _InAppMessageNotification.fromJson(Map<String, dynamic> json) =>
      _$InAppMessageNotificationFromJson(json);

  @override
  final String messageId;
  @override
  final String? pushNotificationId;
  @override
  @ParseSfIdConverter()
  final SfId? conversationId;
  @override
  final String? conversationScrtUuid;
  @override
  @ParseSfIdConverter()
  final SfId? messagingSessionId;
  @override
  final String title;
  @override
  final String body;
  @override
  @Deprecated('do we still need this?')
  final String? username;
  @override
  final String? userPhotoUrl;
  @override
  @ParseSfIdConverter()
  final SfId? workId;
  @override
  @ParseSfIdConverter()
  final SfId? workTargetId;
  @override
  final String? notificationAction;
  @override
  final ChannelType channelType;
  @override
  @JsonKey()
  final MessageType messageType;
  @override
  final String? lastMessageTime;

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InAppMessageNotificationCopyWith<_InAppMessageNotification> get copyWith =>
      __$InAppMessageNotificationCopyWithImpl<_InAppMessageNotification>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InAppMessageNotificationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InAppMessageNotification &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.pushNotificationId, pushNotificationId) ||
                other.pushNotificationId == pushNotificationId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.conversationScrtUuid, conversationScrtUuid) ||
                other.conversationScrtUuid == conversationScrtUuid) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userPhotoUrl, userPhotoUrl) ||
                other.userPhotoUrl == userPhotoUrl) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.notificationAction, notificationAction) ||
                other.notificationAction == notificationAction) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.lastMessageTime, lastMessageTime) ||
                other.lastMessageTime == lastMessageTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      messageId,
      pushNotificationId,
      conversationId,
      conversationScrtUuid,
      messagingSessionId,
      title,
      body,
      username,
      userPhotoUrl,
      workId,
      workTargetId,
      notificationAction,
      channelType,
      messageType,
      lastMessageTime);
}

/// @nodoc
abstract mixin class _$InAppMessageNotificationCopyWith<$Res>
    implements $InAppMessageNotificationCopyWith<$Res> {
  factory _$InAppMessageNotificationCopyWith(_InAppMessageNotification value,
          $Res Function(_InAppMessageNotification) _then) =
      __$InAppMessageNotificationCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String messageId,
      String? pushNotificationId,
      @ParseSfIdConverter() SfId? conversationId,
      String? conversationScrtUuid,
      @ParseSfIdConverter() SfId? messagingSessionId,
      String title,
      String body,
      @Deprecated('do we still need this?') String? username,
      String? userPhotoUrl,
      @ParseSfIdConverter() SfId? workId,
      @ParseSfIdConverter() SfId? workTargetId,
      String? notificationAction,
      ChannelType channelType,
      MessageType messageType,
      String? lastMessageTime});

  @override
  $SfIdCopyWith<$Res>? get conversationId;
  @override
  $SfIdCopyWith<$Res>? get messagingSessionId;
  @override
  $SfIdCopyWith<$Res>? get workId;
  @override
  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class __$InAppMessageNotificationCopyWithImpl<$Res>
    implements _$InAppMessageNotificationCopyWith<$Res> {
  __$InAppMessageNotificationCopyWithImpl(this._self, this._then);

  final _InAppMessageNotification _self;
  final $Res Function(_InAppMessageNotification) _then;

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messageId = null,
    Object? pushNotificationId = freezed,
    Object? conversationId = freezed,
    Object? conversationScrtUuid = freezed,
    Object? messagingSessionId = freezed,
    Object? title = null,
    Object? body = null,
    Object? username = freezed,
    Object? userPhotoUrl = freezed,
    Object? workId = freezed,
    Object? workTargetId = freezed,
    Object? notificationAction = freezed,
    Object? channelType = null,
    Object? messageType = null,
    Object? lastMessageTime = freezed,
  }) {
    return _then(_InAppMessageNotification(
      messageId: null == messageId
          ? _self.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      pushNotificationId: freezed == pushNotificationId
          ? _self.pushNotificationId
          : pushNotificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      conversationScrtUuid: freezed == conversationScrtUuid
          ? _self.conversationScrtUuid
          : conversationScrtUuid // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingSessionId: freezed == messagingSessionId
          ? _self.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _self.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _self.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      workId: freezed == workId
          ? _self.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      workTargetId: freezed == workTargetId
          ? _self.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      notificationAction: freezed == notificationAction
          ? _self.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as ChannelType,
      messageType: null == messageType
          ? _self.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as MessageType,
      lastMessageTime: freezed == lastMessageTime
          ? _self.lastMessageTime
          : lastMessageTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get conversationId {
    if (_self.conversationId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.conversationId!, (value) {
      return _then(_self.copyWith(conversationId: value));
    });
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingSessionId {
    if (_self.messagingSessionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingSessionId!, (value) {
      return _then(_self.copyWith(messagingSessionId: value));
    });
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workId {
    if (_self.workId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workId!, (value) {
      return _then(_self.copyWith(workId: value));
    });
  }

  /// Create a copy of InAppMessageNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_self.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.workTargetId!, (value) {
      return _then(_self.copyWith(workTargetId: value));
    });
  }
}

// dart format on

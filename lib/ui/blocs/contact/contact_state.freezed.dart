// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContactState {
  Contact? get contact;
  Contact? get savedContact;
  bool get isLoading;
  bool get isEditing;
  bool get showFirstNameErrorText;
  bool get showLastNameErrorText;
  bool get showPhoneNumberErrorText;
  bool get showEmailErrorText;
  MessagingChannelEntry? get messagingChannelEntry;
  @JsonKey(ignore: true)
  UiEvent<SfId>? get goToConversation;
  @JsonKey(ignore: true)
  UiEvent<SfId>? get goToNewConversation;

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactStateCopyWith<ContactState> get copyWith =>
      _$ContactStateCopyWithImpl<ContactState>(
          this as ContactState, _$identity);

  /// Serializes this ContactState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactState &&
            (identical(other.contact, contact) || other.contact == contact) &&
            (identical(other.savedContact, savedContact) ||
                other.savedContact == savedContact) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isEditing, isEditing) ||
                other.isEditing == isEditing) &&
            (identical(other.showFirstNameErrorText, showFirstNameErrorText) ||
                other.showFirstNameErrorText == showFirstNameErrorText) &&
            (identical(other.showLastNameErrorText, showLastNameErrorText) ||
                other.showLastNameErrorText == showLastNameErrorText) &&
            (identical(
                    other.showPhoneNumberErrorText, showPhoneNumberErrorText) ||
                other.showPhoneNumberErrorText == showPhoneNumberErrorText) &&
            (identical(other.showEmailErrorText, showEmailErrorText) ||
                other.showEmailErrorText == showEmailErrorText) &&
            (identical(other.messagingChannelEntry, messagingChannelEntry) ||
                other.messagingChannelEntry == messagingChannelEntry) &&
            (identical(other.goToConversation, goToConversation) ||
                other.goToConversation == goToConversation) &&
            (identical(other.goToNewConversation, goToNewConversation) ||
                other.goToNewConversation == goToNewConversation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      contact,
      savedContact,
      isLoading,
      isEditing,
      showFirstNameErrorText,
      showLastNameErrorText,
      showPhoneNumberErrorText,
      showEmailErrorText,
      messagingChannelEntry,
      goToConversation,
      goToNewConversation);

  @override
  String toString() {
    return 'ContactState(contact: $contact, savedContact: $savedContact, isLoading: $isLoading, isEditing: $isEditing, showFirstNameErrorText: $showFirstNameErrorText, showLastNameErrorText: $showLastNameErrorText, showPhoneNumberErrorText: $showPhoneNumberErrorText, showEmailErrorText: $showEmailErrorText, messagingChannelEntry: $messagingChannelEntry, goToConversation: $goToConversation, goToNewConversation: $goToNewConversation)';
  }
}

/// @nodoc
abstract mixin class $ContactStateCopyWith<$Res> {
  factory $ContactStateCopyWith(
          ContactState value, $Res Function(ContactState) _then) =
      _$ContactStateCopyWithImpl;
  @useResult
  $Res call(
      {Contact? contact,
      Contact? savedContact,
      bool isLoading,
      bool isEditing,
      bool showFirstNameErrorText,
      bool showLastNameErrorText,
      bool showPhoneNumberErrorText,
      bool showEmailErrorText,
      MessagingChannelEntry? messagingChannelEntry,
      @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
      @JsonKey(ignore: true) UiEvent<SfId>? goToNewConversation});

  $ContactCopyWith<$Res>? get contact;
  $ContactCopyWith<$Res>? get savedContact;
  $MessagingChannelEntryCopyWith<$Res>? get messagingChannelEntry;
}

/// @nodoc
class _$ContactStateCopyWithImpl<$Res> implements $ContactStateCopyWith<$Res> {
  _$ContactStateCopyWithImpl(this._self, this._then);

  final ContactState _self;
  final $Res Function(ContactState) _then;

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contact = freezed,
    Object? savedContact = freezed,
    Object? isLoading = null,
    Object? isEditing = null,
    Object? showFirstNameErrorText = null,
    Object? showLastNameErrorText = null,
    Object? showPhoneNumberErrorText = null,
    Object? showEmailErrorText = null,
    Object? messagingChannelEntry = freezed,
    Object? goToConversation = freezed,
    Object? goToNewConversation = freezed,
  }) {
    return _then(_self.copyWith(
      contact: freezed == contact
          ? _self.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      savedContact: freezed == savedContact
          ? _self.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditing: null == isEditing
          ? _self.isEditing
          : isEditing // ignore: cast_nullable_to_non_nullable
              as bool,
      showFirstNameErrorText: null == showFirstNameErrorText
          ? _self.showFirstNameErrorText
          : showFirstNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showLastNameErrorText: null == showLastNameErrorText
          ? _self.showLastNameErrorText
          : showLastNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showPhoneNumberErrorText: null == showPhoneNumberErrorText
          ? _self.showPhoneNumberErrorText
          : showPhoneNumberErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showEmailErrorText: null == showEmailErrorText
          ? _self.showEmailErrorText
          : showEmailErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingChannelEntry: freezed == messagingChannelEntry
          ? _self.messagingChannelEntry
          : messagingChannelEntry // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
      goToConversation: freezed == goToConversation
          ? _self.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      goToNewConversation: freezed == goToNewConversation
          ? _self.goToNewConversation
          : goToNewConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
    ));
  }

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get contact {
    if (_self.contact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_self.contact!, (value) {
      return _then(_self.copyWith(contact: value));
    });
  }

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get savedContact {
    if (_self.savedContact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_self.savedContact!, (value) {
      return _then(_self.copyWith(savedContact: value));
    });
  }

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelEntryCopyWith<$Res>? get messagingChannelEntry {
    if (_self.messagingChannelEntry == null) {
      return null;
    }

    return $MessagingChannelEntryCopyWith<$Res>(_self.messagingChannelEntry!,
        (value) {
      return _then(_self.copyWith(messagingChannelEntry: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ContactState].
extension ContactStatePatterns on ContactState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            Contact? contact,
            Contact? savedContact,
            bool isLoading,
            bool isEditing,
            bool showFirstNameErrorText,
            bool showLastNameErrorText,
            bool showPhoneNumberErrorText,
            bool showEmailErrorText,
            MessagingChannelEntry? messagingChannelEntry,
            @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
            @JsonKey(ignore: true) UiEvent<SfId>? goToNewConversation)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactState() when $default != null:
        return $default(
            _that.contact,
            _that.savedContact,
            _that.isLoading,
            _that.isEditing,
            _that.showFirstNameErrorText,
            _that.showLastNameErrorText,
            _that.showPhoneNumberErrorText,
            _that.showEmailErrorText,
            _that.messagingChannelEntry,
            _that.goToConversation,
            _that.goToNewConversation);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            Contact? contact,
            Contact? savedContact,
            bool isLoading,
            bool isEditing,
            bool showFirstNameErrorText,
            bool showLastNameErrorText,
            bool showPhoneNumberErrorText,
            bool showEmailErrorText,
            MessagingChannelEntry? messagingChannelEntry,
            @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
            @JsonKey(ignore: true) UiEvent<SfId>? goToNewConversation)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactState():
        return $default(
            _that.contact,
            _that.savedContact,
            _that.isLoading,
            _that.isEditing,
            _that.showFirstNameErrorText,
            _that.showLastNameErrorText,
            _that.showPhoneNumberErrorText,
            _that.showEmailErrorText,
            _that.messagingChannelEntry,
            _that.goToConversation,
            _that.goToNewConversation);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            Contact? contact,
            Contact? savedContact,
            bool isLoading,
            bool isEditing,
            bool showFirstNameErrorText,
            bool showLastNameErrorText,
            bool showPhoneNumberErrorText,
            bool showEmailErrorText,
            MessagingChannelEntry? messagingChannelEntry,
            @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
            @JsonKey(ignore: true) UiEvent<SfId>? goToNewConversation)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactState() when $default != null:
        return $default(
            _that.contact,
            _that.savedContact,
            _that.isLoading,
            _that.isEditing,
            _that.showFirstNameErrorText,
            _that.showLastNameErrorText,
            _that.showPhoneNumberErrorText,
            _that.showEmailErrorText,
            _that.messagingChannelEntry,
            _that.goToConversation,
            _that.goToNewConversation);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactState extends ContactState {
  const _ContactState(
      {this.contact,
      this.savedContact,
      this.isLoading = true,
      this.isEditing = false,
      this.showFirstNameErrorText = false,
      this.showLastNameErrorText = false,
      this.showPhoneNumberErrorText = false,
      this.showEmailErrorText = false,
      this.messagingChannelEntry,
      @JsonKey(ignore: true) this.goToConversation,
      @JsonKey(ignore: true) this.goToNewConversation})
      : super._();
  factory _ContactState.fromJson(Map<String, dynamic> json) =>
      _$ContactStateFromJson(json);

  @override
  final Contact? contact;
  @override
  final Contact? savedContact;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isEditing;
  @override
  @JsonKey()
  final bool showFirstNameErrorText;
  @override
  @JsonKey()
  final bool showLastNameErrorText;
  @override
  @JsonKey()
  final bool showPhoneNumberErrorText;
  @override
  @JsonKey()
  final bool showEmailErrorText;
  @override
  final MessagingChannelEntry? messagingChannelEntry;
  @override
  @JsonKey(ignore: true)
  final UiEvent<SfId>? goToConversation;
  @override
  @JsonKey(ignore: true)
  final UiEvent<SfId>? goToNewConversation;

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactStateCopyWith<_ContactState> get copyWith =>
      __$ContactStateCopyWithImpl<_ContactState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactState &&
            (identical(other.contact, contact) || other.contact == contact) &&
            (identical(other.savedContact, savedContact) ||
                other.savedContact == savedContact) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isEditing, isEditing) ||
                other.isEditing == isEditing) &&
            (identical(other.showFirstNameErrorText, showFirstNameErrorText) ||
                other.showFirstNameErrorText == showFirstNameErrorText) &&
            (identical(other.showLastNameErrorText, showLastNameErrorText) ||
                other.showLastNameErrorText == showLastNameErrorText) &&
            (identical(
                    other.showPhoneNumberErrorText, showPhoneNumberErrorText) ||
                other.showPhoneNumberErrorText == showPhoneNumberErrorText) &&
            (identical(other.showEmailErrorText, showEmailErrorText) ||
                other.showEmailErrorText == showEmailErrorText) &&
            (identical(other.messagingChannelEntry, messagingChannelEntry) ||
                other.messagingChannelEntry == messagingChannelEntry) &&
            (identical(other.goToConversation, goToConversation) ||
                other.goToConversation == goToConversation) &&
            (identical(other.goToNewConversation, goToNewConversation) ||
                other.goToNewConversation == goToNewConversation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      contact,
      savedContact,
      isLoading,
      isEditing,
      showFirstNameErrorText,
      showLastNameErrorText,
      showPhoneNumberErrorText,
      showEmailErrorText,
      messagingChannelEntry,
      goToConversation,
      goToNewConversation);

  @override
  String toString() {
    return 'ContactState(contact: $contact, savedContact: $savedContact, isLoading: $isLoading, isEditing: $isEditing, showFirstNameErrorText: $showFirstNameErrorText, showLastNameErrorText: $showLastNameErrorText, showPhoneNumberErrorText: $showPhoneNumberErrorText, showEmailErrorText: $showEmailErrorText, messagingChannelEntry: $messagingChannelEntry, goToConversation: $goToConversation, goToNewConversation: $goToNewConversation)';
  }
}

/// @nodoc
abstract mixin class _$ContactStateCopyWith<$Res>
    implements $ContactStateCopyWith<$Res> {
  factory _$ContactStateCopyWith(
          _ContactState value, $Res Function(_ContactState) _then) =
      __$ContactStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Contact? contact,
      Contact? savedContact,
      bool isLoading,
      bool isEditing,
      bool showFirstNameErrorText,
      bool showLastNameErrorText,
      bool showPhoneNumberErrorText,
      bool showEmailErrorText,
      MessagingChannelEntry? messagingChannelEntry,
      @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
      @JsonKey(ignore: true) UiEvent<SfId>? goToNewConversation});

  @override
  $ContactCopyWith<$Res>? get contact;
  @override
  $ContactCopyWith<$Res>? get savedContact;
  @override
  $MessagingChannelEntryCopyWith<$Res>? get messagingChannelEntry;
}

/// @nodoc
class __$ContactStateCopyWithImpl<$Res>
    implements _$ContactStateCopyWith<$Res> {
  __$ContactStateCopyWithImpl(this._self, this._then);

  final _ContactState _self;
  final $Res Function(_ContactState) _then;

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contact = freezed,
    Object? savedContact = freezed,
    Object? isLoading = null,
    Object? isEditing = null,
    Object? showFirstNameErrorText = null,
    Object? showLastNameErrorText = null,
    Object? showPhoneNumberErrorText = null,
    Object? showEmailErrorText = null,
    Object? messagingChannelEntry = freezed,
    Object? goToConversation = freezed,
    Object? goToNewConversation = freezed,
  }) {
    return _then(_ContactState(
      contact: freezed == contact
          ? _self.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      savedContact: freezed == savedContact
          ? _self.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditing: null == isEditing
          ? _self.isEditing
          : isEditing // ignore: cast_nullable_to_non_nullable
              as bool,
      showFirstNameErrorText: null == showFirstNameErrorText
          ? _self.showFirstNameErrorText
          : showFirstNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showLastNameErrorText: null == showLastNameErrorText
          ? _self.showLastNameErrorText
          : showLastNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showPhoneNumberErrorText: null == showPhoneNumberErrorText
          ? _self.showPhoneNumberErrorText
          : showPhoneNumberErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showEmailErrorText: null == showEmailErrorText
          ? _self.showEmailErrorText
          : showEmailErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingChannelEntry: freezed == messagingChannelEntry
          ? _self.messagingChannelEntry
          : messagingChannelEntry // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
      goToConversation: freezed == goToConversation
          ? _self.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      goToNewConversation: freezed == goToNewConversation
          ? _self.goToNewConversation
          : goToNewConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
    ));
  }

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get contact {
    if (_self.contact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_self.contact!, (value) {
      return _then(_self.copyWith(contact: value));
    });
  }

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get savedContact {
    if (_self.savedContact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_self.savedContact!, (value) {
      return _then(_self.copyWith(savedContact: value));
    });
  }

  /// Create a copy of ContactState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelEntryCopyWith<$Res>? get messagingChannelEntry {
    if (_self.messagingChannelEntry == null) {
      return null;
    }

    return $MessagingChannelEntryCopyWith<$Res>(_self.messagingChannelEntry!,
        (value) {
      return _then(_self.copyWith(messagingChannelEntry: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ContactState _$ContactStateFromJson(Map json) => $checkedCreate(
      '_ContactState',
      json,
      ($checkedConvert) {
        final val = _ContactState(
          contact: $checkedConvert(
              'contact',
              (v) => v == null
                  ? null
                  : Contact.fromJson(Map<String, dynamic>.from(v as Map))),
          savedContact: $checkedConvert(
              'savedContact',
              (v) => v == null
                  ? null
                  : Contact.fromJson(Map<String, dynamic>.from(v as Map))),
          isLoading: $checkedConvert('isLoading', (v) => v as bool? ?? true),
          isEditing: $checkedConvert('isEditing', (v) => v as bool? ?? false),
          showFirstNameErrorText: $checkedConvert(
              'showFirstNameErrorText', (v) => v as bool? ?? false),
          showLastNameErrorText: $checkedConvert(
              'showLastNameErrorText', (v) => v as bool? ?? false),
          showPhoneNumberErrorText: $checkedConvert(
              'showPhoneNumberErrorText', (v) => v as bool? ?? false),
          showEmailErrorText:
              $checkedConvert('showEmailErrorText', (v) => v as bool? ?? false),
          messagingChannelEntry: $checkedConvert(
              'messagingChannelEntry',
              (v) => v == null
                  ? null
                  : MessagingChannelEntry.fromJson(
                      Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$ContactStateToJson(_ContactState instance) =>
    <String, dynamic>{
      if (instance.contact?.toJson() case final value?) 'contact': value,
      if (instance.savedContact?.toJson() case final value?)
        'savedContact': value,
      'isLoading': instance.isLoading,
      'isEditing': instance.isEditing,
      'showFirstNameErrorText': instance.showFirstNameErrorText,
      'showLastNameErrorText': instance.showLastNameErrorText,
      'showPhoneNumberErrorText': instance.showPhoneNumberErrorText,
      'showEmailErrorText': instance.showEmailErrorText,
      if (instance.messagingChannelEntry?.toJson() case final value?)
        'messagingChannelEntry': value,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'channels_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChannelsState {
  dynamic get isLoadingChannels;
  @JsonKey(ignore: true)
  UiEvent<bool>? get isSelectedChannelChanged;
  MessagingChannelsResponse? get messagingChannels;
  MessagingChannelEntry? get selectedChannel;

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChannelsStateCopyWith<ChannelsState> get copyWith =>
      _$ChannelsStateCopyWithImpl<ChannelsState>(
          this as ChannelsState, _$identity);

  /// Serializes this ChannelsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChannelsState &&
            const DeepCollectionEquality()
                .equals(other.isLoadingChannels, isLoadingChannels) &&
            (identical(
                    other.isSelectedChannelChanged, isSelectedChannelChanged) ||
                other.isSelectedChannelChanged == isSelectedChannelChanged) &&
            (identical(other.messagingChannels, messagingChannels) ||
                other.messagingChannels == messagingChannels) &&
            (identical(other.selectedChannel, selectedChannel) ||
                other.selectedChannel == selectedChannel));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(isLoadingChannels),
      isSelectedChannelChanged,
      messagingChannels,
      selectedChannel);

  @override
  String toString() {
    return 'ChannelsState(isLoadingChannels: $isLoadingChannels, isSelectedChannelChanged: $isSelectedChannelChanged, messagingChannels: $messagingChannels, selectedChannel: $selectedChannel)';
  }
}

/// @nodoc
abstract mixin class $ChannelsStateCopyWith<$Res> {
  factory $ChannelsStateCopyWith(
          ChannelsState value, $Res Function(ChannelsState) _then) =
      _$ChannelsStateCopyWithImpl;
  @useResult
  $Res call(
      {dynamic isLoadingChannels,
      @JsonKey(ignore: true) UiEvent<bool>? isSelectedChannelChanged,
      MessagingChannelsResponse? messagingChannels,
      MessagingChannelEntry? selectedChannel});

  $MessagingChannelsResponseCopyWith<$Res>? get messagingChannels;
  $MessagingChannelEntryCopyWith<$Res>? get selectedChannel;
}

/// @nodoc
class _$ChannelsStateCopyWithImpl<$Res>
    implements $ChannelsStateCopyWith<$Res> {
  _$ChannelsStateCopyWithImpl(this._self, this._then);

  final ChannelsState _self;
  final $Res Function(ChannelsState) _then;

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingChannels = freezed,
    Object? isSelectedChannelChanged = freezed,
    Object? messagingChannels = freezed,
    Object? selectedChannel = freezed,
  }) {
    return _then(_self.copyWith(
      isLoadingChannels: freezed == isLoadingChannels
          ? _self.isLoadingChannels
          : isLoadingChannels // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSelectedChannelChanged: freezed == isSelectedChannelChanged
          ? _self.isSelectedChannelChanged
          : isSelectedChannelChanged // ignore: cast_nullable_to_non_nullable
              as UiEvent<bool>?,
      messagingChannels: freezed == messagingChannels
          ? _self.messagingChannels
          : messagingChannels // ignore: cast_nullable_to_non_nullable
              as MessagingChannelsResponse?,
      selectedChannel: freezed == selectedChannel
          ? _self.selectedChannel
          : selectedChannel // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
    ));
  }

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelsResponseCopyWith<$Res>? get messagingChannels {
    if (_self.messagingChannels == null) {
      return null;
    }

    return $MessagingChannelsResponseCopyWith<$Res>(_self.messagingChannels!,
        (value) {
      return _then(_self.copyWith(messagingChannels: value));
    });
  }

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelEntryCopyWith<$Res>? get selectedChannel {
    if (_self.selectedChannel == null) {
      return null;
    }

    return $MessagingChannelEntryCopyWith<$Res>(_self.selectedChannel!,
        (value) {
      return _then(_self.copyWith(selectedChannel: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ChannelsState].
extension ChannelsStatePatterns on ChannelsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ChannelsState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChannelsState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ChannelsState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelsState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ChannelsState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelsState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            dynamic isLoadingChannels,
            @JsonKey(ignore: true) UiEvent<bool>? isSelectedChannelChanged,
            MessagingChannelsResponse? messagingChannels,
            MessagingChannelEntry? selectedChannel)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChannelsState() when $default != null:
        return $default(_that.isLoadingChannels, _that.isSelectedChannelChanged,
            _that.messagingChannels, _that.selectedChannel);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            dynamic isLoadingChannels,
            @JsonKey(ignore: true) UiEvent<bool>? isSelectedChannelChanged,
            MessagingChannelsResponse? messagingChannels,
            MessagingChannelEntry? selectedChannel)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelsState():
        return $default(_that.isLoadingChannels, _that.isSelectedChannelChanged,
            _that.messagingChannels, _that.selectedChannel);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            dynamic isLoadingChannels,
            @JsonKey(ignore: true) UiEvent<bool>? isSelectedChannelChanged,
            MessagingChannelsResponse? messagingChannels,
            MessagingChannelEntry? selectedChannel)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChannelsState() when $default != null:
        return $default(_that.isLoadingChannels, _that.isSelectedChannelChanged,
            _that.messagingChannels, _that.selectedChannel);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ChannelsState implements ChannelsState {
  const _ChannelsState(
      {this.isLoadingChannels = true,
      @JsonKey(ignore: true) this.isSelectedChannelChanged,
      this.messagingChannels = null,
      this.selectedChannel = null});
  factory _ChannelsState.fromJson(Map<String, dynamic> json) =>
      _$ChannelsStateFromJson(json);

  @override
  @JsonKey()
  final dynamic isLoadingChannels;
  @override
  @JsonKey(ignore: true)
  final UiEvent<bool>? isSelectedChannelChanged;
  @override
  @JsonKey()
  final MessagingChannelsResponse? messagingChannels;
  @override
  @JsonKey()
  final MessagingChannelEntry? selectedChannel;

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ChannelsStateCopyWith<_ChannelsState> get copyWith =>
      __$ChannelsStateCopyWithImpl<_ChannelsState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ChannelsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChannelsState &&
            const DeepCollectionEquality()
                .equals(other.isLoadingChannels, isLoadingChannels) &&
            (identical(
                    other.isSelectedChannelChanged, isSelectedChannelChanged) ||
                other.isSelectedChannelChanged == isSelectedChannelChanged) &&
            (identical(other.messagingChannels, messagingChannels) ||
                other.messagingChannels == messagingChannels) &&
            (identical(other.selectedChannel, selectedChannel) ||
                other.selectedChannel == selectedChannel));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(isLoadingChannels),
      isSelectedChannelChanged,
      messagingChannels,
      selectedChannel);

  @override
  String toString() {
    return 'ChannelsState(isLoadingChannels: $isLoadingChannels, isSelectedChannelChanged: $isSelectedChannelChanged, messagingChannels: $messagingChannels, selectedChannel: $selectedChannel)';
  }
}

/// @nodoc
abstract mixin class _$ChannelsStateCopyWith<$Res>
    implements $ChannelsStateCopyWith<$Res> {
  factory _$ChannelsStateCopyWith(
          _ChannelsState value, $Res Function(_ChannelsState) _then) =
      __$ChannelsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {dynamic isLoadingChannels,
      @JsonKey(ignore: true) UiEvent<bool>? isSelectedChannelChanged,
      MessagingChannelsResponse? messagingChannels,
      MessagingChannelEntry? selectedChannel});

  @override
  $MessagingChannelsResponseCopyWith<$Res>? get messagingChannels;
  @override
  $MessagingChannelEntryCopyWith<$Res>? get selectedChannel;
}

/// @nodoc
class __$ChannelsStateCopyWithImpl<$Res>
    implements _$ChannelsStateCopyWith<$Res> {
  __$ChannelsStateCopyWithImpl(this._self, this._then);

  final _ChannelsState _self;
  final $Res Function(_ChannelsState) _then;

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoadingChannels = freezed,
    Object? isSelectedChannelChanged = freezed,
    Object? messagingChannels = freezed,
    Object? selectedChannel = freezed,
  }) {
    return _then(_ChannelsState(
      isLoadingChannels: freezed == isLoadingChannels
          ? _self.isLoadingChannels
          : isLoadingChannels // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSelectedChannelChanged: freezed == isSelectedChannelChanged
          ? _self.isSelectedChannelChanged
          : isSelectedChannelChanged // ignore: cast_nullable_to_non_nullable
              as UiEvent<bool>?,
      messagingChannels: freezed == messagingChannels
          ? _self.messagingChannels
          : messagingChannels // ignore: cast_nullable_to_non_nullable
              as MessagingChannelsResponse?,
      selectedChannel: freezed == selectedChannel
          ? _self.selectedChannel
          : selectedChannel // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
    ));
  }

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelsResponseCopyWith<$Res>? get messagingChannels {
    if (_self.messagingChannels == null) {
      return null;
    }

    return $MessagingChannelsResponseCopyWith<$Res>(_self.messagingChannels!,
        (value) {
      return _then(_self.copyWith(messagingChannels: value));
    });
  }

  /// Create a copy of ChannelsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelEntryCopyWith<$Res>? get selectedChannel {
    if (_self.selectedChannel == null) {
      return null;
    }

    return $MessagingChannelEntryCopyWith<$Res>(_self.selectedChannel!,
        (value) {
      return _then(_self.copyWith(selectedChannel: value));
    });
  }
}

// dart format on

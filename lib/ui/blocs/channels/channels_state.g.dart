// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'channels_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChannelsState _$ChannelsStateFromJson(Map json) => $checkedCreate(
      '_ChannelsState',
      json,
      ($checkedConvert) {
        final val = _ChannelsState(
          isLoadingChannels:
              $checkedConvert('isLoadingChannels', (v) => v ?? true),
          messagingChannels: $checkedConvert(
              'messagingChannels',
              (v) => v == null
                  ? null
                  : MessagingChannelsResponse.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          selectedChannel: $checkedConvert(
              'selectedChannel',
              (v) => v == null
                  ? null
                  : MessagingChannelEntry.fromJson(
                      Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$ChannelsStateToJson(_ChannelsState instance) =>
    <String, dynamic>{
      if (instance.isLoadingChannels case final value?)
        'isLoadingChannels': value,
      if (instance.messagingChannels?.toJson() case final value?)
        'messagingChannels': value,
      if (instance.selectedChannel?.toJson() case final value?)
        'selectedChannel': value,
    };

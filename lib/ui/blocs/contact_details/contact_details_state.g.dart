// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_details_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ContactDetailsState _$ContactDetailsStateFromJson(Map json) => $checkedCreate(
      '_ContactDetailsState',
      json,
      ($checkedConvert) {
        final val = _ContactDetailsState(
          contact: $checkedConvert(
              'contact',
              (v) => v == null
                  ? null
                  : Contact.fromJson(Map<String, dynamic>.from(v as Map))),
          conversationUserName:
              $checkedConvert('conversationUserName', (v) => v as String?),
          messagingEndUser: $checkedConvert(
              'messagingEndUser',
              (v) => v == null
                  ? null
                  : MessagingEndUser.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          isLoading: $checkedConvert('isLoading', (v) => v as bool? ?? true),
        );
        return val;
      },
    );

Map<String, dynamic> _$ContactDetailsStateToJson(
        _ContactDetailsState instance) =>
    <String, dynamic>{
      if (instance.contact?.toJson() case final value?) 'contact': value,
      if (instance.conversationUserName case final value?)
        'conversationUserName': value,
      if (instance.messagingEndUser?.toJson() case final value?)
        'messagingEndUser': value,
      'isLoading': instance.isLoading,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contacts_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContactsState {
  @JsonKey(ignore: true)
  Map<SfId, Contact> get contacts;
  @JsonKey(ignore: true)
  Map<SfId, SfId> get meuContactAssociations;

  /// Create a copy of ContactsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactsStateCopyWith<ContactsState> get copyWith =>
      _$ContactsStateCopyWithImpl<ContactsState>(
          this as ContactsState, _$identity);

  /// Serializes this ContactsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactsState &&
            const DeepCollectionEquality().equals(other.contacts, contacts) &&
            const DeepCollectionEquality()
                .equals(other.meuContactAssociations, meuContactAssociations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(contacts),
      const DeepCollectionEquality().hash(meuContactAssociations));

  @override
  String toString() {
    return 'ContactsState(contacts: $contacts, meuContactAssociations: $meuContactAssociations)';
  }
}

/// @nodoc
abstract mixin class $ContactsStateCopyWith<$Res> {
  factory $ContactsStateCopyWith(
          ContactsState value, $Res Function(ContactsState) _then) =
      _$ContactsStateCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(ignore: true) Map<SfId, Contact> contacts,
      @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations});
}

/// @nodoc
class _$ContactsStateCopyWithImpl<$Res>
    implements $ContactsStateCopyWith<$Res> {
  _$ContactsStateCopyWithImpl(this._self, this._then);

  final ContactsState _self;
  final $Res Function(ContactsState) _then;

  /// Create a copy of ContactsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
    Object? meuContactAssociations = null,
  }) {
    return _then(_self.copyWith(
      contacts: null == contacts
          ? _self.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _self.meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactsState].
extension ContactsStatePatterns on ContactsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactsState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactsState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactsState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactsState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactsState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactsState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@JsonKey(ignore: true) Map<SfId, Contact> contacts,
            @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactsState() when $default != null:
        return $default(_that.contacts, _that.meuContactAssociations);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@JsonKey(ignore: true) Map<SfId, Contact> contacts,
            @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactsState():
        return $default(_that.contacts, _that.meuContactAssociations);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@JsonKey(ignore: true) Map<SfId, Contact> contacts,
            @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactsState() when $default != null:
        return $default(_that.contacts, _that.meuContactAssociations);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactsState extends ContactsState {
  const _ContactsState(
      {@JsonKey(ignore: true) final Map<SfId, Contact> contacts = const {},
      @JsonKey(ignore: true)
      final Map<SfId, SfId> meuContactAssociations = const {}})
      : _contacts = contacts,
        _meuContactAssociations = meuContactAssociations,
        super._();
  factory _ContactsState.fromJson(Map<String, dynamic> json) =>
      _$ContactsStateFromJson(json);

  final Map<SfId, Contact> _contacts;
  @override
  @JsonKey(ignore: true)
  Map<SfId, Contact> get contacts {
    if (_contacts is EqualUnmodifiableMapView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_contacts);
  }

  final Map<SfId, SfId> _meuContactAssociations;
  @override
  @JsonKey(ignore: true)
  Map<SfId, SfId> get meuContactAssociations {
    if (_meuContactAssociations is EqualUnmodifiableMapView)
      return _meuContactAssociations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_meuContactAssociations);
  }

  /// Create a copy of ContactsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactsStateCopyWith<_ContactsState> get copyWith =>
      __$ContactsStateCopyWithImpl<_ContactsState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactsState &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            const DeepCollectionEquality().equals(
                other._meuContactAssociations, _meuContactAssociations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_contacts),
      const DeepCollectionEquality().hash(_meuContactAssociations));

  @override
  String toString() {
    return 'ContactsState(contacts: $contacts, meuContactAssociations: $meuContactAssociations)';
  }
}

/// @nodoc
abstract mixin class _$ContactsStateCopyWith<$Res>
    implements $ContactsStateCopyWith<$Res> {
  factory _$ContactsStateCopyWith(
          _ContactsState value, $Res Function(_ContactsState) _then) =
      __$ContactsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(ignore: true) Map<SfId, Contact> contacts,
      @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations});
}

/// @nodoc
class __$ContactsStateCopyWithImpl<$Res>
    implements _$ContactsStateCopyWith<$Res> {
  __$ContactsStateCopyWithImpl(this._self, this._then);

  final _ContactsState _self;
  final $Res Function(_ContactsState) _then;

  /// Create a copy of ContactsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contacts = null,
    Object? meuContactAssociations = null,
  }) {
    return _then(_ContactsState(
      contacts: null == contacts
          ? _self._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _self._meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
    ));
  }
}

// dart format on

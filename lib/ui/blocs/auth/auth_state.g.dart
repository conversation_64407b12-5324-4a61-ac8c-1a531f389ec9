// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AuthState _$AuthStateFromJson(Map json) => $checkedCreate(
      '_AuthState',
      json,
      ($checkedConvert) {
        final val = _AuthState(
          status: $checkedConvert(
              'status',
              (v) =>
                  $enumDecodeNullable(_$AuthStatusEnumMap, v) ??
                  AuthStatus.loggedOut),
          appError: $checkedConvert(
              'appError',
              (v) => v == null
                  ? null
                  : AppError.fromJson(Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$AuthStateToJson(_AuthState instance) =>
    <String, dynamic>{
      'status': _$AuthStatusEnumMap[instance.status]!,
      if (instance.appError?.toJson() case final value?) 'appError': value,
    };

const _$AuthStatusEnumMap = {
  AuthStatus.loggedOut: 'loggedOut',
  AuthStatus.isLoggingIn: 'isLoggingIn',
  AuthStatus.sfLoggedIn: 'sfLoggedIn',
  AuthStatus.loggedIn: 'loggedIn',
  AuthStatus.isLoggingOut: 'isLoggingOut',
};

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AuthState {
  AuthStatus get status;
  AppError? get appError;
  @JsonKey(ignore: true)
  UiEvent<SalesforceConfig>? get oauth;
  @JsonKey(ignore: true)
  UiEvent<Nothing>? get logIn;
  @JsonKey(ignore: true)
  UiEvent<Nothing>? get logOut;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AuthStateCopyWith<AuthState> get copyWith =>
      _$AuthStateCopyWithImpl<AuthState>(this as AuthState, _$identity);

  /// Serializes this AuthState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AuthState &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.appError, appError) ||
                other.appError == appError) &&
            (identical(other.oauth, oauth) || other.oauth == oauth) &&
            (identical(other.logIn, logIn) || other.logIn == logIn) &&
            (identical(other.logOut, logOut) || other.logOut == logOut));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, status, appError, oauth, logIn, logOut);

  @override
  String toString() {
    return 'AuthState(status: $status, appError: $appError, oauth: $oauth, logIn: $logIn, logOut: $logOut)';
  }
}

/// @nodoc
abstract mixin class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) _then) =
      _$AuthStateCopyWithImpl;
  @useResult
  $Res call(
      {AuthStatus status,
      AppError? appError,
      @JsonKey(ignore: true) UiEvent<SalesforceConfig>? oauth,
      @JsonKey(ignore: true) UiEvent<Nothing>? logIn,
      @JsonKey(ignore: true) UiEvent<Nothing>? logOut});

  $AppErrorCopyWith<$Res>? get appError;
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res> implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._self, this._then);

  final AuthState _self;
  final $Res Function(AuthState) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? appError = freezed,
    Object? oauth = freezed,
    Object? logIn = freezed,
    Object? logOut = freezed,
  }) {
    return _then(_self.copyWith(
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as AuthStatus,
      appError: freezed == appError
          ? _self.appError
          : appError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      oauth: freezed == oauth
          ? _self.oauth
          : oauth // ignore: cast_nullable_to_non_nullable
              as UiEvent<SalesforceConfig>?,
      logIn: freezed == logIn
          ? _self.logIn
          : logIn // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      logOut: freezed == logOut
          ? _self.logOut
          : logOut // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get appError {
    if (_self.appError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_self.appError!, (value) {
      return _then(_self.copyWith(appError: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AuthState].
extension AuthStatePatterns on AuthState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AuthState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AuthState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AuthState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AuthState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AuthState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AuthState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            AuthStatus status,
            AppError? appError,
            @JsonKey(ignore: true) UiEvent<SalesforceConfig>? oauth,
            @JsonKey(ignore: true) UiEvent<Nothing>? logIn,
            @JsonKey(ignore: true) UiEvent<Nothing>? logOut)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AuthState() when $default != null:
        return $default(_that.status, _that.appError, _that.oauth, _that.logIn,
            _that.logOut);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            AuthStatus status,
            AppError? appError,
            @JsonKey(ignore: true) UiEvent<SalesforceConfig>? oauth,
            @JsonKey(ignore: true) UiEvent<Nothing>? logIn,
            @JsonKey(ignore: true) UiEvent<Nothing>? logOut)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AuthState():
        return $default(_that.status, _that.appError, _that.oauth, _that.logIn,
            _that.logOut);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            AuthStatus status,
            AppError? appError,
            @JsonKey(ignore: true) UiEvent<SalesforceConfig>? oauth,
            @JsonKey(ignore: true) UiEvent<Nothing>? logIn,
            @JsonKey(ignore: true) UiEvent<Nothing>? logOut)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AuthState() when $default != null:
        return $default(_that.status, _that.appError, _that.oauth, _that.logIn,
            _that.logOut);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AuthState extends AuthState {
  const _AuthState(
      {this.status = AuthStatus.loggedOut,
      this.appError,
      @JsonKey(ignore: true) this.oauth,
      @JsonKey(ignore: true) this.logIn,
      @JsonKey(ignore: true) this.logOut})
      : super._();
  factory _AuthState.fromJson(Map<String, dynamic> json) =>
      _$AuthStateFromJson(json);

  @override
  @JsonKey()
  final AuthStatus status;
  @override
  final AppError? appError;
  @override
  @JsonKey(ignore: true)
  final UiEvent<SalesforceConfig>? oauth;
  @override
  @JsonKey(ignore: true)
  final UiEvent<Nothing>? logIn;
  @override
  @JsonKey(ignore: true)
  final UiEvent<Nothing>? logOut;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AuthStateCopyWith<_AuthState> get copyWith =>
      __$AuthStateCopyWithImpl<_AuthState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AuthStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AuthState &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.appError, appError) ||
                other.appError == appError) &&
            (identical(other.oauth, oauth) || other.oauth == oauth) &&
            (identical(other.logIn, logIn) || other.logIn == logIn) &&
            (identical(other.logOut, logOut) || other.logOut == logOut));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, status, appError, oauth, logIn, logOut);

  @override
  String toString() {
    return 'AuthState(status: $status, appError: $appError, oauth: $oauth, logIn: $logIn, logOut: $logOut)';
  }
}

/// @nodoc
abstract mixin class _$AuthStateCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$AuthStateCopyWith(
          _AuthState value, $Res Function(_AuthState) _then) =
      __$AuthStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {AuthStatus status,
      AppError? appError,
      @JsonKey(ignore: true) UiEvent<SalesforceConfig>? oauth,
      @JsonKey(ignore: true) UiEvent<Nothing>? logIn,
      @JsonKey(ignore: true) UiEvent<Nothing>? logOut});

  @override
  $AppErrorCopyWith<$Res>? get appError;
}

/// @nodoc
class __$AuthStateCopyWithImpl<$Res> implements _$AuthStateCopyWith<$Res> {
  __$AuthStateCopyWithImpl(this._self, this._then);

  final _AuthState _self;
  final $Res Function(_AuthState) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? status = null,
    Object? appError = freezed,
    Object? oauth = freezed,
    Object? logIn = freezed,
    Object? logOut = freezed,
  }) {
    return _then(_AuthState(
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as AuthStatus,
      appError: freezed == appError
          ? _self.appError
          : appError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      oauth: freezed == oauth
          ? _self.oauth
          : oauth // ignore: cast_nullable_to_non_nullable
              as UiEvent<SalesforceConfig>?,
      logIn: freezed == logIn
          ? _self.logIn
          : logIn // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      logOut: freezed == logOut
          ? _self.logOut
          : logOut // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get appError {
    if (_self.appError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_self.appError!, (value) {
      return _then(_self.copyWith(appError: value));
    });
  }
}

// dart format on

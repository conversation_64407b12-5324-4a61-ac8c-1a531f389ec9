// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'image_size_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ImageSizeState {
  Map<String, ImageSize> get imageSizes;

  /// Create a copy of ImageSizeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ImageSizeStateCopyWith<ImageSizeState> get copyWith =>
      _$ImageSizeStateCopyWithImpl<ImageSizeState>(
          this as ImageSizeState, _$identity);

  /// Serializes this ImageSizeState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ImageSizeState &&
            const DeepCollectionEquality()
                .equals(other.imageSizes, imageSizes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(imageSizes));

  @override
  String toString() {
    return 'ImageSizeState(imageSizes: $imageSizes)';
  }
}

/// @nodoc
abstract mixin class $ImageSizeStateCopyWith<$Res> {
  factory $ImageSizeStateCopyWith(
          ImageSizeState value, $Res Function(ImageSizeState) _then) =
      _$ImageSizeStateCopyWithImpl;
  @useResult
  $Res call({Map<String, ImageSize> imageSizes});
}

/// @nodoc
class _$ImageSizeStateCopyWithImpl<$Res>
    implements $ImageSizeStateCopyWith<$Res> {
  _$ImageSizeStateCopyWithImpl(this._self, this._then);

  final ImageSizeState _self;
  final $Res Function(ImageSizeState) _then;

  /// Create a copy of ImageSizeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageSizes = null,
  }) {
    return _then(_self.copyWith(
      imageSizes: null == imageSizes
          ? _self.imageSizes
          : imageSizes // ignore: cast_nullable_to_non_nullable
              as Map<String, ImageSize>,
    ));
  }
}

/// Adds pattern-matching-related methods to [ImageSizeState].
extension ImageSizeStatePatterns on ImageSizeState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ImageSizeState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ImageSizeState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ImageSizeState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ImageSizeState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ImageSizeState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ImageSizeState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(Map<String, ImageSize> imageSizes)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ImageSizeState() when $default != null:
        return $default(_that.imageSizes);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(Map<String, ImageSize> imageSizes) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ImageSizeState():
        return $default(_that.imageSizes);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(Map<String, ImageSize> imageSizes)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ImageSizeState() when $default != null:
        return $default(_that.imageSizes);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ImageSizeState implements ImageSizeState {
  const _ImageSizeState(
      {final Map<String, ImageSize> imageSizes = const <String, ImageSize>{}})
      : _imageSizes = imageSizes;
  factory _ImageSizeState.fromJson(Map<String, dynamic> json) =>
      _$ImageSizeStateFromJson(json);

  final Map<String, ImageSize> _imageSizes;
  @override
  @JsonKey()
  Map<String, ImageSize> get imageSizes {
    if (_imageSizes is EqualUnmodifiableMapView) return _imageSizes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_imageSizes);
  }

  /// Create a copy of ImageSizeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ImageSizeStateCopyWith<_ImageSizeState> get copyWith =>
      __$ImageSizeStateCopyWithImpl<_ImageSizeState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ImageSizeStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ImageSizeState &&
            const DeepCollectionEquality()
                .equals(other._imageSizes, _imageSizes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_imageSizes));

  @override
  String toString() {
    return 'ImageSizeState(imageSizes: $imageSizes)';
  }
}

/// @nodoc
abstract mixin class _$ImageSizeStateCopyWith<$Res>
    implements $ImageSizeStateCopyWith<$Res> {
  factory _$ImageSizeStateCopyWith(
          _ImageSizeState value, $Res Function(_ImageSizeState) _then) =
      __$ImageSizeStateCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, ImageSize> imageSizes});
}

/// @nodoc
class __$ImageSizeStateCopyWithImpl<$Res>
    implements _$ImageSizeStateCopyWith<$Res> {
  __$ImageSizeStateCopyWithImpl(this._self, this._then);

  final _ImageSizeState _self;
  final $Res Function(_ImageSizeState) _then;

  /// Create a copy of ImageSizeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? imageSizes = null,
  }) {
    return _then(_ImageSizeState(
      imageSizes: null == imageSizes
          ? _self._imageSizes
          : imageSizes // ignore: cast_nullable_to_non_nullable
              as Map<String, ImageSize>,
    ));
  }
}

// dart format on

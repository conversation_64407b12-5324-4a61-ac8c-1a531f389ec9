// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_size.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ImageSize _$ImageSizeFromJson(Map json) => $checkedCreate(
      '_ImageSize',
      json,
      ($checkedConvert) {
        final val = _ImageSize(
          height: $checkedConvert('height', (v) => (v as num?)?.toDouble()),
          width: $checkedConvert('width', (v) => (v as num?)?.toDouble()),
        );
        return val;
      },
    );

Map<String, dynamic> _$ImageSizeToJson(_ImageSize instance) =>
    <String, dynamic>{
      if (instance.height case final value?) 'height': value,
      if (instance.width case final value?) 'width': value,
    };

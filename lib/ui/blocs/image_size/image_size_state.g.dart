// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_size_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ImageSizeState _$ImageSizeStateFromJson(Map json) => $checkedCreate(
      '_ImageSizeState',
      json,
      ($checkedConvert) {
        final val = _ImageSizeState(
          imageSizes: $checkedConvert(
              'imageSizes',
              (v) =>
                  (v as Map?)?.map(
                    (k, e) => MapEntry(
                        k as String,
                        ImageSize.fromJson(
                            Map<String, dynamic>.from(e as Map))),
                  ) ??
                  const <String, ImageSize>{}),
        );
        return val;
      },
    );

Map<String, dynamic> _$ImageSizeStateToJson(_ImageSizeState instance) =>
    <String, dynamic>{
      'imageSizes': instance.imageSizes.map((k, e) => MapEntry(k, e.toJson())),
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChatState {
  @Deprecated('legacy support')
  @JsonKey(ignore: true)
  Conversation? get conversation;
  @SavedContactByMeuConverter()
  SavedContactByMeu? get savedContact;
  bool? get canTransfer;

  /// this is to support our legacy LakConversation that doesn't prompt the bloc updates correctly ... so setting 'toggle' value will force the ChatScreen to update // TODO: remove toggle
  bool get toggle;
  bool get isFetchingMessagingEndUserStatus;
  bool get isFetchingMessagingDefinitions;
  MessagingEndUserStatusResponse? get messagingEndUserStatus;
  String get messageText;
  @JsonKey(ignore: true)
  List<File> get attachedFiles;
  MessagingDefinitionStatus get messagingDefinitionStatus;
  ChatFieldStatus get chatFieldStatus;
  SfId? get messagingEndUserId;
  MessagingChannelType? get messagingType;
  @JsonKey(ignore: true)
  UiEvent<Nothing>? get navigateToDefinitions;
  @JsonKey(ignore: true)
  UiEvent<MessagingException>? get messageDefinitionFailure;
  @JsonKey(ignore: true)
  UiEvent<SfId>? get goToConversation;
  QuickAction? get mainQuickAction;
  List<QuickAction>? get overflowQuickActions;
  @JsonKey(ignore: true)
  UiEvent<QuickActionException>? get quickActionException;

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChatStateCopyWith<ChatState> get copyWith =>
      _$ChatStateCopyWithImpl<ChatState>(this as ChatState, _$identity);

  /// Serializes this ChatState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChatState &&
            (identical(other.conversation, conversation) ||
                other.conversation == conversation) &&
            (identical(other.savedContact, savedContact) ||
                other.savedContact == savedContact) &&
            (identical(other.canTransfer, canTransfer) ||
                other.canTransfer == canTransfer) &&
            (identical(other.toggle, toggle) || other.toggle == toggle) &&
            (identical(other.isFetchingMessagingEndUserStatus,
                    isFetchingMessagingEndUserStatus) ||
                other.isFetchingMessagingEndUserStatus ==
                    isFetchingMessagingEndUserStatus) &&
            (identical(other.isFetchingMessagingDefinitions,
                    isFetchingMessagingDefinitions) ||
                other.isFetchingMessagingDefinitions ==
                    isFetchingMessagingDefinitions) &&
            (identical(other.messagingEndUserStatus, messagingEndUserStatus) ||
                other.messagingEndUserStatus == messagingEndUserStatus) &&
            (identical(other.messageText, messageText) ||
                other.messageText == messageText) &&
            const DeepCollectionEquality()
                .equals(other.attachedFiles, attachedFiles) &&
            (identical(other.messagingDefinitionStatus, messagingDefinitionStatus) ||
                other.messagingDefinitionStatus == messagingDefinitionStatus) &&
            (identical(other.chatFieldStatus, chatFieldStatus) ||
                other.chatFieldStatus == chatFieldStatus) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingType, messagingType) ||
                other.messagingType == messagingType) &&
            (identical(other.navigateToDefinitions, navigateToDefinitions) ||
                other.navigateToDefinitions == navigateToDefinitions) &&
            (identical(other.messageDefinitionFailure, messageDefinitionFailure) ||
                other.messageDefinitionFailure == messageDefinitionFailure) &&
            (identical(other.goToConversation, goToConversation) ||
                other.goToConversation == goToConversation) &&
            (identical(other.mainQuickAction, mainQuickAction) ||
                other.mainQuickAction == mainQuickAction) &&
            const DeepCollectionEquality()
                .equals(other.overflowQuickActions, overflowQuickActions) &&
            (identical(other.quickActionException, quickActionException) ||
                other.quickActionException == quickActionException));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        conversation,
        savedContact,
        canTransfer,
        toggle,
        isFetchingMessagingEndUserStatus,
        isFetchingMessagingDefinitions,
        messagingEndUserStatus,
        messageText,
        const DeepCollectionEquality().hash(attachedFiles),
        messagingDefinitionStatus,
        chatFieldStatus,
        messagingEndUserId,
        messagingType,
        navigateToDefinitions,
        messageDefinitionFailure,
        goToConversation,
        mainQuickAction,
        const DeepCollectionEquality().hash(overflowQuickActions),
        quickActionException
      ]);

  @override
  String toString() {
    return 'ChatState(conversation: $conversation, savedContact: $savedContact, canTransfer: $canTransfer, toggle: $toggle, isFetchingMessagingEndUserStatus: $isFetchingMessagingEndUserStatus, isFetchingMessagingDefinitions: $isFetchingMessagingDefinitions, messagingEndUserStatus: $messagingEndUserStatus, messageText: $messageText, attachedFiles: $attachedFiles, messagingDefinitionStatus: $messagingDefinitionStatus, chatFieldStatus: $chatFieldStatus, messagingEndUserId: $messagingEndUserId, messagingType: $messagingType, navigateToDefinitions: $navigateToDefinitions, messageDefinitionFailure: $messageDefinitionFailure, goToConversation: $goToConversation, mainQuickAction: $mainQuickAction, overflowQuickActions: $overflowQuickActions, quickActionException: $quickActionException)';
  }
}

/// @nodoc
abstract mixin class $ChatStateCopyWith<$Res> {
  factory $ChatStateCopyWith(ChatState value, $Res Function(ChatState) _then) =
      _$ChatStateCopyWithImpl;
  @useResult
  $Res call(
      {@Deprecated('legacy support')
      @JsonKey(ignore: true)
      Conversation? conversation,
      @SavedContactByMeuConverter() SavedContactByMeu? savedContact,
      bool? canTransfer,
      bool toggle,
      bool isFetchingMessagingEndUserStatus,
      bool isFetchingMessagingDefinitions,
      MessagingEndUserStatusResponse? messagingEndUserStatus,
      String messageText,
      @JsonKey(ignore: true) List<File> attachedFiles,
      MessagingDefinitionStatus messagingDefinitionStatus,
      ChatFieldStatus chatFieldStatus,
      SfId? messagingEndUserId,
      MessagingChannelType? messagingType,
      @JsonKey(ignore: true) UiEvent<Nothing>? navigateToDefinitions,
      @JsonKey(ignore: true)
      UiEvent<MessagingException>? messageDefinitionFailure,
      @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      @JsonKey(ignore: true)
      UiEvent<QuickActionException>? quickActionException});

  $MessagingEndUserStatusResponseCopyWith<$Res>? get messagingEndUserStatus;
  $MessagingDefinitionStatusCopyWith<$Res> get messagingDefinitionStatus;
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class _$ChatStateCopyWithImpl<$Res> implements $ChatStateCopyWith<$Res> {
  _$ChatStateCopyWithImpl(this._self, this._then);

  final ChatState _self;
  final $Res Function(ChatState) _then;

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversation = freezed,
    Object? savedContact = freezed,
    Object? canTransfer = freezed,
    Object? toggle = null,
    Object? isFetchingMessagingEndUserStatus = null,
    Object? isFetchingMessagingDefinitions = null,
    Object? messagingEndUserStatus = freezed,
    Object? messageText = null,
    Object? attachedFiles = null,
    Object? messagingDefinitionStatus = null,
    Object? chatFieldStatus = null,
    Object? messagingEndUserId = freezed,
    Object? messagingType = freezed,
    Object? navigateToDefinitions = freezed,
    Object? messageDefinitionFailure = freezed,
    Object? goToConversation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
  }) {
    return _then(_self.copyWith(
      conversation: freezed == conversation
          ? _self.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as Conversation?,
      savedContact: freezed == savedContact
          ? _self.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as SavedContactByMeu?,
      canTransfer: freezed == canTransfer
          ? _self.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool?,
      toggle: null == toggle
          ? _self.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingEndUserStatus: null == isFetchingMessagingEndUserStatus
          ? _self.isFetchingMessagingEndUserStatus
          : isFetchingMessagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingDefinitions: null == isFetchingMessagingDefinitions
          ? _self.isFetchingMessagingDefinitions
          : isFetchingMessagingDefinitions // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserStatus: freezed == messagingEndUserStatus
          ? _self.messagingEndUserStatus
          : messagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as MessagingEndUserStatusResponse?,
      messageText: null == messageText
          ? _self.messageText
          : messageText // ignore: cast_nullable_to_non_nullable
              as String,
      attachedFiles: null == attachedFiles
          ? _self.attachedFiles
          : attachedFiles // ignore: cast_nullable_to_non_nullable
              as List<File>,
      messagingDefinitionStatus: null == messagingDefinitionStatus
          ? _self.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus,
      chatFieldStatus: null == chatFieldStatus
          ? _self.chatFieldStatus
          : chatFieldStatus // ignore: cast_nullable_to_non_nullable
              as ChatFieldStatus,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingType: freezed == messagingType
          ? _self.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      navigateToDefinitions: freezed == navigateToDefinitions
          ? _self.navigateToDefinitions
          : navigateToDefinitions // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      messageDefinitionFailure: freezed == messageDefinitionFailure
          ? _self.messageDefinitionFailure
          : messageDefinitionFailure // ignore: cast_nullable_to_non_nullable
              as UiEvent<MessagingException>?,
      goToConversation: freezed == goToConversation
          ? _self.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      mainQuickAction: freezed == mainQuickAction
          ? _self.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _self.overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _self.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
    ));
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserStatusResponseCopyWith<$Res>? get messagingEndUserStatus {
    if (_self.messagingEndUserStatus == null) {
      return null;
    }

    return $MessagingEndUserStatusResponseCopyWith<$Res>(
        _self.messagingEndUserStatus!, (value) {
      return _then(_self.copyWith(messagingEndUserStatus: value));
    });
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionStatusCopyWith<$Res> get messagingDefinitionStatus {
    return $MessagingDefinitionStatusCopyWith<$Res>(
        _self.messagingDefinitionStatus, (value) {
      return _then(_self.copyWith(messagingDefinitionStatus: value));
    });
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_self.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUserId!, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuickActionCopyWith<$Res>? get mainQuickAction {
    if (_self.mainQuickAction == null) {
      return null;
    }

    return $QuickActionCopyWith<$Res>(_self.mainQuickAction!, (value) {
      return _then(_self.copyWith(mainQuickAction: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ChatState].
extension ChatStatePatterns on ChatState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ChatState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChatState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ChatState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChatState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ChatState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChatState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @Deprecated('legacy support')
            @JsonKey(ignore: true)
            Conversation? conversation,
            @SavedContactByMeuConverter() SavedContactByMeu? savedContact,
            bool? canTransfer,
            bool toggle,
            bool isFetchingMessagingEndUserStatus,
            bool isFetchingMessagingDefinitions,
            MessagingEndUserStatusResponse? messagingEndUserStatus,
            String messageText,
            @JsonKey(ignore: true) List<File> attachedFiles,
            MessagingDefinitionStatus messagingDefinitionStatus,
            ChatFieldStatus chatFieldStatus,
            SfId? messagingEndUserId,
            MessagingChannelType? messagingType,
            @JsonKey(ignore: true) UiEvent<Nothing>? navigateToDefinitions,
            @JsonKey(ignore: true)
            UiEvent<MessagingException>? messageDefinitionFailure,
            @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
            QuickAction? mainQuickAction,
            List<QuickAction>? overflowQuickActions,
            @JsonKey(ignore: true)
            UiEvent<QuickActionException>? quickActionException)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ChatState() when $default != null:
        return $default(
            _that.conversation,
            _that.savedContact,
            _that.canTransfer,
            _that.toggle,
            _that.isFetchingMessagingEndUserStatus,
            _that.isFetchingMessagingDefinitions,
            _that.messagingEndUserStatus,
            _that.messageText,
            _that.attachedFiles,
            _that.messagingDefinitionStatus,
            _that.chatFieldStatus,
            _that.messagingEndUserId,
            _that.messagingType,
            _that.navigateToDefinitions,
            _that.messageDefinitionFailure,
            _that.goToConversation,
            _that.mainQuickAction,
            _that.overflowQuickActions,
            _that.quickActionException);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @Deprecated('legacy support')
            @JsonKey(ignore: true)
            Conversation? conversation,
            @SavedContactByMeuConverter() SavedContactByMeu? savedContact,
            bool? canTransfer,
            bool toggle,
            bool isFetchingMessagingEndUserStatus,
            bool isFetchingMessagingDefinitions,
            MessagingEndUserStatusResponse? messagingEndUserStatus,
            String messageText,
            @JsonKey(ignore: true) List<File> attachedFiles,
            MessagingDefinitionStatus messagingDefinitionStatus,
            ChatFieldStatus chatFieldStatus,
            SfId? messagingEndUserId,
            MessagingChannelType? messagingType,
            @JsonKey(ignore: true) UiEvent<Nothing>? navigateToDefinitions,
            @JsonKey(ignore: true)
            UiEvent<MessagingException>? messageDefinitionFailure,
            @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
            QuickAction? mainQuickAction,
            List<QuickAction>? overflowQuickActions,
            @JsonKey(ignore: true)
            UiEvent<QuickActionException>? quickActionException)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChatState():
        return $default(
            _that.conversation,
            _that.savedContact,
            _that.canTransfer,
            _that.toggle,
            _that.isFetchingMessagingEndUserStatus,
            _that.isFetchingMessagingDefinitions,
            _that.messagingEndUserStatus,
            _that.messageText,
            _that.attachedFiles,
            _that.messagingDefinitionStatus,
            _that.chatFieldStatus,
            _that.messagingEndUserId,
            _that.messagingType,
            _that.navigateToDefinitions,
            _that.messageDefinitionFailure,
            _that.goToConversation,
            _that.mainQuickAction,
            _that.overflowQuickActions,
            _that.quickActionException);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @Deprecated('legacy support')
            @JsonKey(ignore: true)
            Conversation? conversation,
            @SavedContactByMeuConverter() SavedContactByMeu? savedContact,
            bool? canTransfer,
            bool toggle,
            bool isFetchingMessagingEndUserStatus,
            bool isFetchingMessagingDefinitions,
            MessagingEndUserStatusResponse? messagingEndUserStatus,
            String messageText,
            @JsonKey(ignore: true) List<File> attachedFiles,
            MessagingDefinitionStatus messagingDefinitionStatus,
            ChatFieldStatus chatFieldStatus,
            SfId? messagingEndUserId,
            MessagingChannelType? messagingType,
            @JsonKey(ignore: true) UiEvent<Nothing>? navigateToDefinitions,
            @JsonKey(ignore: true)
            UiEvent<MessagingException>? messageDefinitionFailure,
            @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
            QuickAction? mainQuickAction,
            List<QuickAction>? overflowQuickActions,
            @JsonKey(ignore: true)
            UiEvent<QuickActionException>? quickActionException)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ChatState() when $default != null:
        return $default(
            _that.conversation,
            _that.savedContact,
            _that.canTransfer,
            _that.toggle,
            _that.isFetchingMessagingEndUserStatus,
            _that.isFetchingMessagingDefinitions,
            _that.messagingEndUserStatus,
            _that.messageText,
            _that.attachedFiles,
            _that.messagingDefinitionStatus,
            _that.chatFieldStatus,
            _that.messagingEndUserId,
            _that.messagingType,
            _that.navigateToDefinitions,
            _that.messageDefinitionFailure,
            _that.goToConversation,
            _that.mainQuickAction,
            _that.overflowQuickActions,
            _that.quickActionException);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ChatState extends ChatState {
  const _ChatState(
      {@Deprecated('legacy support') @JsonKey(ignore: true) this.conversation,
      @SavedContactByMeuConverter() this.savedContact,
      this.canTransfer = false,
      this.toggle = false,
      this.isFetchingMessagingEndUserStatus = false,
      this.isFetchingMessagingDefinitions = false,
      this.messagingEndUserStatus,
      this.messageText = '',
      @JsonKey(ignore: true) final List<File> attachedFiles = const [],
      this.messagingDefinitionStatus =
          const MessagingDefinitionStatus(mustUseDefinition: true),
      this.chatFieldStatus = ChatFieldStatus.loading,
      this.messagingEndUserId,
      this.messagingType,
      @JsonKey(ignore: true) this.navigateToDefinitions,
      @JsonKey(ignore: true) this.messageDefinitionFailure,
      @JsonKey(ignore: true) this.goToConversation,
      this.mainQuickAction,
      final List<QuickAction>? overflowQuickActions,
      @JsonKey(ignore: true) this.quickActionException})
      : _attachedFiles = attachedFiles,
        _overflowQuickActions = overflowQuickActions,
        super._();
  factory _ChatState.fromJson(Map<String, dynamic> json) =>
      _$ChatStateFromJson(json);

  @override
  @Deprecated('legacy support')
  @JsonKey(ignore: true)
  final Conversation? conversation;
  @override
  @SavedContactByMeuConverter()
  final SavedContactByMeu? savedContact;
  @override
  @JsonKey()
  final bool? canTransfer;

  /// this is to support our legacy LakConversation that doesn't prompt the bloc updates correctly ... so setting 'toggle' value will force the ChatScreen to update // TODO: remove toggle
  @override
  @JsonKey()
  final bool toggle;
  @override
  @JsonKey()
  final bool isFetchingMessagingEndUserStatus;
  @override
  @JsonKey()
  final bool isFetchingMessagingDefinitions;
  @override
  final MessagingEndUserStatusResponse? messagingEndUserStatus;
  @override
  @JsonKey()
  final String messageText;
  final List<File> _attachedFiles;
  @override
  @JsonKey(ignore: true)
  List<File> get attachedFiles {
    if (_attachedFiles is EqualUnmodifiableListView) return _attachedFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachedFiles);
  }

  @override
  @JsonKey()
  final MessagingDefinitionStatus messagingDefinitionStatus;
  @override
  @JsonKey()
  final ChatFieldStatus chatFieldStatus;
  @override
  final SfId? messagingEndUserId;
  @override
  final MessagingChannelType? messagingType;
  @override
  @JsonKey(ignore: true)
  final UiEvent<Nothing>? navigateToDefinitions;
  @override
  @JsonKey(ignore: true)
  final UiEvent<MessagingException>? messageDefinitionFailure;
  @override
  @JsonKey(ignore: true)
  final UiEvent<SfId>? goToConversation;
  @override
  final QuickAction? mainQuickAction;
  final List<QuickAction>? _overflowQuickActions;
  @override
  List<QuickAction>? get overflowQuickActions {
    final value = _overflowQuickActions;
    if (value == null) return null;
    if (_overflowQuickActions is EqualUnmodifiableListView)
      return _overflowQuickActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(ignore: true)
  final UiEvent<QuickActionException>? quickActionException;

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ChatStateCopyWith<_ChatState> get copyWith =>
      __$ChatStateCopyWithImpl<_ChatState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ChatStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChatState &&
            (identical(other.conversation, conversation) ||
                other.conversation == conversation) &&
            (identical(other.savedContact, savedContact) ||
                other.savedContact == savedContact) &&
            (identical(other.canTransfer, canTransfer) ||
                other.canTransfer == canTransfer) &&
            (identical(other.toggle, toggle) || other.toggle == toggle) &&
            (identical(other.isFetchingMessagingEndUserStatus,
                    isFetchingMessagingEndUserStatus) ||
                other.isFetchingMessagingEndUserStatus ==
                    isFetchingMessagingEndUserStatus) &&
            (identical(other.isFetchingMessagingDefinitions,
                    isFetchingMessagingDefinitions) ||
                other.isFetchingMessagingDefinitions ==
                    isFetchingMessagingDefinitions) &&
            (identical(other.messagingEndUserStatus, messagingEndUserStatus) ||
                other.messagingEndUserStatus == messagingEndUserStatus) &&
            (identical(other.messageText, messageText) ||
                other.messageText == messageText) &&
            const DeepCollectionEquality()
                .equals(other._attachedFiles, _attachedFiles) &&
            (identical(other.messagingDefinitionStatus, messagingDefinitionStatus) ||
                other.messagingDefinitionStatus == messagingDefinitionStatus) &&
            (identical(other.chatFieldStatus, chatFieldStatus) ||
                other.chatFieldStatus == chatFieldStatus) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingType, messagingType) ||
                other.messagingType == messagingType) &&
            (identical(other.navigateToDefinitions, navigateToDefinitions) ||
                other.navigateToDefinitions == navigateToDefinitions) &&
            (identical(other.messageDefinitionFailure, messageDefinitionFailure) ||
                other.messageDefinitionFailure == messageDefinitionFailure) &&
            (identical(other.goToConversation, goToConversation) ||
                other.goToConversation == goToConversation) &&
            (identical(other.mainQuickAction, mainQuickAction) ||
                other.mainQuickAction == mainQuickAction) &&
            const DeepCollectionEquality()
                .equals(other._overflowQuickActions, _overflowQuickActions) &&
            (identical(other.quickActionException, quickActionException) ||
                other.quickActionException == quickActionException));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        conversation,
        savedContact,
        canTransfer,
        toggle,
        isFetchingMessagingEndUserStatus,
        isFetchingMessagingDefinitions,
        messagingEndUserStatus,
        messageText,
        const DeepCollectionEquality().hash(_attachedFiles),
        messagingDefinitionStatus,
        chatFieldStatus,
        messagingEndUserId,
        messagingType,
        navigateToDefinitions,
        messageDefinitionFailure,
        goToConversation,
        mainQuickAction,
        const DeepCollectionEquality().hash(_overflowQuickActions),
        quickActionException
      ]);

  @override
  String toString() {
    return 'ChatState(conversation: $conversation, savedContact: $savedContact, canTransfer: $canTransfer, toggle: $toggle, isFetchingMessagingEndUserStatus: $isFetchingMessagingEndUserStatus, isFetchingMessagingDefinitions: $isFetchingMessagingDefinitions, messagingEndUserStatus: $messagingEndUserStatus, messageText: $messageText, attachedFiles: $attachedFiles, messagingDefinitionStatus: $messagingDefinitionStatus, chatFieldStatus: $chatFieldStatus, messagingEndUserId: $messagingEndUserId, messagingType: $messagingType, navigateToDefinitions: $navigateToDefinitions, messageDefinitionFailure: $messageDefinitionFailure, goToConversation: $goToConversation, mainQuickAction: $mainQuickAction, overflowQuickActions: $overflowQuickActions, quickActionException: $quickActionException)';
  }
}

/// @nodoc
abstract mixin class _$ChatStateCopyWith<$Res>
    implements $ChatStateCopyWith<$Res> {
  factory _$ChatStateCopyWith(
          _ChatState value, $Res Function(_ChatState) _then) =
      __$ChatStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@Deprecated('legacy support')
      @JsonKey(ignore: true)
      Conversation? conversation,
      @SavedContactByMeuConverter() SavedContactByMeu? savedContact,
      bool? canTransfer,
      bool toggle,
      bool isFetchingMessagingEndUserStatus,
      bool isFetchingMessagingDefinitions,
      MessagingEndUserStatusResponse? messagingEndUserStatus,
      String messageText,
      @JsonKey(ignore: true) List<File> attachedFiles,
      MessagingDefinitionStatus messagingDefinitionStatus,
      ChatFieldStatus chatFieldStatus,
      SfId? messagingEndUserId,
      MessagingChannelType? messagingType,
      @JsonKey(ignore: true) UiEvent<Nothing>? navigateToDefinitions,
      @JsonKey(ignore: true)
      UiEvent<MessagingException>? messageDefinitionFailure,
      @JsonKey(ignore: true) UiEvent<SfId>? goToConversation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      @JsonKey(ignore: true)
      UiEvent<QuickActionException>? quickActionException});

  @override
  $MessagingEndUserStatusResponseCopyWith<$Res>? get messagingEndUserStatus;
  @override
  $MessagingDefinitionStatusCopyWith<$Res> get messagingDefinitionStatus;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  @override
  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class __$ChatStateCopyWithImpl<$Res> implements _$ChatStateCopyWith<$Res> {
  __$ChatStateCopyWithImpl(this._self, this._then);

  final _ChatState _self;
  final $Res Function(_ChatState) _then;

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? conversation = freezed,
    Object? savedContact = freezed,
    Object? canTransfer = freezed,
    Object? toggle = null,
    Object? isFetchingMessagingEndUserStatus = null,
    Object? isFetchingMessagingDefinitions = null,
    Object? messagingEndUserStatus = freezed,
    Object? messageText = null,
    Object? attachedFiles = null,
    Object? messagingDefinitionStatus = null,
    Object? chatFieldStatus = null,
    Object? messagingEndUserId = freezed,
    Object? messagingType = freezed,
    Object? navigateToDefinitions = freezed,
    Object? messageDefinitionFailure = freezed,
    Object? goToConversation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
  }) {
    return _then(_ChatState(
      conversation: freezed == conversation
          ? _self.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as Conversation?,
      savedContact: freezed == savedContact
          ? _self.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as SavedContactByMeu?,
      canTransfer: freezed == canTransfer
          ? _self.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool?,
      toggle: null == toggle
          ? _self.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingEndUserStatus: null == isFetchingMessagingEndUserStatus
          ? _self.isFetchingMessagingEndUserStatus
          : isFetchingMessagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingDefinitions: null == isFetchingMessagingDefinitions
          ? _self.isFetchingMessagingDefinitions
          : isFetchingMessagingDefinitions // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserStatus: freezed == messagingEndUserStatus
          ? _self.messagingEndUserStatus
          : messagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as MessagingEndUserStatusResponse?,
      messageText: null == messageText
          ? _self.messageText
          : messageText // ignore: cast_nullable_to_non_nullable
              as String,
      attachedFiles: null == attachedFiles
          ? _self._attachedFiles
          : attachedFiles // ignore: cast_nullable_to_non_nullable
              as List<File>,
      messagingDefinitionStatus: null == messagingDefinitionStatus
          ? _self.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus,
      chatFieldStatus: null == chatFieldStatus
          ? _self.chatFieldStatus
          : chatFieldStatus // ignore: cast_nullable_to_non_nullable
              as ChatFieldStatus,
      messagingEndUserId: freezed == messagingEndUserId
          ? _self.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingType: freezed == messagingType
          ? _self.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      navigateToDefinitions: freezed == navigateToDefinitions
          ? _self.navigateToDefinitions
          : navigateToDefinitions // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      messageDefinitionFailure: freezed == messageDefinitionFailure
          ? _self.messageDefinitionFailure
          : messageDefinitionFailure // ignore: cast_nullable_to_non_nullable
              as UiEvent<MessagingException>?,
      goToConversation: freezed == goToConversation
          ? _self.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      mainQuickAction: freezed == mainQuickAction
          ? _self.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _self._overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _self.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
    ));
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserStatusResponseCopyWith<$Res>? get messagingEndUserStatus {
    if (_self.messagingEndUserStatus == null) {
      return null;
    }

    return $MessagingEndUserStatusResponseCopyWith<$Res>(
        _self.messagingEndUserStatus!, (value) {
      return _then(_self.copyWith(messagingEndUserStatus: value));
    });
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionStatusCopyWith<$Res> get messagingDefinitionStatus {
    return $MessagingDefinitionStatusCopyWith<$Res>(
        _self.messagingDefinitionStatus, (value) {
      return _then(_self.copyWith(messagingDefinitionStatus: value));
    });
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_self.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUserId!, (value) {
      return _then(_self.copyWith(messagingEndUserId: value));
    });
  }

  /// Create a copy of ChatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuickActionCopyWith<$Res>? get mainQuickAction {
    if (_self.mainQuickAction == null) {
      return null;
    }

    return $QuickActionCopyWith<$Res>(_self.mainQuickAction!, (value) {
      return _then(_self.copyWith(mainQuickAction: value));
    });
  }
}

// dart format on

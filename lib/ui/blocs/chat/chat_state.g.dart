// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChatState _$ChatStateFromJson(Map json) => $checkedCreate(
      '_ChatState',
      json,
      ($checkedConvert) {
        final val = _ChatState(
          savedContact: $checkedConvert(
              'savedContact',
              (v) => const SavedContactByMeuConverter()
                  .fromJson(v as Map<String, dynamic>?)),
          canTransfer:
              $checkedConvert('canTransfer', (v) => v as bool? ?? false),
          toggle: $checkedConvert('toggle', (v) => v as bool? ?? false),
          isFetchingMessagingEndUserStatus: $checkedConvert(
              'isFetchingMessagingEndUserStatus', (v) => v as bool? ?? false),
          isFetchingMessagingDefinitions: $checkedConvert(
              'isFetchingMessagingDefinitions', (v) => v as bool? ?? false),
          messagingEndUserStatus: $checkedConvert(
              'messagingEndUserStatus',
              (v) => v == null
                  ? null
                  : MessagingEndUserStatusResponse.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          messageText:
              $checkedConvert('messageText', (v) => v as String? ?? ''),
          messagingDefinitionStatus: $checkedConvert(
              'messagingDefinitionStatus',
              (v) => v == null
                  ? const MessagingDefinitionStatus(mustUseDefinition: true)
                  : MessagingDefinitionStatus.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          chatFieldStatus: $checkedConvert(
              'chatFieldStatus',
              (v) =>
                  $enumDecodeNullable(_$ChatFieldStatusEnumMap, v) ??
                  ChatFieldStatus.loading),
          messagingEndUserId: $checkedConvert(
              'messagingEndUserId',
              (v) => v == null
                  ? null
                  : SfId.fromJson(Map<String, dynamic>.from(v as Map))),
          messagingType: $checkedConvert('messagingType',
              (v) => $enumDecodeNullable(_$MessagingChannelTypeEnumMap, v)),
          mainQuickAction: $checkedConvert(
              'mainQuickAction',
              (v) => v == null
                  ? null
                  : QuickAction.fromJson(Map<String, dynamic>.from(v as Map))),
          overflowQuickActions: $checkedConvert(
              'overflowQuickActions',
              (v) => (v as List<dynamic>?)
                  ?.map((e) =>
                      QuickAction.fromJson(Map<String, dynamic>.from(e as Map)))
                  .toList()),
        );
        return val;
      },
    );

Map<String, dynamic> _$ChatStateToJson(_ChatState instance) =>
    <String, dynamic>{
      if (const SavedContactByMeuConverter().toJson(instance.savedContact)
          case final value?)
        'savedContact': value,
      if (instance.canTransfer case final value?) 'canTransfer': value,
      'toggle': instance.toggle,
      'isFetchingMessagingEndUserStatus':
          instance.isFetchingMessagingEndUserStatus,
      'isFetchingMessagingDefinitions': instance.isFetchingMessagingDefinitions,
      if (instance.messagingEndUserStatus?.toJson() case final value?)
        'messagingEndUserStatus': value,
      'messageText': instance.messageText,
      'messagingDefinitionStatus': instance.messagingDefinitionStatus.toJson(),
      'chatFieldStatus': _$ChatFieldStatusEnumMap[instance.chatFieldStatus]!,
      if (instance.messagingEndUserId?.toJson() case final value?)
        'messagingEndUserId': value,
      if (_$MessagingChannelTypeEnumMap[instance.messagingType]
          case final value?)
        'messagingType': value,
      if (instance.mainQuickAction?.toJson() case final value?)
        'mainQuickAction': value,
      if (instance.overflowQuickActions?.map((e) => e.toJson()).toList()
          case final value?)
        'overflowQuickActions': value,
    };

const _$ChatFieldStatusEnumMap = {
  ChatFieldStatus.loading: 'loading',
  ChatFieldStatus.active: 'active',
  ChatFieldStatus.showTemplates: 'showTemplates',
  ChatFieldStatus.showTemplatesRequiredButUnavailableWarning:
      'showTemplatesRequiredButUnavailableWarning',
  ChatFieldStatus.inactiveExistingSession: 'inactiveExistingSession',
  ChatFieldStatus.inactiveNewSession: 'inactiveNewSession',
};

const _$MessagingChannelTypeEnumMap = {
  MessagingChannelType.whatsApp: 'whatsApp',
  MessagingChannelType.text: 'text',
};

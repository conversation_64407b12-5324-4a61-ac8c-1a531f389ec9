// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TransferState {
  List<TransferOption> get transferOptions;
  List<TransferDestination> get destinations;
  List<TransferDestination> get filteredDestinations;
  dynamic get isLoadingDestinations;
  dynamic get isTransferring;
  TransferDestination? get selectedDestination;
  @JsonKey(ignore: true)
  LakConversation? get conversation;
  String? get searchTerm;
  TransferDestinationType? get transferDestinationType;
  @JsonKey(ignore: true)
  UiEvent<TransferStatus>? get transferStatus;

  /// Create a copy of TransferState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TransferStateCopyWith<TransferState> get copyWith =>
      _$TransferStateCopyWithImpl<TransferState>(
          this as TransferState, _$identity);

  /// Serializes this TransferState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TransferState &&
            const DeepCollectionEquality()
                .equals(other.transferOptions, transferOptions) &&
            const DeepCollectionEquality()
                .equals(other.destinations, destinations) &&
            const DeepCollectionEquality()
                .equals(other.filteredDestinations, filteredDestinations) &&
            const DeepCollectionEquality()
                .equals(other.isLoadingDestinations, isLoadingDestinations) &&
            const DeepCollectionEquality()
                .equals(other.isTransferring, isTransferring) &&
            (identical(other.selectedDestination, selectedDestination) ||
                other.selectedDestination == selectedDestination) &&
            (identical(other.conversation, conversation) ||
                other.conversation == conversation) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm) &&
            (identical(
                    other.transferDestinationType, transferDestinationType) ||
                other.transferDestinationType == transferDestinationType) &&
            (identical(other.transferStatus, transferStatus) ||
                other.transferStatus == transferStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(transferOptions),
      const DeepCollectionEquality().hash(destinations),
      const DeepCollectionEquality().hash(filteredDestinations),
      const DeepCollectionEquality().hash(isLoadingDestinations),
      const DeepCollectionEquality().hash(isTransferring),
      selectedDestination,
      conversation,
      searchTerm,
      transferDestinationType,
      transferStatus);

  @override
  String toString() {
    return 'TransferState(transferOptions: $transferOptions, destinations: $destinations, filteredDestinations: $filteredDestinations, isLoadingDestinations: $isLoadingDestinations, isTransferring: $isTransferring, selectedDestination: $selectedDestination, conversation: $conversation, searchTerm: $searchTerm, transferDestinationType: $transferDestinationType, transferStatus: $transferStatus)';
  }
}

/// @nodoc
abstract mixin class $TransferStateCopyWith<$Res> {
  factory $TransferStateCopyWith(
          TransferState value, $Res Function(TransferState) _then) =
      _$TransferStateCopyWithImpl;
  @useResult
  $Res call(
      {List<TransferOption> transferOptions,
      List<TransferDestination> destinations,
      List<TransferDestination> filteredDestinations,
      dynamic isLoadingDestinations,
      dynamic isTransferring,
      TransferDestination? selectedDestination,
      @JsonKey(ignore: true) LakConversation? conversation,
      String? searchTerm,
      TransferDestinationType? transferDestinationType,
      @JsonKey(ignore: true) UiEvent<TransferStatus>? transferStatus});

  $TransferDestinationCopyWith<$Res>? get selectedDestination;
}

/// @nodoc
class _$TransferStateCopyWithImpl<$Res>
    implements $TransferStateCopyWith<$Res> {
  _$TransferStateCopyWithImpl(this._self, this._then);

  final TransferState _self;
  final $Res Function(TransferState) _then;

  /// Create a copy of TransferState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferOptions = null,
    Object? destinations = null,
    Object? filteredDestinations = null,
    Object? isLoadingDestinations = freezed,
    Object? isTransferring = freezed,
    Object? selectedDestination = freezed,
    Object? conversation = freezed,
    Object? searchTerm = freezed,
    Object? transferDestinationType = freezed,
    Object? transferStatus = freezed,
  }) {
    return _then(_self.copyWith(
      transferOptions: null == transferOptions
          ? _self.transferOptions
          : transferOptions // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
      destinations: null == destinations
          ? _self.destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      filteredDestinations: null == filteredDestinations
          ? _self.filteredDestinations
          : filteredDestinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      isLoadingDestinations: freezed == isLoadingDestinations
          ? _self.isLoadingDestinations
          : isLoadingDestinations // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isTransferring: freezed == isTransferring
          ? _self.isTransferring
          : isTransferring // ignore: cast_nullable_to_non_nullable
              as dynamic,
      selectedDestination: freezed == selectedDestination
          ? _self.selectedDestination
          : selectedDestination // ignore: cast_nullable_to_non_nullable
              as TransferDestination?,
      conversation: freezed == conversation
          ? _self.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as LakConversation?,
      searchTerm: freezed == searchTerm
          ? _self.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
      transferDestinationType: freezed == transferDestinationType
          ? _self.transferDestinationType
          : transferDestinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType?,
      transferStatus: freezed == transferStatus
          ? _self.transferStatus
          : transferStatus // ignore: cast_nullable_to_non_nullable
              as UiEvent<TransferStatus>?,
    ));
  }

  /// Create a copy of TransferState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransferDestinationCopyWith<$Res>? get selectedDestination {
    if (_self.selectedDestination == null) {
      return null;
    }

    return $TransferDestinationCopyWith<$Res>(_self.selectedDestination!,
        (value) {
      return _then(_self.copyWith(selectedDestination: value));
    });
  }
}

/// Adds pattern-matching-related methods to [TransferState].
extension TransferStatePatterns on TransferState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_TransferState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_TransferState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_TransferState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            List<TransferOption> transferOptions,
            List<TransferDestination> destinations,
            List<TransferDestination> filteredDestinations,
            dynamic isLoadingDestinations,
            dynamic isTransferring,
            TransferDestination? selectedDestination,
            @JsonKey(ignore: true) LakConversation? conversation,
            String? searchTerm,
            TransferDestinationType? transferDestinationType,
            @JsonKey(ignore: true) UiEvent<TransferStatus>? transferStatus)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TransferState() when $default != null:
        return $default(
            _that.transferOptions,
            _that.destinations,
            _that.filteredDestinations,
            _that.isLoadingDestinations,
            _that.isTransferring,
            _that.selectedDestination,
            _that.conversation,
            _that.searchTerm,
            _that.transferDestinationType,
            _that.transferStatus);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            List<TransferOption> transferOptions,
            List<TransferDestination> destinations,
            List<TransferDestination> filteredDestinations,
            dynamic isLoadingDestinations,
            dynamic isTransferring,
            TransferDestination? selectedDestination,
            @JsonKey(ignore: true) LakConversation? conversation,
            String? searchTerm,
            TransferDestinationType? transferDestinationType,
            @JsonKey(ignore: true) UiEvent<TransferStatus>? transferStatus)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferState():
        return $default(
            _that.transferOptions,
            _that.destinations,
            _that.filteredDestinations,
            _that.isLoadingDestinations,
            _that.isTransferring,
            _that.selectedDestination,
            _that.conversation,
            _that.searchTerm,
            _that.transferDestinationType,
            _that.transferStatus);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            List<TransferOption> transferOptions,
            List<TransferDestination> destinations,
            List<TransferDestination> filteredDestinations,
            dynamic isLoadingDestinations,
            dynamic isTransferring,
            TransferDestination? selectedDestination,
            @JsonKey(ignore: true) LakConversation? conversation,
            String? searchTerm,
            TransferDestinationType? transferDestinationType,
            @JsonKey(ignore: true) UiEvent<TransferStatus>? transferStatus)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TransferState() when $default != null:
        return $default(
            _that.transferOptions,
            _that.destinations,
            _that.filteredDestinations,
            _that.isLoadingDestinations,
            _that.isTransferring,
            _that.selectedDestination,
            _that.conversation,
            _that.searchTerm,
            _that.transferDestinationType,
            _that.transferStatus);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _TransferState implements TransferState {
  const _TransferState(
      {final List<TransferOption> transferOptions = const <TransferOption>[],
      final List<TransferDestination> destinations =
          const <TransferDestination>[],
      final List<TransferDestination> filteredDestinations =
          const <TransferDestination>[],
      this.isLoadingDestinations = true,
      this.isTransferring = false,
      this.selectedDestination = null,
      @JsonKey(ignore: true) this.conversation = null,
      this.searchTerm = null,
      this.transferDestinationType = null,
      @JsonKey(ignore: true) this.transferStatus})
      : _transferOptions = transferOptions,
        _destinations = destinations,
        _filteredDestinations = filteredDestinations;
  factory _TransferState.fromJson(Map<String, dynamic> json) =>
      _$TransferStateFromJson(json);

  final List<TransferOption> _transferOptions;
  @override
  @JsonKey()
  List<TransferOption> get transferOptions {
    if (_transferOptions is EqualUnmodifiableListView) return _transferOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transferOptions);
  }

  final List<TransferDestination> _destinations;
  @override
  @JsonKey()
  List<TransferDestination> get destinations {
    if (_destinations is EqualUnmodifiableListView) return _destinations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_destinations);
  }

  final List<TransferDestination> _filteredDestinations;
  @override
  @JsonKey()
  List<TransferDestination> get filteredDestinations {
    if (_filteredDestinations is EqualUnmodifiableListView)
      return _filteredDestinations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredDestinations);
  }

  @override
  @JsonKey()
  final dynamic isLoadingDestinations;
  @override
  @JsonKey()
  final dynamic isTransferring;
  @override
  @JsonKey()
  final TransferDestination? selectedDestination;
  @override
  @JsonKey(ignore: true)
  final LakConversation? conversation;
  @override
  @JsonKey()
  final String? searchTerm;
  @override
  @JsonKey()
  final TransferDestinationType? transferDestinationType;
  @override
  @JsonKey(ignore: true)
  final UiEvent<TransferStatus>? transferStatus;

  /// Create a copy of TransferState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TransferStateCopyWith<_TransferState> get copyWith =>
      __$TransferStateCopyWithImpl<_TransferState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TransferStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TransferState &&
            const DeepCollectionEquality()
                .equals(other._transferOptions, _transferOptions) &&
            const DeepCollectionEquality()
                .equals(other._destinations, _destinations) &&
            const DeepCollectionEquality()
                .equals(other._filteredDestinations, _filteredDestinations) &&
            const DeepCollectionEquality()
                .equals(other.isLoadingDestinations, isLoadingDestinations) &&
            const DeepCollectionEquality()
                .equals(other.isTransferring, isTransferring) &&
            (identical(other.selectedDestination, selectedDestination) ||
                other.selectedDestination == selectedDestination) &&
            (identical(other.conversation, conversation) ||
                other.conversation == conversation) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm) &&
            (identical(
                    other.transferDestinationType, transferDestinationType) ||
                other.transferDestinationType == transferDestinationType) &&
            (identical(other.transferStatus, transferStatus) ||
                other.transferStatus == transferStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_transferOptions),
      const DeepCollectionEquality().hash(_destinations),
      const DeepCollectionEquality().hash(_filteredDestinations),
      const DeepCollectionEquality().hash(isLoadingDestinations),
      const DeepCollectionEquality().hash(isTransferring),
      selectedDestination,
      conversation,
      searchTerm,
      transferDestinationType,
      transferStatus);

  @override
  String toString() {
    return 'TransferState(transferOptions: $transferOptions, destinations: $destinations, filteredDestinations: $filteredDestinations, isLoadingDestinations: $isLoadingDestinations, isTransferring: $isTransferring, selectedDestination: $selectedDestination, conversation: $conversation, searchTerm: $searchTerm, transferDestinationType: $transferDestinationType, transferStatus: $transferStatus)';
  }
}

/// @nodoc
abstract mixin class _$TransferStateCopyWith<$Res>
    implements $TransferStateCopyWith<$Res> {
  factory _$TransferStateCopyWith(
          _TransferState value, $Res Function(_TransferState) _then) =
      __$TransferStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<TransferOption> transferOptions,
      List<TransferDestination> destinations,
      List<TransferDestination> filteredDestinations,
      dynamic isLoadingDestinations,
      dynamic isTransferring,
      TransferDestination? selectedDestination,
      @JsonKey(ignore: true) LakConversation? conversation,
      String? searchTerm,
      TransferDestinationType? transferDestinationType,
      @JsonKey(ignore: true) UiEvent<TransferStatus>? transferStatus});

  @override
  $TransferDestinationCopyWith<$Res>? get selectedDestination;
}

/// @nodoc
class __$TransferStateCopyWithImpl<$Res>
    implements _$TransferStateCopyWith<$Res> {
  __$TransferStateCopyWithImpl(this._self, this._then);

  final _TransferState _self;
  final $Res Function(_TransferState) _then;

  /// Create a copy of TransferState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? transferOptions = null,
    Object? destinations = null,
    Object? filteredDestinations = null,
    Object? isLoadingDestinations = freezed,
    Object? isTransferring = freezed,
    Object? selectedDestination = freezed,
    Object? conversation = freezed,
    Object? searchTerm = freezed,
    Object? transferDestinationType = freezed,
    Object? transferStatus = freezed,
  }) {
    return _then(_TransferState(
      transferOptions: null == transferOptions
          ? _self._transferOptions
          : transferOptions // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
      destinations: null == destinations
          ? _self._destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      filteredDestinations: null == filteredDestinations
          ? _self._filteredDestinations
          : filteredDestinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      isLoadingDestinations: freezed == isLoadingDestinations
          ? _self.isLoadingDestinations
          : isLoadingDestinations // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isTransferring: freezed == isTransferring
          ? _self.isTransferring
          : isTransferring // ignore: cast_nullable_to_non_nullable
              as dynamic,
      selectedDestination: freezed == selectedDestination
          ? _self.selectedDestination
          : selectedDestination // ignore: cast_nullable_to_non_nullable
              as TransferDestination?,
      conversation: freezed == conversation
          ? _self.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as LakConversation?,
      searchTerm: freezed == searchTerm
          ? _self.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
      transferDestinationType: freezed == transferDestinationType
          ? _self.transferDestinationType
          : transferDestinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType?,
      transferStatus: freezed == transferStatus
          ? _self.transferStatus
          : transferStatus // ignore: cast_nullable_to_non_nullable
              as UiEvent<TransferStatus>?,
    ));
  }

  /// Create a copy of TransferState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransferDestinationCopyWith<$Res>? get selectedDestination {
    if (_self.selectedDestination == null) {
      return null;
    }

    return $TransferDestinationCopyWith<$Res>(_self.selectedDestination!,
        (value) {
      return _then(_self.copyWith(selectedDestination: value));
    });
  }
}

// dart format on

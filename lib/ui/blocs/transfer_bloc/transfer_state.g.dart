// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransferState _$TransferStateFromJson(Map json) => $checkedCreate(
      '_TransferState',
      json,
      ($checkedConvert) {
        final val = _TransferState(
          transferOptions: $checkedConvert(
              'transferOptions',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => TransferOption.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <TransferOption>[]),
          destinations: $checkedConvert(
              'destinations',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => TransferDestination.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <TransferDestination>[]),
          filteredDestinations: $checkedConvert(
              'filteredDestinations',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => TransferDestination.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <TransferDestination>[]),
          isLoadingDestinations:
              $checkedConvert('isLoadingDestinations', (v) => v ?? true),
          isTransferring: $checkedConvert('isTransferring', (v) => v ?? false),
          selectedDestination: $checkedConvert(
              'selectedDestination',
              (v) => v == null
                  ? null
                  : TransferDestination.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          searchTerm:
              $checkedConvert('searchTerm', (v) => v as String? ?? null),
          transferDestinationType: $checkedConvert(
              'transferDestinationType',
              (v) =>
                  $enumDecodeNullable(_$TransferDestinationTypeEnumMap, v) ??
                  null),
        );
        return val;
      },
    );

Map<String, dynamic> _$TransferStateToJson(_TransferState instance) =>
    <String, dynamic>{
      'transferOptions':
          instance.transferOptions.map((e) => e.toJson()).toList(),
      'destinations': instance.destinations.map((e) => e.toJson()).toList(),
      'filteredDestinations':
          instance.filteredDestinations.map((e) => e.toJson()).toList(),
      if (instance.isLoadingDestinations case final value?)
        'isLoadingDestinations': value,
      if (instance.isTransferring case final value?) 'isTransferring': value,
      if (instance.selectedDestination?.toJson() case final value?)
        'selectedDestination': value,
      if (instance.searchTerm case final value?) 'searchTerm': value,
      if (_$TransferDestinationTypeEnumMap[instance.transferDestinationType]
          case final value?)
        'transferDestinationType': value,
    };

const _$TransferDestinationTypeEnumMap = {
  TransferDestinationType.queue: 'queue',
  TransferDestinationType.agent: 'agent',
  TransferDestinationType.flow: 'flow',
};

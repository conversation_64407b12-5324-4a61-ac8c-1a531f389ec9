// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppErrorState {
  @JsonKey(ignore: true)
  UiEvent<Nothing>? get showError;
  AppError? get activeError;
  List<AppError> get handledErrors;
  List<AppError> get reportedErrors;

  /// Create a copy of AppErrorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AppErrorStateCopyWith<AppErrorState> get copyWith =>
      _$AppErrorStateCopyWithImpl<AppErrorState>(
          this as AppErrorState, _$identity);

  /// Serializes this AppErrorState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppErrorState &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.activeError, activeError) ||
                other.activeError == activeError) &&
            const DeepCollectionEquality()
                .equals(other.handledErrors, handledErrors) &&
            const DeepCollectionEquality()
                .equals(other.reportedErrors, reportedErrors));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showError,
      activeError,
      const DeepCollectionEquality().hash(handledErrors),
      const DeepCollectionEquality().hash(reportedErrors));

  @override
  String toString() {
    return 'AppErrorState(showError: $showError, activeError: $activeError, handledErrors: $handledErrors, reportedErrors: $reportedErrors)';
  }
}

/// @nodoc
abstract mixin class $AppErrorStateCopyWith<$Res> {
  factory $AppErrorStateCopyWith(
          AppErrorState value, $Res Function(AppErrorState) _then) =
      _$AppErrorStateCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(ignore: true) UiEvent<Nothing>? showError,
      AppError? activeError,
      List<AppError> handledErrors,
      List<AppError> reportedErrors});

  $AppErrorCopyWith<$Res>? get activeError;
}

/// @nodoc
class _$AppErrorStateCopyWithImpl<$Res>
    implements $AppErrorStateCopyWith<$Res> {
  _$AppErrorStateCopyWithImpl(this._self, this._then);

  final AppErrorState _self;
  final $Res Function(AppErrorState) _then;

  /// Create a copy of AppErrorState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showError = freezed,
    Object? activeError = freezed,
    Object? handledErrors = null,
    Object? reportedErrors = null,
  }) {
    return _then(_self.copyWith(
      showError: freezed == showError
          ? _self.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      activeError: freezed == activeError
          ? _self.activeError
          : activeError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      handledErrors: null == handledErrors
          ? _self.handledErrors
          : handledErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
      reportedErrors: null == reportedErrors
          ? _self.reportedErrors
          : reportedErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
    ));
  }

  /// Create a copy of AppErrorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get activeError {
    if (_self.activeError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_self.activeError!, (value) {
      return _then(_self.copyWith(activeError: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AppErrorState].
extension AppErrorStatePatterns on AppErrorState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AppErrorState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppErrorState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AppErrorState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppErrorState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AppErrorState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppErrorState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true) UiEvent<Nothing>? showError,
            AppError? activeError,
            List<AppError> handledErrors,
            List<AppError> reportedErrors)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppErrorState() when $default != null:
        return $default(_that.showError, _that.activeError, _that.handledErrors,
            _that.reportedErrors);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(ignore: true) UiEvent<Nothing>? showError,
            AppError? activeError,
            List<AppError> handledErrors,
            List<AppError> reportedErrors)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppErrorState():
        return $default(_that.showError, _that.activeError, _that.handledErrors,
            _that.reportedErrors);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(ignore: true) UiEvent<Nothing>? showError,
            AppError? activeError,
            List<AppError> handledErrors,
            List<AppError> reportedErrors)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppErrorState() when $default != null:
        return $default(_that.showError, _that.activeError, _that.handledErrors,
            _that.reportedErrors);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AppErrorState implements AppErrorState {
  _AppErrorState(
      {@JsonKey(ignore: true) this.showError,
      this.activeError,
      final List<AppError> handledErrors = const <AppError>[],
      final List<AppError> reportedErrors = const <AppError>[]})
      : _handledErrors = handledErrors,
        _reportedErrors = reportedErrors;
  factory _AppErrorState.fromJson(Map<String, dynamic> json) =>
      _$AppErrorStateFromJson(json);

  @override
  @JsonKey(ignore: true)
  final UiEvent<Nothing>? showError;
  @override
  final AppError? activeError;
  final List<AppError> _handledErrors;
  @override
  @JsonKey()
  List<AppError> get handledErrors {
    if (_handledErrors is EqualUnmodifiableListView) return _handledErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_handledErrors);
  }

  final List<AppError> _reportedErrors;
  @override
  @JsonKey()
  List<AppError> get reportedErrors {
    if (_reportedErrors is EqualUnmodifiableListView) return _reportedErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportedErrors);
  }

  /// Create a copy of AppErrorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AppErrorStateCopyWith<_AppErrorState> get copyWith =>
      __$AppErrorStateCopyWithImpl<_AppErrorState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AppErrorStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AppErrorState &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.activeError, activeError) ||
                other.activeError == activeError) &&
            const DeepCollectionEquality()
                .equals(other._handledErrors, _handledErrors) &&
            const DeepCollectionEquality()
                .equals(other._reportedErrors, _reportedErrors));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showError,
      activeError,
      const DeepCollectionEquality().hash(_handledErrors),
      const DeepCollectionEquality().hash(_reportedErrors));

  @override
  String toString() {
    return 'AppErrorState(showError: $showError, activeError: $activeError, handledErrors: $handledErrors, reportedErrors: $reportedErrors)';
  }
}

/// @nodoc
abstract mixin class _$AppErrorStateCopyWith<$Res>
    implements $AppErrorStateCopyWith<$Res> {
  factory _$AppErrorStateCopyWith(
          _AppErrorState value, $Res Function(_AppErrorState) _then) =
      __$AppErrorStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(ignore: true) UiEvent<Nothing>? showError,
      AppError? activeError,
      List<AppError> handledErrors,
      List<AppError> reportedErrors});

  @override
  $AppErrorCopyWith<$Res>? get activeError;
}

/// @nodoc
class __$AppErrorStateCopyWithImpl<$Res>
    implements _$AppErrorStateCopyWith<$Res> {
  __$AppErrorStateCopyWithImpl(this._self, this._then);

  final _AppErrorState _self;
  final $Res Function(_AppErrorState) _then;

  /// Create a copy of AppErrorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? showError = freezed,
    Object? activeError = freezed,
    Object? handledErrors = null,
    Object? reportedErrors = null,
  }) {
    return _then(_AppErrorState(
      showError: freezed == showError
          ? _self.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      activeError: freezed == activeError
          ? _self.activeError
          : activeError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      handledErrors: null == handledErrors
          ? _self._handledErrors
          : handledErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
      reportedErrors: null == reportedErrors
          ? _self._reportedErrors
          : reportedErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
    ));
  }

  /// Create a copy of AppErrorState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get activeError {
    if (_self.activeError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_self.activeError!, (value) {
      return _then(_self.copyWith(activeError: value));
    });
  }
}

// dart format on

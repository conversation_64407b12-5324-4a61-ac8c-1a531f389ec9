// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_error_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppErrorState _$AppErrorStateFromJson(Map json) => $checkedCreate(
      '_AppErrorState',
      json,
      ($checkedConvert) {
        final val = _AppErrorState(
          activeError: $checkedConvert(
              'activeError',
              (v) => v == null
                  ? null
                  : AppError.fromJson(Map<String, dynamic>.from(v as Map))),
          handledErrors: $checkedConvert(
              'handledErrors',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => AppError.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <AppError>[]),
          reportedErrors: $checkedConvert(
              'reportedErrors',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => AppError.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <AppError>[]),
        );
        return val;
      },
    );

Map<String, dynamic> _$AppErrorStateToJson(_AppErrorState instance) =>
    <String, dynamic>{
      if (instance.activeError?.toJson() case final value?)
        'activeError': value,
      'handledErrors': instance.handledErrors.map((e) => e.toJson()).toList(),
      'reportedErrors': instance.reportedErrors.map((e) => e.toJson()).toList(),
    };

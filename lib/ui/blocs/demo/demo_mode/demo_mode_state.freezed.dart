// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'demo_mode_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DemoModeState {
  bool get toggle;
  @JsonKey(ignore: true)
  Map<SfId, String> get contactPhotoUrls;
  @JsonKey(ignore: true)
  Map<SfId, Contact> get contacts;
  @JsonKey(ignore: true)
  Map<SfId, SfId> get meuContactAssociations;
  Set<SfId> get conversationsWithUnreadMessages;
  DemoUser? get user;

  /// Create a copy of DemoModeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DemoModeStateCopyWith<DemoModeState> get copyWith =>
      _$DemoModeStateCopyWithImpl<DemoModeState>(
          this as DemoModeState, _$identity);

  /// Serializes this DemoModeState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DemoModeState &&
            (identical(other.toggle, toggle) || other.toggle == toggle) &&
            const DeepCollectionEquality()
                .equals(other.contactPhotoUrls, contactPhotoUrls) &&
            const DeepCollectionEquality().equals(other.contacts, contacts) &&
            const DeepCollectionEquality()
                .equals(other.meuContactAssociations, meuContactAssociations) &&
            const DeepCollectionEquality().equals(
                other.conversationsWithUnreadMessages,
                conversationsWithUnreadMessages) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      toggle,
      const DeepCollectionEquality().hash(contactPhotoUrls),
      const DeepCollectionEquality().hash(contacts),
      const DeepCollectionEquality().hash(meuContactAssociations),
      const DeepCollectionEquality().hash(conversationsWithUnreadMessages),
      user);

  @override
  String toString() {
    return 'DemoModeState(toggle: $toggle, contactPhotoUrls: $contactPhotoUrls, contacts: $contacts, meuContactAssociations: $meuContactAssociations, conversationsWithUnreadMessages: $conversationsWithUnreadMessages, user: $user)';
  }
}

/// @nodoc
abstract mixin class $DemoModeStateCopyWith<$Res> {
  factory $DemoModeStateCopyWith(
          DemoModeState value, $Res Function(DemoModeState) _then) =
      _$DemoModeStateCopyWithImpl;
  @useResult
  $Res call(
      {bool toggle,
      @JsonKey(ignore: true) Map<SfId, String> contactPhotoUrls,
      @JsonKey(ignore: true) Map<SfId, Contact> contacts,
      @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations,
      Set<SfId> conversationsWithUnreadMessages,
      DemoUser? user});

  $DemoUserCopyWith<$Res>? get user;
}

/// @nodoc
class _$DemoModeStateCopyWithImpl<$Res>
    implements $DemoModeStateCopyWith<$Res> {
  _$DemoModeStateCopyWithImpl(this._self, this._then);

  final DemoModeState _self;
  final $Res Function(DemoModeState) _then;

  /// Create a copy of DemoModeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? toggle = null,
    Object? contactPhotoUrls = null,
    Object? contacts = null,
    Object? meuContactAssociations = null,
    Object? conversationsWithUnreadMessages = null,
    Object? user = freezed,
  }) {
    return _then(_self.copyWith(
      toggle: null == toggle
          ? _self.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      contactPhotoUrls: null == contactPhotoUrls
          ? _self.contactPhotoUrls
          : contactPhotoUrls // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
      contacts: null == contacts
          ? _self.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _self.meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
      conversationsWithUnreadMessages: null == conversationsWithUnreadMessages
          ? _self.conversationsWithUnreadMessages
          : conversationsWithUnreadMessages // ignore: cast_nullable_to_non_nullable
              as Set<SfId>,
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as DemoUser?,
    ));
  }

  /// Create a copy of DemoModeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DemoUserCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $DemoUserCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }
}

/// Adds pattern-matching-related methods to [DemoModeState].
extension DemoModeStatePatterns on DemoModeState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DemoModeState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DemoModeState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DemoModeState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoModeState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DemoModeState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoModeState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool toggle,
            @JsonKey(ignore: true) Map<SfId, String> contactPhotoUrls,
            @JsonKey(ignore: true) Map<SfId, Contact> contacts,
            @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations,
            Set<SfId> conversationsWithUnreadMessages,
            DemoUser? user)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DemoModeState() when $default != null:
        return $default(
            _that.toggle,
            _that.contactPhotoUrls,
            _that.contacts,
            _that.meuContactAssociations,
            _that.conversationsWithUnreadMessages,
            _that.user);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool toggle,
            @JsonKey(ignore: true) Map<SfId, String> contactPhotoUrls,
            @JsonKey(ignore: true) Map<SfId, Contact> contacts,
            @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations,
            Set<SfId> conversationsWithUnreadMessages,
            DemoUser? user)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoModeState():
        return $default(
            _that.toggle,
            _that.contactPhotoUrls,
            _that.contacts,
            _that.meuContactAssociations,
            _that.conversationsWithUnreadMessages,
            _that.user);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool toggle,
            @JsonKey(ignore: true) Map<SfId, String> contactPhotoUrls,
            @JsonKey(ignore: true) Map<SfId, Contact> contacts,
            @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations,
            Set<SfId> conversationsWithUnreadMessages,
            DemoUser? user)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoModeState() when $default != null:
        return $default(
            _that.toggle,
            _that.contactPhotoUrls,
            _that.contacts,
            _that.meuContactAssociations,
            _that.conversationsWithUnreadMessages,
            _that.user);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DemoModeState extends DemoModeState {
  const _DemoModeState(
      {this.toggle = true,
      @JsonKey(ignore: true)
      final Map<SfId, String> contactPhotoUrls = const {},
      @JsonKey(ignore: true) final Map<SfId, Contact> contacts = const {},
      @JsonKey(ignore: true)
      final Map<SfId, SfId> meuContactAssociations = const {},
      final Set<SfId> conversationsWithUnreadMessages = const {},
      this.user})
      : _contactPhotoUrls = contactPhotoUrls,
        _contacts = contacts,
        _meuContactAssociations = meuContactAssociations,
        _conversationsWithUnreadMessages = conversationsWithUnreadMessages,
        super._();
  factory _DemoModeState.fromJson(Map<String, dynamic> json) =>
      _$DemoModeStateFromJson(json);

  @override
  @JsonKey()
  final bool toggle;
  final Map<SfId, String> _contactPhotoUrls;
  @override
  @JsonKey(ignore: true)
  Map<SfId, String> get contactPhotoUrls {
    if (_contactPhotoUrls is EqualUnmodifiableMapView) return _contactPhotoUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_contactPhotoUrls);
  }

  final Map<SfId, Contact> _contacts;
  @override
  @JsonKey(ignore: true)
  Map<SfId, Contact> get contacts {
    if (_contacts is EqualUnmodifiableMapView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_contacts);
  }

  final Map<SfId, SfId> _meuContactAssociations;
  @override
  @JsonKey(ignore: true)
  Map<SfId, SfId> get meuContactAssociations {
    if (_meuContactAssociations is EqualUnmodifiableMapView)
      return _meuContactAssociations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_meuContactAssociations);
  }

  final Set<SfId> _conversationsWithUnreadMessages;
  @override
  @JsonKey()
  Set<SfId> get conversationsWithUnreadMessages {
    if (_conversationsWithUnreadMessages is EqualUnmodifiableSetView)
      return _conversationsWithUnreadMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_conversationsWithUnreadMessages);
  }

  @override
  final DemoUser? user;

  /// Create a copy of DemoModeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DemoModeStateCopyWith<_DemoModeState> get copyWith =>
      __$DemoModeStateCopyWithImpl<_DemoModeState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DemoModeStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DemoModeState &&
            (identical(other.toggle, toggle) || other.toggle == toggle) &&
            const DeepCollectionEquality()
                .equals(other._contactPhotoUrls, _contactPhotoUrls) &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            const DeepCollectionEquality().equals(
                other._meuContactAssociations, _meuContactAssociations) &&
            const DeepCollectionEquality().equals(
                other._conversationsWithUnreadMessages,
                _conversationsWithUnreadMessages) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      toggle,
      const DeepCollectionEquality().hash(_contactPhotoUrls),
      const DeepCollectionEquality().hash(_contacts),
      const DeepCollectionEquality().hash(_meuContactAssociations),
      const DeepCollectionEquality().hash(_conversationsWithUnreadMessages),
      user);

  @override
  String toString() {
    return 'DemoModeState(toggle: $toggle, contactPhotoUrls: $contactPhotoUrls, contacts: $contacts, meuContactAssociations: $meuContactAssociations, conversationsWithUnreadMessages: $conversationsWithUnreadMessages, user: $user)';
  }
}

/// @nodoc
abstract mixin class _$DemoModeStateCopyWith<$Res>
    implements $DemoModeStateCopyWith<$Res> {
  factory _$DemoModeStateCopyWith(
          _DemoModeState value, $Res Function(_DemoModeState) _then) =
      __$DemoModeStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool toggle,
      @JsonKey(ignore: true) Map<SfId, String> contactPhotoUrls,
      @JsonKey(ignore: true) Map<SfId, Contact> contacts,
      @JsonKey(ignore: true) Map<SfId, SfId> meuContactAssociations,
      Set<SfId> conversationsWithUnreadMessages,
      DemoUser? user});

  @override
  $DemoUserCopyWith<$Res>? get user;
}

/// @nodoc
class __$DemoModeStateCopyWithImpl<$Res>
    implements _$DemoModeStateCopyWith<$Res> {
  __$DemoModeStateCopyWithImpl(this._self, this._then);

  final _DemoModeState _self;
  final $Res Function(_DemoModeState) _then;

  /// Create a copy of DemoModeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? toggle = null,
    Object? contactPhotoUrls = null,
    Object? contacts = null,
    Object? meuContactAssociations = null,
    Object? conversationsWithUnreadMessages = null,
    Object? user = freezed,
  }) {
    return _then(_DemoModeState(
      toggle: null == toggle
          ? _self.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      contactPhotoUrls: null == contactPhotoUrls
          ? _self._contactPhotoUrls
          : contactPhotoUrls // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
      contacts: null == contacts
          ? _self._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _self._meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
      conversationsWithUnreadMessages: null == conversationsWithUnreadMessages
          ? _self._conversationsWithUnreadMessages
          : conversationsWithUnreadMessages // ignore: cast_nullable_to_non_nullable
              as Set<SfId>,
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as DemoUser?,
    ));
  }

  /// Create a copy of DemoModeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DemoUserCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $DemoUserCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }
}

// dart format on

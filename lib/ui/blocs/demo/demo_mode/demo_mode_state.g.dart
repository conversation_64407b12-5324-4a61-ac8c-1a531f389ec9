// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'demo_mode_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DemoModeState _$DemoModeStateFromJson(Map json) => $checkedCreate(
      '_DemoModeState',
      json,
      ($checkedConvert) {
        final val = _DemoModeState(
          toggle: $checkedConvert('toggle', (v) => v as bool? ?? true),
          conversationsWithUnreadMessages: $checkedConvert(
              'conversationsWithUnreadMessages',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) =>
                          SfId.fromJson(Map<String, dynamic>.from(e as Map)))
                      .toSet() ??
                  const {}),
          user: $checkedConvert(
              'user',
              (v) => v == null
                  ? null
                  : DemoUser.fromJson(Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$DemoModeStateToJson(_DemoModeState instance) =>
    <String, dynamic>{
      'toggle': instance.toggle,
      'conversationsWithUnreadMessages': instance
          .conversationsWithUnreadMessages
          .map((e) => e.toJson())
          .toList(),
      if (instance.user?.toJson() case final value?) 'user': value,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'demo_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DemoUser {
  String? get name;
  String? get email;
  String? get phoneNumber;
  String? get companyName;

  /// Create a copy of DemoUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DemoUserCopyWith<DemoUser> get copyWith =>
      _$DemoUserCopyWithImpl<DemoUser>(this as DemoUser, _$identity);

  /// Serializes this DemoUser to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DemoUser &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, email, phoneNumber, companyName);

  @override
  String toString() {
    return 'DemoUser(name: $name, email: $email, phoneNumber: $phoneNumber, companyName: $companyName)';
  }
}

/// @nodoc
abstract mixin class $DemoUserCopyWith<$Res> {
  factory $DemoUserCopyWith(DemoUser value, $Res Function(DemoUser) _then) =
      _$DemoUserCopyWithImpl;
  @useResult
  $Res call(
      {String? name, String? email, String? phoneNumber, String? companyName});
}

/// @nodoc
class _$DemoUserCopyWithImpl<$Res> implements $DemoUserCopyWith<$Res> {
  _$DemoUserCopyWithImpl(this._self, this._then);

  final DemoUser _self;
  final $Res Function(DemoUser) _then;

  /// Create a copy of DemoUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? companyName = freezed,
  }) {
    return _then(_self.copyWith(
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _self.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [DemoUser].
extension DemoUserPatterns on DemoUser {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DemoUser value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DemoUser() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DemoUser value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoUser():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DemoUser value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoUser() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String? name, String? email, String? phoneNumber,
            String? companyName)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DemoUser() when $default != null:
        return $default(
            _that.name, _that.email, _that.phoneNumber, _that.companyName);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String? name, String? email, String? phoneNumber,
            String? companyName)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoUser():
        return $default(
            _that.name, _that.email, _that.phoneNumber, _that.companyName);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String? name, String? email, String? phoneNumber,
            String? companyName)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoUser() when $default != null:
        return $default(
            _that.name, _that.email, _that.phoneNumber, _that.companyName);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DemoUser extends DemoUser {
  const _DemoUser({this.name, this.email, this.phoneNumber, this.companyName})
      : super._();
  factory _DemoUser.fromJson(Map<String, dynamic> json) =>
      _$DemoUserFromJson(json);

  @override
  final String? name;
  @override
  final String? email;
  @override
  final String? phoneNumber;
  @override
  final String? companyName;

  /// Create a copy of DemoUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DemoUserCopyWith<_DemoUser> get copyWith =>
      __$DemoUserCopyWithImpl<_DemoUser>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DemoUserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DemoUser &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, email, phoneNumber, companyName);

  @override
  String toString() {
    return 'DemoUser(name: $name, email: $email, phoneNumber: $phoneNumber, companyName: $companyName)';
  }
}

/// @nodoc
abstract mixin class _$DemoUserCopyWith<$Res>
    implements $DemoUserCopyWith<$Res> {
  factory _$DemoUserCopyWith(_DemoUser value, $Res Function(_DemoUser) _then) =
      __$DemoUserCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? name, String? email, String? phoneNumber, String? companyName});
}

/// @nodoc
class __$DemoUserCopyWithImpl<$Res> implements _$DemoUserCopyWith<$Res> {
  __$DemoUserCopyWithImpl(this._self, this._then);

  final _DemoUser _self;
  final $Res Function(_DemoUser) _then;

  /// Create a copy of DemoUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? companyName = freezed,
  }) {
    return _then(_DemoUser(
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _self.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'demo_form_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DemoFormState {
  String get name;
  String get phoneNumber;
  String get email;
  String get companyName;
  bool get isValid;
  @JsonKey(ignore: true)
  UiEvent<Nothing>? get submitEvent;

  /// Create a copy of DemoFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DemoFormStateCopyWith<DemoFormState> get copyWith =>
      _$DemoFormStateCopyWithImpl<DemoFormState>(
          this as DemoFormState, _$identity);

  /// Serializes this DemoFormState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DemoFormState &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.submitEvent, submitEvent) ||
                other.submitEvent == submitEvent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, phoneNumber, email, companyName, isValid, submitEvent);

  @override
  String toString() {
    return 'DemoFormState(name: $name, phoneNumber: $phoneNumber, email: $email, companyName: $companyName, isValid: $isValid, submitEvent: $submitEvent)';
  }
}

/// @nodoc
abstract mixin class $DemoFormStateCopyWith<$Res> {
  factory $DemoFormStateCopyWith(
          DemoFormState value, $Res Function(DemoFormState) _then) =
      _$DemoFormStateCopyWithImpl;
  @useResult
  $Res call(
      {String name,
      String phoneNumber,
      String email,
      String companyName,
      bool isValid,
      @JsonKey(ignore: true) UiEvent<Nothing>? submitEvent});
}

/// @nodoc
class _$DemoFormStateCopyWithImpl<$Res>
    implements $DemoFormStateCopyWith<$Res> {
  _$DemoFormStateCopyWithImpl(this._self, this._then);

  final DemoFormState _self;
  final $Res Function(DemoFormState) _then;

  /// Create a copy of DemoFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? email = null,
    Object? companyName = null,
    Object? isValid = null,
    Object? submitEvent = freezed,
  }) {
    return _then(_self.copyWith(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      companyName: null == companyName
          ? _self.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      isValid: null == isValid
          ? _self.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      submitEvent: freezed == submitEvent
          ? _self.submitEvent
          : submitEvent // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [DemoFormState].
extension DemoFormStatePatterns on DemoFormState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DemoFormState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DemoFormState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DemoFormState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoFormState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DemoFormState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoFormState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String name,
            String phoneNumber,
            String email,
            String companyName,
            bool isValid,
            @JsonKey(ignore: true) UiEvent<Nothing>? submitEvent)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DemoFormState() when $default != null:
        return $default(_that.name, _that.phoneNumber, _that.email,
            _that.companyName, _that.isValid, _that.submitEvent);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String name,
            String phoneNumber,
            String email,
            String companyName,
            bool isValid,
            @JsonKey(ignore: true) UiEvent<Nothing>? submitEvent)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoFormState():
        return $default(_that.name, _that.phoneNumber, _that.email,
            _that.companyName, _that.isValid, _that.submitEvent);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String name,
            String phoneNumber,
            String email,
            String companyName,
            bool isValid,
            @JsonKey(ignore: true) UiEvent<Nothing>? submitEvent)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DemoFormState() when $default != null:
        return $default(_that.name, _that.phoneNumber, _that.email,
            _that.companyName, _that.isValid, _that.submitEvent);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DemoFormState implements DemoFormState {
  const _DemoFormState(
      {this.name = '',
      this.phoneNumber = '',
      this.email = '',
      this.companyName = '',
      this.isValid = false,
      @JsonKey(ignore: true) this.submitEvent});
  factory _DemoFormState.fromJson(Map<String, dynamic> json) =>
      _$DemoFormStateFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String phoneNumber;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final String companyName;
  @override
  @JsonKey()
  final bool isValid;
  @override
  @JsonKey(ignore: true)
  final UiEvent<Nothing>? submitEvent;

  /// Create a copy of DemoFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DemoFormStateCopyWith<_DemoFormState> get copyWith =>
      __$DemoFormStateCopyWithImpl<_DemoFormState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DemoFormStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DemoFormState &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.submitEvent, submitEvent) ||
                other.submitEvent == submitEvent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, phoneNumber, email, companyName, isValid, submitEvent);

  @override
  String toString() {
    return 'DemoFormState(name: $name, phoneNumber: $phoneNumber, email: $email, companyName: $companyName, isValid: $isValid, submitEvent: $submitEvent)';
  }
}

/// @nodoc
abstract mixin class _$DemoFormStateCopyWith<$Res>
    implements $DemoFormStateCopyWith<$Res> {
  factory _$DemoFormStateCopyWith(
          _DemoFormState value, $Res Function(_DemoFormState) _then) =
      __$DemoFormStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String name,
      String phoneNumber,
      String email,
      String companyName,
      bool isValid,
      @JsonKey(ignore: true) UiEvent<Nothing>? submitEvent});
}

/// @nodoc
class __$DemoFormStateCopyWithImpl<$Res>
    implements _$DemoFormStateCopyWith<$Res> {
  __$DemoFormStateCopyWithImpl(this._self, this._then);

  final _DemoFormState _self;
  final $Res Function(_DemoFormState) _then;

  /// Create a copy of DemoFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? email = null,
    Object? companyName = null,
    Object? isValid = null,
    Object? submitEvent = freezed,
  }) {
    return _then(_DemoFormState(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      companyName: null == companyName
          ? _self.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      isValid: null == isValid
          ? _self.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      submitEvent: freezed == submitEvent
          ? _self.submitEvent
          : submitEvent // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }
}

// dart format on

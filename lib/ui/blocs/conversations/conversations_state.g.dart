// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversations_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ConversationsState _$ConversationsStateFromJson(Map json) => $checkedCreate(
      '_ConversationsState',
      json,
      ($checkedConvert) {
        final val = _ConversationsState(
          showEnded: $checkedConvert('showEnded', (v) => v as bool? ?? false),
          sortingOption: $checkedConvert(
              'sortingOption',
              (v) =>
                  $enumDecodeNullable(_$ConversationSortingOptionEnumMap, v)),
          isLoading: $checkedConvert('isLoading', (v) => v ?? false),
          mainQuickAction: $checkedConvert(
              'mainQuickAction',
              (v) => v == null
                  ? null
                  : QuickAction.fromJson(Map<String, dynamic>.from(v as Map))),
          overflowQuickActions: $checkedConvert(
              'overflowQuickActions',
              (v) => (v as List<dynamic>?)
                  ?.map((e) =>
                      QuickAction.fromJson(Map<String, dynamic>.from(e as Map)))
                  .toList()),
          pushPermissionsGranted: $checkedConvert(
              'pushPermissionsGranted', (v) => v as bool? ?? true),
        );
        return val;
      },
    );

Map<String, dynamic> _$ConversationsStateToJson(_ConversationsState instance) =>
    <String, dynamic>{
      'showEnded': instance.showEnded,
      if (_$ConversationSortingOptionEnumMap[instance.sortingOption]
          case final value?)
        'sortingOption': value,
      if (instance.isLoading case final value?) 'isLoading': value,
      if (instance.mainQuickAction?.toJson() case final value?)
        'mainQuickAction': value,
      if (instance.overflowQuickActions?.map((e) => e.toJson()).toList()
          case final value?)
        'overflowQuickActions': value,
      'pushPermissionsGranted': instance.pushPermissionsGranted,
    };

const _$ConversationSortingOptionEnumMap = {
  ConversationSortingOption.alphabetical: 'alphabetical',
  ConversationSortingOption.chronological: 'chronological',
  ConversationSortingOption.byChannel: 'byChannel',
};

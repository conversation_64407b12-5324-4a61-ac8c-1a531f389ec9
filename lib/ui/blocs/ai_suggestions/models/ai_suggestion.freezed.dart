// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_suggestion.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AiSuggestion {
  @ParseSfIdConverter()
  SfId get meuId;
  bool get isLoading;
  String? get suggestion;

  /// Create a copy of AiSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AiSuggestionCopyWith<AiSuggestion> get copyWith =>
      _$AiSuggestionCopyWithImpl<AiSuggestion>(
          this as AiSuggestion, _$identity);

  /// Serializes this AiSuggestion to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AiSuggestion &&
            (identical(other.meuId, meuId) || other.meuId == meuId) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.suggestion, suggestion) ||
                other.suggestion == suggestion));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, meuId, isLoading, suggestion);

  @override
  String toString() {
    return 'AiSuggestion(meuId: $meuId, isLoading: $isLoading, suggestion: $suggestion)';
  }
}

/// @nodoc
abstract mixin class $AiSuggestionCopyWith<$Res> {
  factory $AiSuggestionCopyWith(
          AiSuggestion value, $Res Function(AiSuggestion) _then) =
      _$AiSuggestionCopyWithImpl;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId meuId, bool isLoading, String? suggestion});

  $SfIdCopyWith<$Res> get meuId;
}

/// @nodoc
class _$AiSuggestionCopyWithImpl<$Res> implements $AiSuggestionCopyWith<$Res> {
  _$AiSuggestionCopyWithImpl(this._self, this._then);

  final AiSuggestion _self;
  final $Res Function(AiSuggestion) _then;

  /// Create a copy of AiSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? meuId = null,
    Object? isLoading = null,
    Object? suggestion = freezed,
  }) {
    return _then(_self.copyWith(
      meuId: null == meuId
          ? _self.meuId
          : meuId // ignore: cast_nullable_to_non_nullable
              as SfId,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      suggestion: freezed == suggestion
          ? _self.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of AiSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get meuId {
    return $SfIdCopyWith<$Res>(_self.meuId, (value) {
      return _then(_self.copyWith(meuId: value));
    });
  }
}

/// Adds pattern-matching-related methods to [AiSuggestion].
extension AiSuggestionPatterns on AiSuggestion {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AiSuggestion value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AiSuggestion() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AiSuggestion value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestion():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AiSuggestion value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestion() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId meuId, bool isLoading,
            String? suggestion)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AiSuggestion() when $default != null:
        return $default(_that.meuId, _that.isLoading, _that.suggestion);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() SfId meuId, bool isLoading,
            String? suggestion)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestion():
        return $default(_that.meuId, _that.isLoading, _that.suggestion);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@ParseSfIdConverter() SfId meuId, bool isLoading,
            String? suggestion)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestion() when $default != null:
        return $default(_that.meuId, _that.isLoading, _that.suggestion);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AiSuggestion implements AiSuggestion {
  const _AiSuggestion(
      {@ParseSfIdConverter() required this.meuId,
      this.isLoading = true,
      this.suggestion});
  factory _AiSuggestion.fromJson(Map<String, dynamic> json) =>
      _$AiSuggestionFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId meuId;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? suggestion;

  /// Create a copy of AiSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AiSuggestionCopyWith<_AiSuggestion> get copyWith =>
      __$AiSuggestionCopyWithImpl<_AiSuggestion>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AiSuggestionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AiSuggestion &&
            (identical(other.meuId, meuId) || other.meuId == meuId) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.suggestion, suggestion) ||
                other.suggestion == suggestion));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, meuId, isLoading, suggestion);

  @override
  String toString() {
    return 'AiSuggestion(meuId: $meuId, isLoading: $isLoading, suggestion: $suggestion)';
  }
}

/// @nodoc
abstract mixin class _$AiSuggestionCopyWith<$Res>
    implements $AiSuggestionCopyWith<$Res> {
  factory _$AiSuggestionCopyWith(
          _AiSuggestion value, $Res Function(_AiSuggestion) _then) =
      __$AiSuggestionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId meuId, bool isLoading, String? suggestion});

  @override
  $SfIdCopyWith<$Res> get meuId;
}

/// @nodoc
class __$AiSuggestionCopyWithImpl<$Res>
    implements _$AiSuggestionCopyWith<$Res> {
  __$AiSuggestionCopyWithImpl(this._self, this._then);

  final _AiSuggestion _self;
  final $Res Function(_AiSuggestion) _then;

  /// Create a copy of AiSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? meuId = null,
    Object? isLoading = null,
    Object? suggestion = freezed,
  }) {
    return _then(_AiSuggestion(
      meuId: null == meuId
          ? _self.meuId
          : meuId // ignore: cast_nullable_to_non_nullable
              as SfId,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      suggestion: freezed == suggestion
          ? _self.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of AiSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get meuId {
    return $SfIdCopyWith<$Res>(_self.meuId, (value) {
      return _then(_self.copyWith(meuId: value));
    });
  }
}

// dart format on

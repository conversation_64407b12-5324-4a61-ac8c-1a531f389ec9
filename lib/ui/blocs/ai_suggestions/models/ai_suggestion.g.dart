// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_suggestion.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AiSuggestion _$AiSuggestionFromJson(Map json) => $checkedCreate(
      '_AiSuggestion',
      json,
      ($checkedConvert) {
        final val = _AiSuggestion(
          meuId: $checkedConvert(
              'meuId', (v) => const ParseSfIdConverter().fromJson(v)),
          isLoading: $checkedConvert('isLoading', (v) => v as bool? ?? true),
          suggestion: $checkedConvert('suggestion', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$AiSuggestionToJson(_AiSuggestion instance) =>
    <String, dynamic>{
      if (const ParseSfIdConverter().toJson(instance.meuId) case final value?)
        'meuId': value,
      'isLoading': instance.isLoading,
      if (instance.suggestion case final value?) 'suggestion': value,
    };

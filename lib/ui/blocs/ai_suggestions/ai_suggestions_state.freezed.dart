// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_suggestions_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AiSuggestionsState {
  /// key: convoId (meuId), value: suggestion
  @JsonKey(ignore: true)
  Map<SfId, String> get suggestions;

  /// Create a copy of AiSuggestionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AiSuggestionsStateCopyWith<AiSuggestionsState> get copyWith =>
      _$AiSuggestionsStateCopyWithImpl<AiSuggestionsState>(
          this as AiSuggestionsState, _$identity);

  /// Serializes this AiSuggestionsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AiSuggestionsState &&
            const DeepCollectionEquality()
                .equals(other.suggestions, suggestions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(suggestions));

  @override
  String toString() {
    return 'AiSuggestionsState(suggestions: $suggestions)';
  }
}

/// @nodoc
abstract mixin class $AiSuggestionsStateCopyWith<$Res> {
  factory $AiSuggestionsStateCopyWith(
          AiSuggestionsState value, $Res Function(AiSuggestionsState) _then) =
      _$AiSuggestionsStateCopyWithImpl;
  @useResult
  $Res call({@JsonKey(ignore: true) Map<SfId, String> suggestions});
}

/// @nodoc
class _$AiSuggestionsStateCopyWithImpl<$Res>
    implements $AiSuggestionsStateCopyWith<$Res> {
  _$AiSuggestionsStateCopyWithImpl(this._self, this._then);

  final AiSuggestionsState _self;
  final $Res Function(AiSuggestionsState) _then;

  /// Create a copy of AiSuggestionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? suggestions = null,
  }) {
    return _then(_self.copyWith(
      suggestions: null == suggestions
          ? _self.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
    ));
  }
}

/// Adds pattern-matching-related methods to [AiSuggestionsState].
extension AiSuggestionsStatePatterns on AiSuggestionsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AiSuggestionsState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AiSuggestionsState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AiSuggestionsState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestionsState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AiSuggestionsState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestionsState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@JsonKey(ignore: true) Map<SfId, String> suggestions)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AiSuggestionsState() when $default != null:
        return $default(_that.suggestions);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@JsonKey(ignore: true) Map<SfId, String> suggestions)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestionsState():
        return $default(_that.suggestions);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@JsonKey(ignore: true) Map<SfId, String> suggestions)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AiSuggestionsState() when $default != null:
        return $default(_that.suggestions);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AiSuggestionsState extends AiSuggestionsState {
  const _AiSuggestionsState(
      {@JsonKey(ignore: true) final Map<SfId, String> suggestions = const {}})
      : _suggestions = suggestions,
        super._();
  factory _AiSuggestionsState.fromJson(Map<String, dynamic> json) =>
      _$AiSuggestionsStateFromJson(json);

  /// key: convoId (meuId), value: suggestion
  final Map<SfId, String> _suggestions;

  /// key: convoId (meuId), value: suggestion
  @override
  @JsonKey(ignore: true)
  Map<SfId, String> get suggestions {
    if (_suggestions is EqualUnmodifiableMapView) return _suggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_suggestions);
  }

  /// Create a copy of AiSuggestionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AiSuggestionsStateCopyWith<_AiSuggestionsState> get copyWith =>
      __$AiSuggestionsStateCopyWithImpl<_AiSuggestionsState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AiSuggestionsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AiSuggestionsState &&
            const DeepCollectionEquality()
                .equals(other._suggestions, _suggestions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_suggestions));

  @override
  String toString() {
    return 'AiSuggestionsState(suggestions: $suggestions)';
  }
}

/// @nodoc
abstract mixin class _$AiSuggestionsStateCopyWith<$Res>
    implements $AiSuggestionsStateCopyWith<$Res> {
  factory _$AiSuggestionsStateCopyWith(
          _AiSuggestionsState value, $Res Function(_AiSuggestionsState) _then) =
      __$AiSuggestionsStateCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(ignore: true) Map<SfId, String> suggestions});
}

/// @nodoc
class __$AiSuggestionsStateCopyWithImpl<$Res>
    implements _$AiSuggestionsStateCopyWith<$Res> {
  __$AiSuggestionsStateCopyWithImpl(this._self, this._then);

  final _AiSuggestionsState _self;
  final $Res Function(_AiSuggestionsState) _then;

  /// Create a copy of AiSuggestionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? suggestions = null,
  }) {
    return _then(_AiSuggestionsState(
      suggestions: null == suggestions
          ? _self._suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
    ));
  }
}

// dart format on

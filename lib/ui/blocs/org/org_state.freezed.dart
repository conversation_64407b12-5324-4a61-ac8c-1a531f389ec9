// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'org_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrgState {
  User? get user;
  String? get userPhotoUrl;
  bool get isLoading;

  /// Create a copy of OrgState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OrgStateCopyWith<OrgState> get copyWith =>
      _$OrgStateCopyWithImpl<OrgState>(this as OrgState, _$identity);

  /// Serializes this OrgState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OrgState &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.userPhotoUrl, userPhotoUrl) ||
                other.userPhotoUrl == userPhotoUrl) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, user, userPhotoUrl, isLoading);

  @override
  String toString() {
    return 'OrgState(user: $user, userPhotoUrl: $userPhotoUrl, isLoading: $isLoading)';
  }
}

/// @nodoc
abstract mixin class $OrgStateCopyWith<$Res> {
  factory $OrgStateCopyWith(OrgState value, $Res Function(OrgState) _then) =
      _$OrgStateCopyWithImpl;
  @useResult
  $Res call({User? user, String? userPhotoUrl, bool isLoading});

  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$OrgStateCopyWithImpl<$Res> implements $OrgStateCopyWith<$Res> {
  _$OrgStateCopyWithImpl(this._self, this._then);

  final OrgState _self;
  final $Res Function(OrgState) _then;

  /// Create a copy of OrgState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? userPhotoUrl = freezed,
    Object? isLoading = null,
  }) {
    return _then(_self.copyWith(
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _self.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of OrgState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }
}

/// Adds pattern-matching-related methods to [OrgState].
extension OrgStatePatterns on OrgState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_OrgState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OrgState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_OrgState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OrgState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_OrgState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OrgState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(User? user, String? userPhotoUrl, bool isLoading)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OrgState() when $default != null:
        return $default(_that.user, _that.userPhotoUrl, _that.isLoading);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(User? user, String? userPhotoUrl, bool isLoading) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OrgState():
        return $default(_that.user, _that.userPhotoUrl, _that.isLoading);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(User? user, String? userPhotoUrl, bool isLoading)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _OrgState() when $default != null:
        return $default(_that.user, _that.userPhotoUrl, _that.isLoading);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _OrgState extends OrgState {
  _OrgState({this.user, this.userPhotoUrl, this.isLoading = true}) : super._();
  factory _OrgState.fromJson(Map<String, dynamic> json) =>
      _$OrgStateFromJson(json);

  @override
  final User? user;
  @override
  final String? userPhotoUrl;
  @override
  @JsonKey()
  final bool isLoading;

  /// Create a copy of OrgState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OrgStateCopyWith<_OrgState> get copyWith =>
      __$OrgStateCopyWithImpl<_OrgState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OrgStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OrgState &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.userPhotoUrl, userPhotoUrl) ||
                other.userPhotoUrl == userPhotoUrl) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, user, userPhotoUrl, isLoading);

  @override
  String toString() {
    return 'OrgState(user: $user, userPhotoUrl: $userPhotoUrl, isLoading: $isLoading)';
  }
}

/// @nodoc
abstract mixin class _$OrgStateCopyWith<$Res>
    implements $OrgStateCopyWith<$Res> {
  factory _$OrgStateCopyWith(_OrgState value, $Res Function(_OrgState) _then) =
      __$OrgStateCopyWithImpl;
  @override
  @useResult
  $Res call({User? user, String? userPhotoUrl, bool isLoading});

  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$OrgStateCopyWithImpl<$Res> implements _$OrgStateCopyWith<$Res> {
  __$OrgStateCopyWithImpl(this._self, this._then);

  final _OrgState _self;
  final $Res Function(_OrgState) _then;

  /// Create a copy of OrgState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? user = freezed,
    Object? userPhotoUrl = freezed,
    Object? isLoading = null,
  }) {
    return _then(_OrgState(
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _self.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of OrgState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'org_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OrgState _$OrgStateFromJson(Map json) => $checkedCreate(
      '_OrgState',
      json,
      ($checkedConvert) {
        final val = _OrgState(
          user: $checkedConvert(
              'user',
              (v) => v == null
                  ? null
                  : User.fromJson(Map<String, dynamic>.from(v as Map))),
          userPhotoUrl: $checkedConvert('userPhotoUrl', (v) => v as String?),
          isLoading: $checkedConvert('isLoading', (v) => v as bool? ?? true),
        );
        return val;
      },
    );

Map<String, dynamic> _$OrgStateToJson(_OrgState instance) => <String, dynamic>{
      if (instance.user?.toJson() case final value?) 'user': value,
      if (instance.userPhotoUrl case final value?) 'userPhotoUrl': value,
      'isLoading': instance.isLoading,
    };

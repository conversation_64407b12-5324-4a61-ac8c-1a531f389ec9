// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingState {
  List<ShimWebsocketMessage> get websocketReceivedMessages;
  List<QueueReceiveMessage> get queueReceivedMessages;
  List<QueueSendMessage> get queueSendMessages;
  @JsonKey(ignore: true)
  WebsocketConnection get webSocketConnectionState;
  ConnectivityResult get connectivityState;

  /// Create a copy of MessagingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingStateCopyWith<MessagingState> get copyWith =>
      _$MessagingStateCopyWithImpl<MessagingState>(
          this as MessagingState, _$identity);

  /// Serializes this MessagingState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingState &&
            const DeepCollectionEquality().equals(
                other.websocketReceivedMessages, websocketReceivedMessages) &&
            const DeepCollectionEquality()
                .equals(other.queueReceivedMessages, queueReceivedMessages) &&
            const DeepCollectionEquality()
                .equals(other.queueSendMessages, queueSendMessages) &&
            (identical(
                    other.webSocketConnectionState, webSocketConnectionState) ||
                other.webSocketConnectionState == webSocketConnectionState) &&
            (identical(other.connectivityState, connectivityState) ||
                other.connectivityState == connectivityState));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(websocketReceivedMessages),
      const DeepCollectionEquality().hash(queueReceivedMessages),
      const DeepCollectionEquality().hash(queueSendMessages),
      webSocketConnectionState,
      connectivityState);

  @override
  String toString() {
    return 'MessagingState(websocketReceivedMessages: $websocketReceivedMessages, queueReceivedMessages: $queueReceivedMessages, queueSendMessages: $queueSendMessages, webSocketConnectionState: $webSocketConnectionState, connectivityState: $connectivityState)';
  }
}

/// @nodoc
abstract mixin class $MessagingStateCopyWith<$Res> {
  factory $MessagingStateCopyWith(
          MessagingState value, $Res Function(MessagingState) _then) =
      _$MessagingStateCopyWithImpl;
  @useResult
  $Res call(
      {List<ShimWebsocketMessage> websocketReceivedMessages,
      List<QueueReceiveMessage> queueReceivedMessages,
      List<QueueSendMessage> queueSendMessages,
      @JsonKey(ignore: true) WebsocketConnection webSocketConnectionState,
      ConnectivityResult connectivityState});

  $WebsocketConnectionCopyWith<$Res> get webSocketConnectionState;
}

/// @nodoc
class _$MessagingStateCopyWithImpl<$Res>
    implements $MessagingStateCopyWith<$Res> {
  _$MessagingStateCopyWithImpl(this._self, this._then);

  final MessagingState _self;
  final $Res Function(MessagingState) _then;

  /// Create a copy of MessagingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? websocketReceivedMessages = null,
    Object? queueReceivedMessages = null,
    Object? queueSendMessages = null,
    Object? webSocketConnectionState = null,
    Object? connectivityState = null,
  }) {
    return _then(_self.copyWith(
      websocketReceivedMessages: null == websocketReceivedMessages
          ? _self.websocketReceivedMessages
          : websocketReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<ShimWebsocketMessage>,
      queueReceivedMessages: null == queueReceivedMessages
          ? _self.queueReceivedMessages
          : queueReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
      queueSendMessages: null == queueSendMessages
          ? _self.queueSendMessages
          : queueSendMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueSendMessage>,
      webSocketConnectionState: null == webSocketConnectionState
          ? _self.webSocketConnectionState
          : webSocketConnectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnection,
      connectivityState: null == connectivityState
          ? _self.connectivityState
          : connectivityState // ignore: cast_nullable_to_non_nullable
              as ConnectivityResult,
    ));
  }

  /// Create a copy of MessagingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WebsocketConnectionCopyWith<$Res> get webSocketConnectionState {
    return $WebsocketConnectionCopyWith<$Res>(_self.webSocketConnectionState,
        (value) {
      return _then(_self.copyWith(webSocketConnectionState: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingState].
extension MessagingStatePatterns on MessagingState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            List<ShimWebsocketMessage> websocketReceivedMessages,
            List<QueueReceiveMessage> queueReceivedMessages,
            List<QueueSendMessage> queueSendMessages,
            @JsonKey(ignore: true) WebsocketConnection webSocketConnectionState,
            ConnectivityResult connectivityState)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingState() when $default != null:
        return $default(
            _that.websocketReceivedMessages,
            _that.queueReceivedMessages,
            _that.queueSendMessages,
            _that.webSocketConnectionState,
            _that.connectivityState);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            List<ShimWebsocketMessage> websocketReceivedMessages,
            List<QueueReceiveMessage> queueReceivedMessages,
            List<QueueSendMessage> queueSendMessages,
            @JsonKey(ignore: true) WebsocketConnection webSocketConnectionState,
            ConnectivityResult connectivityState)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingState():
        return $default(
            _that.websocketReceivedMessages,
            _that.queueReceivedMessages,
            _that.queueSendMessages,
            _that.webSocketConnectionState,
            _that.connectivityState);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            List<ShimWebsocketMessage> websocketReceivedMessages,
            List<QueueReceiveMessage> queueReceivedMessages,
            List<QueueSendMessage> queueSendMessages,
            @JsonKey(ignore: true) WebsocketConnection webSocketConnectionState,
            ConnectivityResult connectivityState)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingState() when $default != null:
        return $default(
            _that.websocketReceivedMessages,
            _that.queueReceivedMessages,
            _that.queueSendMessages,
            _that.webSocketConnectionState,
            _that.connectivityState);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingState implements MessagingState {
  const _MessagingState(
      {final List<ShimWebsocketMessage> websocketReceivedMessages =
          const <ShimWebsocketMessage>[],
      final List<QueueReceiveMessage> queueReceivedMessages =
          const <QueueReceiveMessage>[],
      final List<QueueSendMessage> queueSendMessages =
          const <QueueSendMessage>[],
      @JsonKey(ignore: true)
      this.webSocketConnectionState = const WebsocketConnection(),
      this.connectivityState = ConnectivityResult.none})
      : _websocketReceivedMessages = websocketReceivedMessages,
        _queueReceivedMessages = queueReceivedMessages,
        _queueSendMessages = queueSendMessages;
  factory _MessagingState.fromJson(Map<String, dynamic> json) =>
      _$MessagingStateFromJson(json);

  final List<ShimWebsocketMessage> _websocketReceivedMessages;
  @override
  @JsonKey()
  List<ShimWebsocketMessage> get websocketReceivedMessages {
    if (_websocketReceivedMessages is EqualUnmodifiableListView)
      return _websocketReceivedMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_websocketReceivedMessages);
  }

  final List<QueueReceiveMessage> _queueReceivedMessages;
  @override
  @JsonKey()
  List<QueueReceiveMessage> get queueReceivedMessages {
    if (_queueReceivedMessages is EqualUnmodifiableListView)
      return _queueReceivedMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_queueReceivedMessages);
  }

  final List<QueueSendMessage> _queueSendMessages;
  @override
  @JsonKey()
  List<QueueSendMessage> get queueSendMessages {
    if (_queueSendMessages is EqualUnmodifiableListView)
      return _queueSendMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_queueSendMessages);
  }

  @override
  @JsonKey(ignore: true)
  final WebsocketConnection webSocketConnectionState;
  @override
  @JsonKey()
  final ConnectivityResult connectivityState;

  /// Create a copy of MessagingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingStateCopyWith<_MessagingState> get copyWith =>
      __$MessagingStateCopyWithImpl<_MessagingState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingState &&
            const DeepCollectionEquality().equals(
                other._websocketReceivedMessages, _websocketReceivedMessages) &&
            const DeepCollectionEquality()
                .equals(other._queueReceivedMessages, _queueReceivedMessages) &&
            const DeepCollectionEquality()
                .equals(other._queueSendMessages, _queueSendMessages) &&
            (identical(
                    other.webSocketConnectionState, webSocketConnectionState) ||
                other.webSocketConnectionState == webSocketConnectionState) &&
            (identical(other.connectivityState, connectivityState) ||
                other.connectivityState == connectivityState));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_websocketReceivedMessages),
      const DeepCollectionEquality().hash(_queueReceivedMessages),
      const DeepCollectionEquality().hash(_queueSendMessages),
      webSocketConnectionState,
      connectivityState);

  @override
  String toString() {
    return 'MessagingState(websocketReceivedMessages: $websocketReceivedMessages, queueReceivedMessages: $queueReceivedMessages, queueSendMessages: $queueSendMessages, webSocketConnectionState: $webSocketConnectionState, connectivityState: $connectivityState)';
  }
}

/// @nodoc
abstract mixin class _$MessagingStateCopyWith<$Res>
    implements $MessagingStateCopyWith<$Res> {
  factory _$MessagingStateCopyWith(
          _MessagingState value, $Res Function(_MessagingState) _then) =
      __$MessagingStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<ShimWebsocketMessage> websocketReceivedMessages,
      List<QueueReceiveMessage> queueReceivedMessages,
      List<QueueSendMessage> queueSendMessages,
      @JsonKey(ignore: true) WebsocketConnection webSocketConnectionState,
      ConnectivityResult connectivityState});

  @override
  $WebsocketConnectionCopyWith<$Res> get webSocketConnectionState;
}

/// @nodoc
class __$MessagingStateCopyWithImpl<$Res>
    implements _$MessagingStateCopyWith<$Res> {
  __$MessagingStateCopyWithImpl(this._self, this._then);

  final _MessagingState _self;
  final $Res Function(_MessagingState) _then;

  /// Create a copy of MessagingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? websocketReceivedMessages = null,
    Object? queueReceivedMessages = null,
    Object? queueSendMessages = null,
    Object? webSocketConnectionState = null,
    Object? connectivityState = null,
  }) {
    return _then(_MessagingState(
      websocketReceivedMessages: null == websocketReceivedMessages
          ? _self._websocketReceivedMessages
          : websocketReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<ShimWebsocketMessage>,
      queueReceivedMessages: null == queueReceivedMessages
          ? _self._queueReceivedMessages
          : queueReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
      queueSendMessages: null == queueSendMessages
          ? _self._queueSendMessages
          : queueSendMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueSendMessage>,
      webSocketConnectionState: null == webSocketConnectionState
          ? _self.webSocketConnectionState
          : webSocketConnectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnection,
      connectivityState: null == connectivityState
          ? _self.connectivityState
          : connectivityState // ignore: cast_nullable_to_non_nullable
              as ConnectivityResult,
    ));
  }

  /// Create a copy of MessagingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WebsocketConnectionCopyWith<$Res> get webSocketConnectionState {
    return $WebsocketConnectionCopyWith<$Res>(_self.webSocketConnectionState,
        (value) {
      return _then(_self.copyWith(webSocketConnectionState: value));
    });
  }
}

// dart format on

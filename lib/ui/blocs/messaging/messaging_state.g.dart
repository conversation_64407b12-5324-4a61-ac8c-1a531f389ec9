// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingState _$MessagingStateFromJson(Map json) => $checkedCreate(
      '_MessagingState',
      json,
      ($checkedConvert) {
        final val = _MessagingState(
          websocketReceivedMessages: $checkedConvert(
              'websocketReceivedMessages',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => ShimWebsocketMessage.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <ShimWebsocketMessage>[]),
          queueReceivedMessages: $checkedConvert(
              'queueReceivedMessages',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => QueueReceiveMessage.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <QueueReceiveMessage>[]),
          queueSendMessages: $checkedConvert(
              'queueSendMessages',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => QueueSendMessage.fromJson(
                          Map<String, dynamic>.from(e as Map)))
                      .toList() ??
                  const <QueueSendMessage>[]),
          connectivityState: $checkedConvert(
              'connectivityState',
              (v) =>
                  $enumDecodeNullable(_$ConnectivityResultEnumMap, v) ??
                  ConnectivityResult.none),
        );
        return val;
      },
    );

Map<String, dynamic> _$MessagingStateToJson(_MessagingState instance) =>
    <String, dynamic>{
      'websocketReceivedMessages':
          instance.websocketReceivedMessages.map((e) => e.toJson()).toList(),
      'queueReceivedMessages':
          instance.queueReceivedMessages.map((e) => e.toJson()).toList(),
      'queueSendMessages':
          instance.queueSendMessages.map((e) => e.toJson()).toList(),
      'connectivityState':
          _$ConnectivityResultEnumMap[instance.connectivityState]!,
    };

const _$ConnectivityResultEnumMap = {
  ConnectivityResult.bluetooth: 'bluetooth',
  ConnectivityResult.wifi: 'wifi',
  ConnectivityResult.ethernet: 'ethernet',
  ConnectivityResult.mobile: 'mobile',
  ConnectivityResult.none: 'none',
  ConnectivityResult.vpn: 'vpn',
  ConnectivityResult.other: 'other',
};

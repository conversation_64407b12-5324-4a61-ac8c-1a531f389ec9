// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definitions_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingDefinitionsState {
  MessagingDefinitionStatus? get messagingDefinitionStatus;
  MessagingChannelType? get messagingType;
  SfId? get messagingEndUser;
  List<MessagingDefinition>? get filteredDefinitions;
  MessagingDefinition? get selectedMessagingDefinition;
  @JsonKey(ignore: true)
  UiEvent<Nothing>? get navigateToChat;

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingDefinitionsStateCopyWith<MessagingDefinitionsState> get copyWith =>
      _$MessagingDefinitionsStateCopyWithImpl<MessagingDefinitionsState>(
          this as MessagingDefinitionsState, _$identity);

  /// Serializes this MessagingDefinitionsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingDefinitionsState &&
            (identical(other.messagingDefinitionStatus,
                    messagingDefinitionStatus) ||
                other.messagingDefinitionStatus == messagingDefinitionStatus) &&
            (identical(other.messagingType, messagingType) ||
                other.messagingType == messagingType) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            const DeepCollectionEquality()
                .equals(other.filteredDefinitions, filteredDefinitions) &&
            (identical(other.selectedMessagingDefinition,
                    selectedMessagingDefinition) ||
                other.selectedMessagingDefinition ==
                    selectedMessagingDefinition) &&
            (identical(other.navigateToChat, navigateToChat) ||
                other.navigateToChat == navigateToChat));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      messagingDefinitionStatus,
      messagingType,
      messagingEndUser,
      const DeepCollectionEquality().hash(filteredDefinitions),
      selectedMessagingDefinition,
      navigateToChat);

  @override
  String toString() {
    return 'MessagingDefinitionsState(messagingDefinitionStatus: $messagingDefinitionStatus, messagingType: $messagingType, messagingEndUser: $messagingEndUser, filteredDefinitions: $filteredDefinitions, selectedMessagingDefinition: $selectedMessagingDefinition, navigateToChat: $navigateToChat)';
  }
}

/// @nodoc
abstract mixin class $MessagingDefinitionsStateCopyWith<$Res> {
  factory $MessagingDefinitionsStateCopyWith(MessagingDefinitionsState value,
          $Res Function(MessagingDefinitionsState) _then) =
      _$MessagingDefinitionsStateCopyWithImpl;
  @useResult
  $Res call(
      {MessagingDefinitionStatus? messagingDefinitionStatus,
      MessagingChannelType? messagingType,
      SfId? messagingEndUser,
      List<MessagingDefinition>? filteredDefinitions,
      MessagingDefinition? selectedMessagingDefinition,
      @JsonKey(ignore: true) UiEvent<Nothing>? navigateToChat});

  $MessagingDefinitionStatusCopyWith<$Res>? get messagingDefinitionStatus;
  $SfIdCopyWith<$Res>? get messagingEndUser;
  $MessagingDefinitionCopyWith<$Res>? get selectedMessagingDefinition;
}

/// @nodoc
class _$MessagingDefinitionsStateCopyWithImpl<$Res>
    implements $MessagingDefinitionsStateCopyWith<$Res> {
  _$MessagingDefinitionsStateCopyWithImpl(this._self, this._then);

  final MessagingDefinitionsState _self;
  final $Res Function(MessagingDefinitionsState) _then;

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingDefinitionStatus = freezed,
    Object? messagingType = freezed,
    Object? messagingEndUser = freezed,
    Object? filteredDefinitions = freezed,
    Object? selectedMessagingDefinition = freezed,
    Object? navigateToChat = freezed,
  }) {
    return _then(_self.copyWith(
      messagingDefinitionStatus: freezed == messagingDefinitionStatus
          ? _self.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus?,
      messagingType: freezed == messagingType
          ? _self.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      messagingEndUser: freezed == messagingEndUser
          ? _self.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as SfId?,
      filteredDefinitions: freezed == filteredDefinitions
          ? _self.filteredDefinitions
          : filteredDefinitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
      selectedMessagingDefinition: freezed == selectedMessagingDefinition
          ? _self.selectedMessagingDefinition
          : selectedMessagingDefinition // ignore: cast_nullable_to_non_nullable
              as MessagingDefinition?,
      navigateToChat: freezed == navigateToChat
          ? _self.navigateToChat
          : navigateToChat // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionStatusCopyWith<$Res>? get messagingDefinitionStatus {
    if (_self.messagingDefinitionStatus == null) {
      return null;
    }

    return $MessagingDefinitionStatusCopyWith<$Res>(
        _self.messagingDefinitionStatus!, (value) {
      return _then(_self.copyWith(messagingDefinitionStatus: value));
    });
  }

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUser {
    if (_self.messagingEndUser == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUser!, (value) {
      return _then(_self.copyWith(messagingEndUser: value));
    });
  }

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionCopyWith<$Res>? get selectedMessagingDefinition {
    if (_self.selectedMessagingDefinition == null) {
      return null;
    }

    return $MessagingDefinitionCopyWith<$Res>(
        _self.selectedMessagingDefinition!, (value) {
      return _then(_self.copyWith(selectedMessagingDefinition: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingDefinitionsState].
extension MessagingDefinitionsStatePatterns on MessagingDefinitionsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingDefinitionsState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionsState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingDefinitionsState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionsState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingDefinitionsState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionsState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            MessagingDefinitionStatus? messagingDefinitionStatus,
            MessagingChannelType? messagingType,
            SfId? messagingEndUser,
            List<MessagingDefinition>? filteredDefinitions,
            MessagingDefinition? selectedMessagingDefinition,
            @JsonKey(ignore: true) UiEvent<Nothing>? navigateToChat)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionsState() when $default != null:
        return $default(
            _that.messagingDefinitionStatus,
            _that.messagingType,
            _that.messagingEndUser,
            _that.filteredDefinitions,
            _that.selectedMessagingDefinition,
            _that.navigateToChat);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            MessagingDefinitionStatus? messagingDefinitionStatus,
            MessagingChannelType? messagingType,
            SfId? messagingEndUser,
            List<MessagingDefinition>? filteredDefinitions,
            MessagingDefinition? selectedMessagingDefinition,
            @JsonKey(ignore: true) UiEvent<Nothing>? navigateToChat)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionsState():
        return $default(
            _that.messagingDefinitionStatus,
            _that.messagingType,
            _that.messagingEndUser,
            _that.filteredDefinitions,
            _that.selectedMessagingDefinition,
            _that.navigateToChat);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            MessagingDefinitionStatus? messagingDefinitionStatus,
            MessagingChannelType? messagingType,
            SfId? messagingEndUser,
            List<MessagingDefinition>? filteredDefinitions,
            MessagingDefinition? selectedMessagingDefinition,
            @JsonKey(ignore: true) UiEvent<Nothing>? navigateToChat)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingDefinitionsState() when $default != null:
        return $default(
            _that.messagingDefinitionStatus,
            _that.messagingType,
            _that.messagingEndUser,
            _that.filteredDefinitions,
            _that.selectedMessagingDefinition,
            _that.navigateToChat);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingDefinitionsState implements MessagingDefinitionsState {
  const _MessagingDefinitionsState(
      {this.messagingDefinitionStatus,
      this.messagingType,
      this.messagingEndUser,
      final List<MessagingDefinition>? filteredDefinitions,
      this.selectedMessagingDefinition,
      @JsonKey(ignore: true) this.navigateToChat})
      : _filteredDefinitions = filteredDefinitions;
  factory _MessagingDefinitionsState.fromJson(Map<String, dynamic> json) =>
      _$MessagingDefinitionsStateFromJson(json);

  @override
  final MessagingDefinitionStatus? messagingDefinitionStatus;
  @override
  final MessagingChannelType? messagingType;
  @override
  final SfId? messagingEndUser;
  final List<MessagingDefinition>? _filteredDefinitions;
  @override
  List<MessagingDefinition>? get filteredDefinitions {
    final value = _filteredDefinitions;
    if (value == null) return null;
    if (_filteredDefinitions is EqualUnmodifiableListView)
      return _filteredDefinitions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final MessagingDefinition? selectedMessagingDefinition;
  @override
  @JsonKey(ignore: true)
  final UiEvent<Nothing>? navigateToChat;

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingDefinitionsStateCopyWith<_MessagingDefinitionsState>
      get copyWith =>
          __$MessagingDefinitionsStateCopyWithImpl<_MessagingDefinitionsState>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingDefinitionsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingDefinitionsState &&
            (identical(other.messagingDefinitionStatus,
                    messagingDefinitionStatus) ||
                other.messagingDefinitionStatus == messagingDefinitionStatus) &&
            (identical(other.messagingType, messagingType) ||
                other.messagingType == messagingType) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            const DeepCollectionEquality()
                .equals(other._filteredDefinitions, _filteredDefinitions) &&
            (identical(other.selectedMessagingDefinition,
                    selectedMessagingDefinition) ||
                other.selectedMessagingDefinition ==
                    selectedMessagingDefinition) &&
            (identical(other.navigateToChat, navigateToChat) ||
                other.navigateToChat == navigateToChat));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      messagingDefinitionStatus,
      messagingType,
      messagingEndUser,
      const DeepCollectionEquality().hash(_filteredDefinitions),
      selectedMessagingDefinition,
      navigateToChat);

  @override
  String toString() {
    return 'MessagingDefinitionsState(messagingDefinitionStatus: $messagingDefinitionStatus, messagingType: $messagingType, messagingEndUser: $messagingEndUser, filteredDefinitions: $filteredDefinitions, selectedMessagingDefinition: $selectedMessagingDefinition, navigateToChat: $navigateToChat)';
  }
}

/// @nodoc
abstract mixin class _$MessagingDefinitionsStateCopyWith<$Res>
    implements $MessagingDefinitionsStateCopyWith<$Res> {
  factory _$MessagingDefinitionsStateCopyWith(_MessagingDefinitionsState value,
          $Res Function(_MessagingDefinitionsState) _then) =
      __$MessagingDefinitionsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {MessagingDefinitionStatus? messagingDefinitionStatus,
      MessagingChannelType? messagingType,
      SfId? messagingEndUser,
      List<MessagingDefinition>? filteredDefinitions,
      MessagingDefinition? selectedMessagingDefinition,
      @JsonKey(ignore: true) UiEvent<Nothing>? navigateToChat});

  @override
  $MessagingDefinitionStatusCopyWith<$Res>? get messagingDefinitionStatus;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUser;
  @override
  $MessagingDefinitionCopyWith<$Res>? get selectedMessagingDefinition;
}

/// @nodoc
class __$MessagingDefinitionsStateCopyWithImpl<$Res>
    implements _$MessagingDefinitionsStateCopyWith<$Res> {
  __$MessagingDefinitionsStateCopyWithImpl(this._self, this._then);

  final _MessagingDefinitionsState _self;
  final $Res Function(_MessagingDefinitionsState) _then;

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? messagingDefinitionStatus = freezed,
    Object? messagingType = freezed,
    Object? messagingEndUser = freezed,
    Object? filteredDefinitions = freezed,
    Object? selectedMessagingDefinition = freezed,
    Object? navigateToChat = freezed,
  }) {
    return _then(_MessagingDefinitionsState(
      messagingDefinitionStatus: freezed == messagingDefinitionStatus
          ? _self.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus?,
      messagingType: freezed == messagingType
          ? _self.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      messagingEndUser: freezed == messagingEndUser
          ? _self.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as SfId?,
      filteredDefinitions: freezed == filteredDefinitions
          ? _self._filteredDefinitions
          : filteredDefinitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
      selectedMessagingDefinition: freezed == selectedMessagingDefinition
          ? _self.selectedMessagingDefinition
          : selectedMessagingDefinition // ignore: cast_nullable_to_non_nullable
              as MessagingDefinition?,
      navigateToChat: freezed == navigateToChat
          ? _self.navigateToChat
          : navigateToChat // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionStatusCopyWith<$Res>? get messagingDefinitionStatus {
    if (_self.messagingDefinitionStatus == null) {
      return null;
    }

    return $MessagingDefinitionStatusCopyWith<$Res>(
        _self.messagingDefinitionStatus!, (value) {
      return _then(_self.copyWith(messagingDefinitionStatus: value));
    });
  }

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUser {
    if (_self.messagingEndUser == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_self.messagingEndUser!, (value) {
      return _then(_self.copyWith(messagingEndUser: value));
    });
  }

  /// Create a copy of MessagingDefinitionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionCopyWith<$Res>? get selectedMessagingDefinition {
    if (_self.selectedMessagingDefinition == null) {
      return null;
    }

    return $MessagingDefinitionCopyWith<$Res>(
        _self.selectedMessagingDefinition!, (value) {
      return _then(_self.copyWith(selectedMessagingDefinition: value));
    });
  }
}

// dart format on

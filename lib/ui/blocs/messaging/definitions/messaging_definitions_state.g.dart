// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_definitions_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingDefinitionsState _$MessagingDefinitionsStateFromJson(Map json) =>
    $checkedCreate(
      '_MessagingDefinitionsState',
      json,
      ($checkedConvert) {
        final val = _MessagingDefinitionsState(
          messagingDefinitionStatus: $checkedConvert(
              'messagingDefinitionStatus',
              (v) => v == null
                  ? null
                  : MessagingDefinitionStatus.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          messagingType: $checkedConvert('messagingType',
              (v) => $enumDecodeNullable(_$MessagingChannelTypeEnumMap, v)),
          messagingEndUser: $checkedConvert(
              'messagingEndUser',
              (v) => v == null
                  ? null
                  : SfId.fromJson(Map<String, dynamic>.from(v as Map))),
          filteredDefinitions: $checkedConvert(
              'filteredDefinitions',
              (v) => (v as List<dynamic>?)
                  ?.map((e) => MessagingDefinition.fromJson(
                      Map<String, dynamic>.from(e as Map)))
                  .toList()),
          selectedMessagingDefinition: $checkedConvert(
              'selectedMessagingDefinition',
              (v) => v == null
                  ? null
                  : MessagingDefinition.fromJson(
                      Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$MessagingDefinitionsStateToJson(
        _MessagingDefinitionsState instance) =>
    <String, dynamic>{
      if (instance.messagingDefinitionStatus?.toJson() case final value?)
        'messagingDefinitionStatus': value,
      if (_$MessagingChannelTypeEnumMap[instance.messagingType]
          case final value?)
        'messagingType': value,
      if (instance.messagingEndUser?.toJson() case final value?)
        'messagingEndUser': value,
      if (instance.filteredDefinitions?.map((e) => e.toJson()).toList()
          case final value?)
        'filteredDefinitions': value,
      if (instance.selectedMessagingDefinition?.toJson() case final value?)
        'selectedMessagingDefinition': value,
    };

const _$MessagingChannelTypeEnumMap = {
  MessagingChannelType.whatsApp: 'whatsApp',
  MessagingChannelType.text: 'text',
};

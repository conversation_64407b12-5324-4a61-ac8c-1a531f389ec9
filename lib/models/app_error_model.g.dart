// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_error_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppError _$AppErrorFromJson(Map json) => $checkedCreate(
      '_AppError',
      json,
      ($checkedConvert) {
        final val = _AppError(
          message: $checkedConvert('message', (v) => v as String?),
          details: $checkedConvert('details', (v) => v as String?),
          iconPath: $checkedConvert('iconPath', (v) => v as String?),
          statusCode:
              $checkedConvert('statusCode', (v) => (v as num?)?.toInt()),
          isAppException:
              $checkedConvert('isAppException', (v) => v as bool? ?? false),
          showErrorOverlay:
              $checkedConvert('showErrorOverlay', (v) => v as bool? ?? false),
          shouldForceLogout:
              $checkedConvert('shouldForceLogout', (v) => v as bool? ?? false),
          shouldShowSystemNotification: $checkedConvert(
              'shouldShowSystemNotification', (v) => v as bool? ?? false),
          isReplaceable:
              $checkedConvert('isReplaceable', (v) => v as bool? ?? false),
        );
        return val;
      },
    );

Map<String, dynamic> _$AppErrorToJson(_AppError instance) => <String, dynamic>{
      if (instance.message case final value?) 'message': value,
      if (instance.details case final value?) 'details': value,
      if (instance.iconPath case final value?) 'iconPath': value,
      if (instance.statusCode case final value?) 'statusCode': value,
      'isAppException': instance.isAppException,
      'showErrorOverlay': instance.showErrorOverlay,
      'shouldForceLogout': instance.shouldForceLogout,
      'shouldShowSystemNotification': instance.shouldShowSystemNotification,
      'isReplaceable': instance.isReplaceable,
    };

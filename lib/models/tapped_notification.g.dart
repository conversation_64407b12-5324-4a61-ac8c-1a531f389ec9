// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tapped_notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TappedNotification _$TappedNotificationFromJson(Map json) => $checkedCreate(
      '_TappedNotification',
      json,
      ($checkedConvert) {
        final val = _TappedNotification(
          notificationId:
              $checkedConvert('notificationId', (v) => v as String?),
          conversationId:
              $checkedConvert('conversationId', (v) => v as String?),
          categoryId: $checkedConvert('categoryId', (v) => v as String?),
          action: $checkedConvert('action', (v) => v as String?),
          payload: $checkedConvert(
              'payload',
              (v) => (v as Map?)?.map(
                    (k, e) => MapEntry(k as String, e),
                  )),
          queueReceiveMessage: $checkedConvert(
              'queueReceiveMessage',
              (v) => v == null
                  ? null
                  : QueueReceiveMessage.fromJson(
                      Map<String, dynamic>.from(v as Map))),
        );
        return val;
      },
    );

Map<String, dynamic> _$TappedNotificationToJson(_TappedNotification instance) =>
    <String, dynamic>{
      if (instance.notificationId case final value?) 'notificationId': value,
      if (instance.conversationId case final value?) 'conversationId': value,
      if (instance.categoryId case final value?) 'categoryId': value,
      if (instance.action case final value?) 'action': value,
      if (instance.payload case final value?) 'payload': value,
      if (instance.queueReceiveMessage?.toJson() case final value?)
        'queueReceiveMessage': value,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_session_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MessagingSession _$MessagingSessionFromJson(Map json) => $checkedCreate(
      '_MessagingSession',
      json,
      ($checkedConvert) {
        final val = _MessagingSession(
          id: $checkedConvert(
              'id', (v) => const ParseSfIdConverter().fromJson(v)),
          channelName: $checkedConvert('channelName', (v) => v as String?),
          channelType: $checkedConvert('channelType', (v) => v as String?),
          caseId: $checkedConvert('caseId', (v) => v as String?),
          leadId: $checkedConvert('leadId', (v) => v as String?),
          opportunityId: $checkedConvert('opportunityId', (v) => v as String?),
          conversationId:
              $checkedConvert('conversationId', (v) => v as String?),
          createdDate: $checkedConvert('createdDate',
              (v) => v == null ? null : DateTime.parse(v as String)),
          ownerId: $checkedConvert('ownerId', (v) => v as String?),
          status: $checkedConvert('status',
              (v) => $enumDecodeNullable(_$MessagingSessionStatusEnumMap, v)),
          endUserContact: $checkedConvert(
              'endUserContact',
              (v) => v == null
                  ? null
                  : EndUserContact.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          messagingEndUser: $checkedConvert(
              'messagingEndUser',
              (v) => v == null
                  ? null
                  : MessagingEndUser.fromJson(
                      Map<String, dynamic>.from(v as Map))),
          messagingChannelId:
              $checkedConvert('messagingChannelId', (v) => v as String?),
        );
        return val;
      },
    );

Map<String, dynamic> _$MessagingSessionToJson(_MessagingSession instance) =>
    <String, dynamic>{
      if (const ParseSfIdConverter().toJson(instance.id) case final value?)
        'id': value,
      if (instance.channelName case final value?) 'channelName': value,
      if (instance.channelType case final value?) 'channelType': value,
      if (instance.caseId case final value?) 'caseId': value,
      if (instance.leadId case final value?) 'leadId': value,
      if (instance.opportunityId case final value?) 'opportunityId': value,
      if (instance.conversationId case final value?) 'conversationId': value,
      if (instance.createdDate?.toIso8601String() case final value?)
        'createdDate': value,
      if (instance.ownerId case final value?) 'ownerId': value,
      if (_$MessagingSessionStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.endUserContact?.toJson() case final value?)
        'endUserContact': value,
      if (instance.messagingEndUser?.toJson() case final value?)
        'messagingEndUser': value,
      if (instance.messagingChannelId case final value?)
        'messagingChannelId': value,
    };

const _$MessagingSessionStatusEnumMap = {
  MessagingSessionStatus.created: 'created',
  MessagingSessionStatus.active: 'active',
  MessagingSessionStatus.consent: 'consent',
  MessagingSessionStatus.waiting: 'waiting',
  MessagingSessionStatus.paused: 'paused',
  MessagingSessionStatus.inactive: 'inactive',
  MessagingSessionStatus.ended: 'ended',
  MessagingSessionStatus.error: 'error',
};

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$User {
  @Json<PERSON>ey(name: 'Id')
  String? get id;
  @JsonKey(name: 'Userna<PERSON>')
  String? get username;
  @JsonKey(name: 'LastName')
  String? get lastName;
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @JsonKey(name: 'Name')
  String? get name;
  @JsonKey(name: 'Street')
  String? get street;
  @JsonKey(name: 'City')
  String? get city;
  @JsonKey(name: 'State')
  String? get state;
  @JsonKey(name: 'PostalCode')
  String? get postalCode;
  @JsonKey(name: 'Country')
  String? get country;
  @JsonKey(name: 'Email')
  String? get email;
  @JsonKey(name: 'Phone')
  String? get phone;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @JsonKey(name: 'Alias')
  String? get alias;
  @JsonKey(name: 'Local_SMS_Channel_Id__c')
  String? get localSMSChannelId;
  @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
  String? get localWhatsAppChannelId;
  @JsonKey(name: 'ContactId')
  String? get contactId;
  @JsonKey(name: 'AccountId')
  String? get accountId;
  @JsonKey(name: 'FullPhotoUrl')
  String? get fullPhotoUrl;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserCopyWith<User> get copyWith =>
      _$UserCopyWithImpl<User>(this as User, _$identity);

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is User &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.alias, alias) || other.alias == alias) &&
            (identical(other.localSMSChannelId, localSMSChannelId) ||
                other.localSMSChannelId == localSMSChannelId) &&
            (identical(other.localWhatsAppChannelId, localWhatsAppChannelId) ||
                other.localWhatsAppChannelId == localWhatsAppChannelId) &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.fullPhotoUrl, fullPhotoUrl) ||
                other.fullPhotoUrl == fullPhotoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        username,
        lastName,
        firstName,
        name,
        street,
        city,
        state,
        postalCode,
        country,
        email,
        phone,
        mobilePhone,
        alias,
        localSMSChannelId,
        localWhatsAppChannelId,
        contactId,
        accountId,
        fullPhotoUrl
      ]);

  @override
  String toString() {
    return 'User(id: $id, username: $username, lastName: $lastName, firstName: $firstName, name: $name, street: $street, city: $city, state: $state, postalCode: $postalCode, country: $country, email: $email, phone: $phone, mobilePhone: $mobilePhone, alias: $alias, localSMSChannelId: $localSMSChannelId, localWhatsAppChannelId: $localWhatsAppChannelId, contactId: $contactId, accountId: $accountId, fullPhotoUrl: $fullPhotoUrl)';
  }
}

/// @nodoc
abstract mixin class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) _then) =
      _$UserCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Username') String? username,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Street') String? street,
      @JsonKey(name: 'City') String? city,
      @JsonKey(name: 'State') String? state,
      @JsonKey(name: 'PostalCode') String? postalCode,
      @JsonKey(name: 'Country') String? country,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Alias') String? alias,
      @JsonKey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
      @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
      String? localWhatsAppChannelId,
      @JsonKey(name: 'ContactId') String? contactId,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl});
}

/// @nodoc
class _$UserCopyWithImpl<$Res> implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._self, this._then);

  final User _self;
  final $Res Function(User) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
    Object? street = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? alias = freezed,
    Object? localSMSChannelId = freezed,
    Object? localWhatsAppChannelId = freezed,
    Object? contactId = freezed,
    Object? accountId = freezed,
    Object? fullPhotoUrl = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _self.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _self.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _self.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      alias: freezed == alias
          ? _self.alias
          : alias // ignore: cast_nullable_to_non_nullable
              as String?,
      localSMSChannelId: freezed == localSMSChannelId
          ? _self.localSMSChannelId
          : localSMSChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      localWhatsAppChannelId: freezed == localWhatsAppChannelId
          ? _self.localWhatsAppChannelId
          : localWhatsAppChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      contactId: freezed == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _self.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      fullPhotoUrl: freezed == fullPhotoUrl
          ? _self.fullPhotoUrl
          : fullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [User].
extension UserPatterns on User {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_User value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _User() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_User value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _User():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_User value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _User() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Username') String? username,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'Street') String? street,
            @JsonKey(name: 'City') String? city,
            @JsonKey(name: 'State') String? state,
            @JsonKey(name: 'PostalCode') String? postalCode,
            @JsonKey(name: 'Country') String? country,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'Phone') String? phone,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Alias') String? alias,
            @JsonKey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
            @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
            String? localWhatsAppChannelId,
            @JsonKey(name: 'ContactId') String? contactId,
            @JsonKey(name: 'AccountId') String? accountId,
            @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _User() when $default != null:
        return $default(
            _that.id,
            _that.username,
            _that.lastName,
            _that.firstName,
            _that.name,
            _that.street,
            _that.city,
            _that.state,
            _that.postalCode,
            _that.country,
            _that.email,
            _that.phone,
            _that.mobilePhone,
            _that.alias,
            _that.localSMSChannelId,
            _that.localWhatsAppChannelId,
            _that.contactId,
            _that.accountId,
            _that.fullPhotoUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Username') String? username,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'Street') String? street,
            @JsonKey(name: 'City') String? city,
            @JsonKey(name: 'State') String? state,
            @JsonKey(name: 'PostalCode') String? postalCode,
            @JsonKey(name: 'Country') String? country,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'Phone') String? phone,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Alias') String? alias,
            @JsonKey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
            @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
            String? localWhatsAppChannelId,
            @JsonKey(name: 'ContactId') String? contactId,
            @JsonKey(name: 'AccountId') String? accountId,
            @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _User():
        return $default(
            _that.id,
            _that.username,
            _that.lastName,
            _that.firstName,
            _that.name,
            _that.street,
            _that.city,
            _that.state,
            _that.postalCode,
            _that.country,
            _that.email,
            _that.phone,
            _that.mobilePhone,
            _that.alias,
            _that.localSMSChannelId,
            _that.localWhatsAppChannelId,
            _that.contactId,
            _that.accountId,
            _that.fullPhotoUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'Id') String? id,
            @JsonKey(name: 'Username') String? username,
            @JsonKey(name: 'LastName') String? lastName,
            @JsonKey(name: 'FirstName') String? firstName,
            @JsonKey(name: 'Name') String? name,
            @JsonKey(name: 'Street') String? street,
            @JsonKey(name: 'City') String? city,
            @JsonKey(name: 'State') String? state,
            @JsonKey(name: 'PostalCode') String? postalCode,
            @JsonKey(name: 'Country') String? country,
            @JsonKey(name: 'Email') String? email,
            @JsonKey(name: 'Phone') String? phone,
            @JsonKey(name: 'MobilePhone') String? mobilePhone,
            @JsonKey(name: 'Alias') String? alias,
            @JsonKey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
            @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
            String? localWhatsAppChannelId,
            @JsonKey(name: 'ContactId') String? contactId,
            @JsonKey(name: 'AccountId') String? accountId,
            @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _User() when $default != null:
        return $default(
            _that.id,
            _that.username,
            _that.lastName,
            _that.firstName,
            _that.name,
            _that.street,
            _that.city,
            _that.state,
            _that.postalCode,
            _that.country,
            _that.email,
            _that.phone,
            _that.mobilePhone,
            _that.alias,
            _that.localSMSChannelId,
            _that.localWhatsAppChannelId,
            _that.contactId,
            _that.accountId,
            _that.fullPhotoUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _User implements User {
  const _User(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'Username') this.username,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'Name') this.name,
      @JsonKey(name: 'Street') this.street,
      @JsonKey(name: 'City') this.city,
      @JsonKey(name: 'State') this.state,
      @JsonKey(name: 'PostalCode') this.postalCode,
      @JsonKey(name: 'Country') this.country,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'Phone') this.phone,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'Alias') this.alias,
      @JsonKey(name: 'Local_SMS_Channel_Id__c') this.localSMSChannelId,
      @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
      this.localWhatsAppChannelId,
      @JsonKey(name: 'ContactId') this.contactId,
      @JsonKey(name: 'AccountId') this.accountId,
      @JsonKey(name: 'FullPhotoUrl') this.fullPhotoUrl});
  factory _User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'Username')
  final String? username;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'Name')
  final String? name;
  @override
  @JsonKey(name: 'Street')
  final String? street;
  @override
  @JsonKey(name: 'City')
  final String? city;
  @override
  @JsonKey(name: 'State')
  final String? state;
  @override
  @JsonKey(name: 'PostalCode')
  final String? postalCode;
  @override
  @JsonKey(name: 'Country')
  final String? country;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'Phone')
  final String? phone;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'Alias')
  final String? alias;
  @override
  @JsonKey(name: 'Local_SMS_Channel_Id__c')
  final String? localSMSChannelId;
  @override
  @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
  final String? localWhatsAppChannelId;
  @override
  @JsonKey(name: 'ContactId')
  final String? contactId;
  @override
  @JsonKey(name: 'AccountId')
  final String? accountId;
  @override
  @JsonKey(name: 'FullPhotoUrl')
  final String? fullPhotoUrl;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserCopyWith<_User> get copyWith =>
      __$UserCopyWithImpl<_User>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _User &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.alias, alias) || other.alias == alias) &&
            (identical(other.localSMSChannelId, localSMSChannelId) ||
                other.localSMSChannelId == localSMSChannelId) &&
            (identical(other.localWhatsAppChannelId, localWhatsAppChannelId) ||
                other.localWhatsAppChannelId == localWhatsAppChannelId) &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.fullPhotoUrl, fullPhotoUrl) ||
                other.fullPhotoUrl == fullPhotoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        username,
        lastName,
        firstName,
        name,
        street,
        city,
        state,
        postalCode,
        country,
        email,
        phone,
        mobilePhone,
        alias,
        localSMSChannelId,
        localWhatsAppChannelId,
        contactId,
        accountId,
        fullPhotoUrl
      ]);

  @override
  String toString() {
    return 'User(id: $id, username: $username, lastName: $lastName, firstName: $firstName, name: $name, street: $street, city: $city, state: $state, postalCode: $postalCode, country: $country, email: $email, phone: $phone, mobilePhone: $mobilePhone, alias: $alias, localSMSChannelId: $localSMSChannelId, localWhatsAppChannelId: $localWhatsAppChannelId, contactId: $contactId, accountId: $accountId, fullPhotoUrl: $fullPhotoUrl)';
  }
}

/// @nodoc
abstract mixin class _$UserCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$UserCopyWith(_User value, $Res Function(_User) _then) =
      __$UserCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Username') String? username,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Street') String? street,
      @JsonKey(name: 'City') String? city,
      @JsonKey(name: 'State') String? state,
      @JsonKey(name: 'PostalCode') String? postalCode,
      @JsonKey(name: 'Country') String? country,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Alias') String? alias,
      @JsonKey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
      @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
      String? localWhatsAppChannelId,
      @JsonKey(name: 'ContactId') String? contactId,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl});
}

/// @nodoc
class __$UserCopyWithImpl<$Res> implements _$UserCopyWith<$Res> {
  __$UserCopyWithImpl(this._self, this._then);

  final _User _self;
  final $Res Function(_User) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
    Object? street = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? alias = freezed,
    Object? localSMSChannelId = freezed,
    Object? localWhatsAppChannelId = freezed,
    Object? contactId = freezed,
    Object? accountId = freezed,
    Object? fullPhotoUrl = freezed,
  }) {
    return _then(_User(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _self.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _self.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _self.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _self.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      alias: freezed == alias
          ? _self.alias
          : alias // ignore: cast_nullable_to_non_nullable
              as String?,
      localSMSChannelId: freezed == localSMSChannelId
          ? _self.localSMSChannelId
          : localSMSChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      localWhatsAppChannelId: freezed == localWhatsAppChannelId
          ? _self.localWhatsAppChannelId
          : localWhatsAppChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      contactId: freezed == contactId
          ? _self.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _self.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      fullPhotoUrl: freezed == fullPhotoUrl
          ? _self.fullPhotoUrl
          : fullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppError {
  String? get message;
  String? get details;
  String? get iconPath;
  int? get statusCode;
  bool get isAppException;
  bool get showErrorOverlay;
  bool get shouldForceLogout;
  bool get shouldShowSystemNotification;

  /// if another error should take precedence and ignore this one
  bool get isReplaceable;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<AppError> get copyWith =>
      _$AppErrorCopyWithImpl<AppError>(this as AppError, _$identity);

  /// Serializes this AppError to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.isAppException, isAppException) ||
                other.isAppException == isAppException) &&
            (identical(other.showErrorOverlay, showErrorOverlay) ||
                other.showErrorOverlay == showErrorOverlay) &&
            (identical(other.shouldForceLogout, shouldForceLogout) ||
                other.shouldForceLogout == shouldForceLogout) &&
            (identical(other.shouldShowSystemNotification,
                    shouldShowSystemNotification) ||
                other.shouldShowSystemNotification ==
                    shouldShowSystemNotification) &&
            (identical(other.isReplaceable, isReplaceable) ||
                other.isReplaceable == isReplaceable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      details,
      iconPath,
      statusCode,
      isAppException,
      showErrorOverlay,
      shouldForceLogout,
      shouldShowSystemNotification,
      isReplaceable);

  @override
  String toString() {
    return 'AppError(message: $message, details: $details, iconPath: $iconPath, statusCode: $statusCode, isAppException: $isAppException, showErrorOverlay: $showErrorOverlay, shouldForceLogout: $shouldForceLogout, shouldShowSystemNotification: $shouldShowSystemNotification, isReplaceable: $isReplaceable)';
  }
}

/// @nodoc
abstract mixin class $AppErrorCopyWith<$Res> {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) _then) =
      _$AppErrorCopyWithImpl;
  @useResult
  $Res call(
      {String? message,
      String? details,
      String? iconPath,
      int? statusCode,
      bool isAppException,
      bool showErrorOverlay,
      bool shouldForceLogout,
      bool shouldShowSystemNotification,
      bool isReplaceable});
}

/// @nodoc
class _$AppErrorCopyWithImpl<$Res> implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._self, this._then);

  final AppError _self;
  final $Res Function(AppError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? details = freezed,
    Object? iconPath = freezed,
    Object? statusCode = freezed,
    Object? isAppException = null,
    Object? showErrorOverlay = null,
    Object? shouldForceLogout = null,
    Object? shouldShowSystemNotification = null,
    Object? isReplaceable = null,
  }) {
    return _then(_self.copyWith(
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _self.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      iconPath: freezed == iconPath
          ? _self.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _self.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      isAppException: null == isAppException
          ? _self.isAppException
          : isAppException // ignore: cast_nullable_to_non_nullable
              as bool,
      showErrorOverlay: null == showErrorOverlay
          ? _self.showErrorOverlay
          : showErrorOverlay // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldForceLogout: null == shouldForceLogout
          ? _self.shouldForceLogout
          : shouldForceLogout // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldShowSystemNotification: null == shouldShowSystemNotification
          ? _self.shouldShowSystemNotification
          : shouldShowSystemNotification // ignore: cast_nullable_to_non_nullable
              as bool,
      isReplaceable: null == isReplaceable
          ? _self.isReplaceable
          : isReplaceable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [AppError].
extension AppErrorPatterns on AppError {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AppError value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppError() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AppError value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppError():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AppError value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppError() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? message,
            String? details,
            String? iconPath,
            int? statusCode,
            bool isAppException,
            bool showErrorOverlay,
            bool shouldForceLogout,
            bool shouldShowSystemNotification,
            bool isReplaceable)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppError() when $default != null:
        return $default(
            _that.message,
            _that.details,
            _that.iconPath,
            _that.statusCode,
            _that.isAppException,
            _that.showErrorOverlay,
            _that.shouldForceLogout,
            _that.shouldShowSystemNotification,
            _that.isReplaceable);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? message,
            String? details,
            String? iconPath,
            int? statusCode,
            bool isAppException,
            bool showErrorOverlay,
            bool shouldForceLogout,
            bool shouldShowSystemNotification,
            bool isReplaceable)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppError():
        return $default(
            _that.message,
            _that.details,
            _that.iconPath,
            _that.statusCode,
            _that.isAppException,
            _that.showErrorOverlay,
            _that.shouldForceLogout,
            _that.shouldShowSystemNotification,
            _that.isReplaceable);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? message,
            String? details,
            String? iconPath,
            int? statusCode,
            bool isAppException,
            bool showErrorOverlay,
            bool shouldForceLogout,
            bool shouldShowSystemNotification,
            bool isReplaceable)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AppError() when $default != null:
        return $default(
            _that.message,
            _that.details,
            _that.iconPath,
            _that.statusCode,
            _that.isAppException,
            _that.showErrorOverlay,
            _that.shouldForceLogout,
            _that.shouldShowSystemNotification,
            _that.isReplaceable);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AppError extends AppError {
  const _AppError(
      {this.message,
      this.details,
      this.iconPath,
      this.statusCode,
      this.isAppException = false,
      this.showErrorOverlay = false,
      this.shouldForceLogout = false,
      this.shouldShowSystemNotification = false,
      this.isReplaceable = false})
      : super._();
  factory _AppError.fromJson(Map<String, dynamic> json) =>
      _$AppErrorFromJson(json);

  @override
  final String? message;
  @override
  final String? details;
  @override
  final String? iconPath;
  @override
  final int? statusCode;
  @override
  @JsonKey()
  final bool isAppException;
  @override
  @JsonKey()
  final bool showErrorOverlay;
  @override
  @JsonKey()
  final bool shouldForceLogout;
  @override
  @JsonKey()
  final bool shouldShowSystemNotification;

  /// if another error should take precedence and ignore this one
  @override
  @JsonKey()
  final bool isReplaceable;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AppErrorCopyWith<_AppError> get copyWith =>
      __$AppErrorCopyWithImpl<_AppError>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AppErrorToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AppError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.isAppException, isAppException) ||
                other.isAppException == isAppException) &&
            (identical(other.showErrorOverlay, showErrorOverlay) ||
                other.showErrorOverlay == showErrorOverlay) &&
            (identical(other.shouldForceLogout, shouldForceLogout) ||
                other.shouldForceLogout == shouldForceLogout) &&
            (identical(other.shouldShowSystemNotification,
                    shouldShowSystemNotification) ||
                other.shouldShowSystemNotification ==
                    shouldShowSystemNotification) &&
            (identical(other.isReplaceable, isReplaceable) ||
                other.isReplaceable == isReplaceable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      details,
      iconPath,
      statusCode,
      isAppException,
      showErrorOverlay,
      shouldForceLogout,
      shouldShowSystemNotification,
      isReplaceable);

  @override
  String toString() {
    return 'AppError(message: $message, details: $details, iconPath: $iconPath, statusCode: $statusCode, isAppException: $isAppException, showErrorOverlay: $showErrorOverlay, shouldForceLogout: $shouldForceLogout, shouldShowSystemNotification: $shouldShowSystemNotification, isReplaceable: $isReplaceable)';
  }
}

/// @nodoc
abstract mixin class _$AppErrorCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$AppErrorCopyWith(_AppError value, $Res Function(_AppError) _then) =
      __$AppErrorCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? message,
      String? details,
      String? iconPath,
      int? statusCode,
      bool isAppException,
      bool showErrorOverlay,
      bool shouldForceLogout,
      bool shouldShowSystemNotification,
      bool isReplaceable});
}

/// @nodoc
class __$AppErrorCopyWithImpl<$Res> implements _$AppErrorCopyWith<$Res> {
  __$AppErrorCopyWithImpl(this._self, this._then);

  final _AppError _self;
  final $Res Function(_AppError) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = freezed,
    Object? details = freezed,
    Object? iconPath = freezed,
    Object? statusCode = freezed,
    Object? isAppException = null,
    Object? showErrorOverlay = null,
    Object? shouldForceLogout = null,
    Object? shouldShowSystemNotification = null,
    Object? isReplaceable = null,
  }) {
    return _then(_AppError(
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _self.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      iconPath: freezed == iconPath
          ? _self.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _self.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      isAppException: null == isAppException
          ? _self.isAppException
          : isAppException // ignore: cast_nullable_to_non_nullable
              as bool,
      showErrorOverlay: null == showErrorOverlay
          ? _self.showErrorOverlay
          : showErrorOverlay // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldForceLogout: null == shouldForceLogout
          ? _self.shouldForceLogout
          : shouldForceLogout // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldShowSystemNotification: null == shouldShowSystemNotification
          ? _self.shouldShowSystemNotification
          : shouldShowSystemNotification // ignore: cast_nullable_to_non_nullable
              as bool,
      isReplaceable: null == isReplaceable
          ? _self.isReplaceable
          : isReplaceable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on

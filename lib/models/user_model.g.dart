// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_User _$UserFromJson(Map json) => $checkedCreate(
      '_User',
      json,
      ($checkedConvert) {
        final val = _User(
          id: $checkedConvert('Id', (v) => v as String?),
          username: $checkedConvert('Username', (v) => v as String?),
          lastName: $checkedConvert('LastName', (v) => v as String?),
          firstName: $checkedConvert('FirstName', (v) => v as String?),
          name: $checkedConvert('Name', (v) => v as String?),
          street: $checkedConvert('Street', (v) => v as String?),
          city: $checkedConvert('City', (v) => v as String?),
          state: $checkedConvert('State', (v) => v as String?),
          postalCode: $checkedConvert('PostalCode', (v) => v as String?),
          country: $checkedConvert('Country', (v) => v as String?),
          email: $checkedConvert('Email', (v) => v as String?),
          phone: $checkedConvert('Phone', (v) => v as String?),
          mobilePhone: $checkedConvert('MobilePhone', (v) => v as String?),
          alias: $checkedConvert('Alias', (v) => v as String?),
          localSMSChannelId:
              $checkedConvert('Local_SMS_Channel_Id__c', (v) => v as String?),
          localWhatsAppChannelId: $checkedConvert(
              'Local_WhatsApp_Channel_Id__c', (v) => v as String?),
          contactId: $checkedConvert('ContactId', (v) => v as String?),
          accountId: $checkedConvert('AccountId', (v) => v as String?),
          fullPhotoUrl: $checkedConvert('FullPhotoUrl', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'id': 'Id',
        'username': 'Username',
        'lastName': 'LastName',
        'firstName': 'FirstName',
        'name': 'Name',
        'street': 'Street',
        'city': 'City',
        'state': 'State',
        'postalCode': 'PostalCode',
        'country': 'Country',
        'email': 'Email',
        'phone': 'Phone',
        'mobilePhone': 'MobilePhone',
        'alias': 'Alias',
        'localSMSChannelId': 'Local_SMS_Channel_Id__c',
        'localWhatsAppChannelId': 'Local_WhatsApp_Channel_Id__c',
        'contactId': 'ContactId',
        'accountId': 'AccountId',
        'fullPhotoUrl': 'FullPhotoUrl'
      },
    );

Map<String, dynamic> _$UserToJson(_User instance) => <String, dynamic>{
      if (instance.id case final value?) 'Id': value,
      if (instance.username case final value?) 'Username': value,
      if (instance.lastName case final value?) 'LastName': value,
      if (instance.firstName case final value?) 'FirstName': value,
      if (instance.name case final value?) 'Name': value,
      if (instance.street case final value?) 'Street': value,
      if (instance.city case final value?) 'City': value,
      if (instance.state case final value?) 'State': value,
      if (instance.postalCode case final value?) 'PostalCode': value,
      if (instance.country case final value?) 'Country': value,
      if (instance.email case final value?) 'Email': value,
      if (instance.phone case final value?) 'Phone': value,
      if (instance.mobilePhone case final value?) 'MobilePhone': value,
      if (instance.alias case final value?) 'Alias': value,
      if (instance.localSMSChannelId case final value?)
        'Local_SMS_Channel_Id__c': value,
      if (instance.localWhatsAppChannelId case final value?)
        'Local_WhatsApp_Channel_Id__c': value,
      if (instance.contactId case final value?) 'ContactId': value,
      if (instance.accountId case final value?) 'AccountId': value,
      if (instance.fullPhotoUrl case final value?) 'FullPhotoUrl': value,
    };

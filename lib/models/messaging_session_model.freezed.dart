// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_session_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessagingSession {
  @ParseSfIdConverter()
  SfId get id;
  String? get channelName;
  String? get channelType;
  String? get caseId;
  String? get leadId;
  String? get opportunityId;

  /// NOTE: important! these "conversationId"'s are not the same as the ones in the Conversation model. Conversations use the MessagingEndUserId as the ConversationId. These ConversationIds are from SF -- they only exist for enhanced conversations, have a prefix of "0dw" and are called "conversationIdentifiers" in many places in Salesforce
  String? get conversationId;
  DateTime? get createdDate;
  String? get ownerId;
  MessagingSessionStatus? get status;
  EndUserContact? get endUserContact;
  MessagingEndUser? get messagingEndUser;
  String? get messagingChannelId;

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessagingSessionCopyWith<MessagingSession> get copyWith =>
      _$MessagingSessionCopyWithImpl<MessagingSession>(
          this as MessagingSession, _$identity);

  /// Serializes this MessagingSession to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessagingSession &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.caseId, caseId) || other.caseId == caseId) &&
            (identical(other.leadId, leadId) || other.leadId == leadId) &&
            (identical(other.opportunityId, opportunityId) ||
                other.opportunityId == opportunityId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.endUserContact, endUserContact) ||
                other.endUserContact == endUserContact) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      channelName,
      channelType,
      caseId,
      leadId,
      opportunityId,
      conversationId,
      createdDate,
      ownerId,
      status,
      endUserContact,
      messagingEndUser,
      messagingChannelId);

  @override
  String toString() {
    return 'MessagingSession(id: $id, channelName: $channelName, channelType: $channelType, caseId: $caseId, leadId: $leadId, opportunityId: $opportunityId, conversationId: $conversationId, createdDate: $createdDate, ownerId: $ownerId, status: $status, endUserContact: $endUserContact, messagingEndUser: $messagingEndUser, messagingChannelId: $messagingChannelId)';
  }
}

/// @nodoc
abstract mixin class $MessagingSessionCopyWith<$Res> {
  factory $MessagingSessionCopyWith(
          MessagingSession value, $Res Function(MessagingSession) _then) =
      _$MessagingSessionCopyWithImpl;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String? channelName,
      String? channelType,
      String? caseId,
      String? leadId,
      String? opportunityId,
      String? conversationId,
      DateTime? createdDate,
      String? ownerId,
      MessagingSessionStatus? status,
      EndUserContact? endUserContact,
      MessagingEndUser? messagingEndUser,
      String? messagingChannelId});

  $SfIdCopyWith<$Res> get id;
  $EndUserContactCopyWith<$Res>? get endUserContact;
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class _$MessagingSessionCopyWithImpl<$Res>
    implements $MessagingSessionCopyWith<$Res> {
  _$MessagingSessionCopyWithImpl(this._self, this._then);

  final MessagingSession _self;
  final $Res Function(MessagingSession) _then;

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? channelName = freezed,
    Object? channelType = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
    Object? conversationId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserContact = freezed,
    Object? messagingEndUser = freezed,
    Object? messagingChannelId = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      channelName: freezed == channelName
          ? _self.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: freezed == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _self.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _self.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _self.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserContact: freezed == endUserContact
          ? _self.endUserContact
          : endUserContact // ignore: cast_nullable_to_non_nullable
              as EndUserContact?,
      messagingEndUser: freezed == messagingEndUser
          ? _self.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      messagingChannelId: freezed == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EndUserContactCopyWith<$Res>? get endUserContact {
    if (_self.endUserContact == null) {
      return null;
    }

    return $EndUserContactCopyWith<$Res>(_self.endUserContact!, (value) {
      return _then(_self.copyWith(endUserContact: value));
    });
  }

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser {
    if (_self.messagingEndUser == null) {
      return null;
    }

    return $MessagingEndUserCopyWith<$Res>(_self.messagingEndUser!, (value) {
      return _then(_self.copyWith(messagingEndUser: value));
    });
  }
}

/// Adds pattern-matching-related methods to [MessagingSession].
extension MessagingSessionPatterns on MessagingSession {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_MessagingSession value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingSession() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_MessagingSession value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSession():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_MessagingSession value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSession() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @ParseSfIdConverter() SfId id,
            String? channelName,
            String? channelType,
            String? caseId,
            String? leadId,
            String? opportunityId,
            String? conversationId,
            DateTime? createdDate,
            String? ownerId,
            MessagingSessionStatus? status,
            EndUserContact? endUserContact,
            MessagingEndUser? messagingEndUser,
            String? messagingChannelId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _MessagingSession() when $default != null:
        return $default(
            _that.id,
            _that.channelName,
            _that.channelType,
            _that.caseId,
            _that.leadId,
            _that.opportunityId,
            _that.conversationId,
            _that.createdDate,
            _that.ownerId,
            _that.status,
            _that.endUserContact,
            _that.messagingEndUser,
            _that.messagingChannelId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @ParseSfIdConverter() SfId id,
            String? channelName,
            String? channelType,
            String? caseId,
            String? leadId,
            String? opportunityId,
            String? conversationId,
            DateTime? createdDate,
            String? ownerId,
            MessagingSessionStatus? status,
            EndUserContact? endUserContact,
            MessagingEndUser? messagingEndUser,
            String? messagingChannelId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSession():
        return $default(
            _that.id,
            _that.channelName,
            _that.channelType,
            _that.caseId,
            _that.leadId,
            _that.opportunityId,
            _that.conversationId,
            _that.createdDate,
            _that.ownerId,
            _that.status,
            _that.endUserContact,
            _that.messagingEndUser,
            _that.messagingChannelId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @ParseSfIdConverter() SfId id,
            String? channelName,
            String? channelType,
            String? caseId,
            String? leadId,
            String? opportunityId,
            String? conversationId,
            DateTime? createdDate,
            String? ownerId,
            MessagingSessionStatus? status,
            EndUserContact? endUserContact,
            MessagingEndUser? messagingEndUser,
            String? messagingChannelId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _MessagingSession() when $default != null:
        return $default(
            _that.id,
            _that.channelName,
            _that.channelType,
            _that.caseId,
            _that.leadId,
            _that.opportunityId,
            _that.conversationId,
            _that.createdDate,
            _that.ownerId,
            _that.status,
            _that.endUserContact,
            _that.messagingEndUser,
            _that.messagingChannelId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _MessagingSession extends MessagingSession {
  const _MessagingSession(
      {@ParseSfIdConverter() required this.id,
      this.channelName,
      this.channelType,
      this.caseId,
      this.leadId,
      this.opportunityId,
      this.conversationId,
      this.createdDate,
      this.ownerId,
      this.status,
      this.endUserContact,
      this.messagingEndUser,
      this.messagingChannelId})
      : super._();
  factory _MessagingSession.fromJson(Map<String, dynamic> json) =>
      _$MessagingSessionFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId id;
  @override
  final String? channelName;
  @override
  final String? channelType;
  @override
  final String? caseId;
  @override
  final String? leadId;
  @override
  final String? opportunityId;

  /// NOTE: important! these "conversationId"'s are not the same as the ones in the Conversation model. Conversations use the MessagingEndUserId as the ConversationId. These ConversationIds are from SF -- they only exist for enhanced conversations, have a prefix of "0dw" and are called "conversationIdentifiers" in many places in Salesforce
  @override
  final String? conversationId;
  @override
  final DateTime? createdDate;
  @override
  final String? ownerId;
  @override
  final MessagingSessionStatus? status;
  @override
  final EndUserContact? endUserContact;
  @override
  final MessagingEndUser? messagingEndUser;
  @override
  final String? messagingChannelId;

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessagingSessionCopyWith<_MessagingSession> get copyWith =>
      __$MessagingSessionCopyWithImpl<_MessagingSession>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessagingSessionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessagingSession &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.caseId, caseId) || other.caseId == caseId) &&
            (identical(other.leadId, leadId) || other.leadId == leadId) &&
            (identical(other.opportunityId, opportunityId) ||
                other.opportunityId == opportunityId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.endUserContact, endUserContact) ||
                other.endUserContact == endUserContact) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      channelName,
      channelType,
      caseId,
      leadId,
      opportunityId,
      conversationId,
      createdDate,
      ownerId,
      status,
      endUserContact,
      messagingEndUser,
      messagingChannelId);

  @override
  String toString() {
    return 'MessagingSession(id: $id, channelName: $channelName, channelType: $channelType, caseId: $caseId, leadId: $leadId, opportunityId: $opportunityId, conversationId: $conversationId, createdDate: $createdDate, ownerId: $ownerId, status: $status, endUserContact: $endUserContact, messagingEndUser: $messagingEndUser, messagingChannelId: $messagingChannelId)';
  }
}

/// @nodoc
abstract mixin class _$MessagingSessionCopyWith<$Res>
    implements $MessagingSessionCopyWith<$Res> {
  factory _$MessagingSessionCopyWith(
          _MessagingSession value, $Res Function(_MessagingSession) _then) =
      __$MessagingSessionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String? channelName,
      String? channelType,
      String? caseId,
      String? leadId,
      String? opportunityId,
      String? conversationId,
      DateTime? createdDate,
      String? ownerId,
      MessagingSessionStatus? status,
      EndUserContact? endUserContact,
      MessagingEndUser? messagingEndUser,
      String? messagingChannelId});

  @override
  $SfIdCopyWith<$Res> get id;
  @override
  $EndUserContactCopyWith<$Res>? get endUserContact;
  @override
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class __$MessagingSessionCopyWithImpl<$Res>
    implements _$MessagingSessionCopyWith<$Res> {
  __$MessagingSessionCopyWithImpl(this._self, this._then);

  final _MessagingSession _self;
  final $Res Function(_MessagingSession) _then;

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? channelName = freezed,
    Object? channelType = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
    Object? conversationId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserContact = freezed,
    Object? messagingEndUser = freezed,
    Object? messagingChannelId = freezed,
  }) {
    return _then(_MessagingSession(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      channelName: freezed == channelName
          ? _self.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: freezed == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _self.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _self.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _self.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _self.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserContact: freezed == endUserContact
          ? _self.endUserContact
          : endUserContact // ignore: cast_nullable_to_non_nullable
              as EndUserContact?,
      messagingEndUser: freezed == messagingEndUser
          ? _self.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      messagingChannelId: freezed == messagingChannelId
          ? _self.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_self.id, (value) {
      return _then(_self.copyWith(id: value));
    });
  }

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EndUserContactCopyWith<$Res>? get endUserContact {
    if (_self.endUserContact == null) {
      return null;
    }

    return $EndUserContactCopyWith<$Res>(_self.endUserContact!, (value) {
      return _then(_self.copyWith(endUserContact: value));
    });
  }

  /// Create a copy of MessagingSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser {
    if (_self.messagingEndUser == null) {
      return null;
    }

    return $MessagingEndUserCopyWith<$Res>(_self.messagingEndUser!, (value) {
      return _then(_self.copyWith(messagingEndUser: value));
    });
  }
}

// dart format on

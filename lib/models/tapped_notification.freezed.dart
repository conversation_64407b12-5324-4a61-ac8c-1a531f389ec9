// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tapped_notification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TappedNotification {
  String? get notificationId;
  String? get conversationId;
  String? get categoryId;
  String? get action;
  Map<String, dynamic>? get payload;
  QueueReceiveMessage? get queueReceiveMessage;

  /// Create a copy of TappedNotification
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TappedNotificationCopyWith<TappedNotification> get copyWith =>
      _$TappedNotificationCopyWithImpl<TappedNotification>(
          this as TappedNotification, _$identity);

  /// Serializes this TappedNotification to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TappedNotification &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.action, action) || other.action == action) &&
            const DeepCollectionEquality().equals(other.payload, payload) &&
            (identical(other.queueReceiveMessage, queueReceiveMessage) ||
                other.queueReceiveMessage == queueReceiveMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      notificationId,
      conversationId,
      categoryId,
      action,
      const DeepCollectionEquality().hash(payload),
      queueReceiveMessage);

  @override
  String toString() {
    return 'TappedNotification(notificationId: $notificationId, conversationId: $conversationId, categoryId: $categoryId, action: $action, payload: $payload, queueReceiveMessage: $queueReceiveMessage)';
  }
}

/// @nodoc
abstract mixin class $TappedNotificationCopyWith<$Res> {
  factory $TappedNotificationCopyWith(
          TappedNotification value, $Res Function(TappedNotification) _then) =
      _$TappedNotificationCopyWithImpl;
  @useResult
  $Res call(
      {String? notificationId,
      String? conversationId,
      String? categoryId,
      String? action,
      Map<String, dynamic>? payload,
      QueueReceiveMessage? queueReceiveMessage});

  $QueueReceiveMessageCopyWith<$Res>? get queueReceiveMessage;
}

/// @nodoc
class _$TappedNotificationCopyWithImpl<$Res>
    implements $TappedNotificationCopyWith<$Res> {
  _$TappedNotificationCopyWithImpl(this._self, this._then);

  final TappedNotification _self;
  final $Res Function(TappedNotification) _then;

  /// Create a copy of TappedNotification
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationId = freezed,
    Object? conversationId = freezed,
    Object? categoryId = freezed,
    Object? action = freezed,
    Object? payload = freezed,
    Object? queueReceiveMessage = freezed,
  }) {
    return _then(_self.copyWith(
      notificationId: freezed == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      action: freezed == action
          ? _self.action
          : action // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _self.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      queueReceiveMessage: freezed == queueReceiveMessage
          ? _self.queueReceiveMessage
          : queueReceiveMessage // ignore: cast_nullable_to_non_nullable
              as QueueReceiveMessage?,
    ));
  }

  /// Create a copy of TappedNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QueueReceiveMessageCopyWith<$Res>? get queueReceiveMessage {
    if (_self.queueReceiveMessage == null) {
      return null;
    }

    return $QueueReceiveMessageCopyWith<$Res>(_self.queueReceiveMessage!,
        (value) {
      return _then(_self.copyWith(queueReceiveMessage: value));
    });
  }
}

/// Adds pattern-matching-related methods to [TappedNotification].
extension TappedNotificationPatterns on TappedNotification {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_TappedNotification value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TappedNotification() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_TappedNotification value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TappedNotification():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_TappedNotification value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TappedNotification() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? notificationId,
            String? conversationId,
            String? categoryId,
            String? action,
            Map<String, dynamic>? payload,
            QueueReceiveMessage? queueReceiveMessage)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _TappedNotification() when $default != null:
        return $default(
            _that.notificationId,
            _that.conversationId,
            _that.categoryId,
            _that.action,
            _that.payload,
            _that.queueReceiveMessage);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? notificationId,
            String? conversationId,
            String? categoryId,
            String? action,
            Map<String, dynamic>? payload,
            QueueReceiveMessage? queueReceiveMessage)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TappedNotification():
        return $default(
            _that.notificationId,
            _that.conversationId,
            _that.categoryId,
            _that.action,
            _that.payload,
            _that.queueReceiveMessage);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? notificationId,
            String? conversationId,
            String? categoryId,
            String? action,
            Map<String, dynamic>? payload,
            QueueReceiveMessage? queueReceiveMessage)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _TappedNotification() when $default != null:
        return $default(
            _that.notificationId,
            _that.conversationId,
            _that.categoryId,
            _that.action,
            _that.payload,
            _that.queueReceiveMessage);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _TappedNotification extends TappedNotification {
  const _TappedNotification(
      {this.notificationId,
      this.conversationId,
      this.categoryId,
      this.action,
      final Map<String, dynamic>? payload,
      this.queueReceiveMessage})
      : _payload = payload,
        super._();
  factory _TappedNotification.fromJson(Map<String, dynamic> json) =>
      _$TappedNotificationFromJson(json);

  @override
  final String? notificationId;
  @override
  final String? conversationId;
  @override
  final String? categoryId;
  @override
  final String? action;
  final Map<String, dynamic>? _payload;
  @override
  Map<String, dynamic>? get payload {
    final value = _payload;
    if (value == null) return null;
    if (_payload is EqualUnmodifiableMapView) return _payload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final QueueReceiveMessage? queueReceiveMessage;

  /// Create a copy of TappedNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TappedNotificationCopyWith<_TappedNotification> get copyWith =>
      __$TappedNotificationCopyWithImpl<_TappedNotification>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TappedNotificationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TappedNotification &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.action, action) || other.action == action) &&
            const DeepCollectionEquality().equals(other._payload, _payload) &&
            (identical(other.queueReceiveMessage, queueReceiveMessage) ||
                other.queueReceiveMessage == queueReceiveMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      notificationId,
      conversationId,
      categoryId,
      action,
      const DeepCollectionEquality().hash(_payload),
      queueReceiveMessage);

  @override
  String toString() {
    return 'TappedNotification(notificationId: $notificationId, conversationId: $conversationId, categoryId: $categoryId, action: $action, payload: $payload, queueReceiveMessage: $queueReceiveMessage)';
  }
}

/// @nodoc
abstract mixin class _$TappedNotificationCopyWith<$Res>
    implements $TappedNotificationCopyWith<$Res> {
  factory _$TappedNotificationCopyWith(
          _TappedNotification value, $Res Function(_TappedNotification) _then) =
      __$TappedNotificationCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? notificationId,
      String? conversationId,
      String? categoryId,
      String? action,
      Map<String, dynamic>? payload,
      QueueReceiveMessage? queueReceiveMessage});

  @override
  $QueueReceiveMessageCopyWith<$Res>? get queueReceiveMessage;
}

/// @nodoc
class __$TappedNotificationCopyWithImpl<$Res>
    implements _$TappedNotificationCopyWith<$Res> {
  __$TappedNotificationCopyWithImpl(this._self, this._then);

  final _TappedNotification _self;
  final $Res Function(_TappedNotification) _then;

  /// Create a copy of TappedNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? notificationId = freezed,
    Object? conversationId = freezed,
    Object? categoryId = freezed,
    Object? action = freezed,
    Object? payload = freezed,
    Object? queueReceiveMessage = freezed,
  }) {
    return _then(_TappedNotification(
      notificationId: freezed == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _self.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      action: freezed == action
          ? _self.action
          : action // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _self._payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      queueReceiveMessage: freezed == queueReceiveMessage
          ? _self.queueReceiveMessage
          : queueReceiveMessage // ignore: cast_nullable_to_non_nullable
              as QueueReceiveMessage?,
    ));
  }

  /// Create a copy of TappedNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QueueReceiveMessageCopyWith<$Res>? get queueReceiveMessage {
    if (_self.queueReceiveMessage == null) {
      return null;
    }

    return $QueueReceiveMessageCopyWith<$Res>(_self.queueReceiveMessage!,
        (value) {
      return _then(_self.copyWith(queueReceiveMessage: value));
    });
  }
}

// dart format on

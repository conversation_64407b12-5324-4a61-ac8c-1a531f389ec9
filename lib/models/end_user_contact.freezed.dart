// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'end_user_contact.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EndUserContact {
  String get id;
  String? get name;
  String? get photoUrl;

  /// Create a copy of EndUserContact
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EndUserContactCopyWith<EndUserContact> get copyWith =>
      _$EndUserContactCopyWithImpl<EndUserContact>(
          this as EndUserContact, _$identity);

  /// Serializes this EndUserContact to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EndUserContact &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, photoUrl);

  @override
  String toString() {
    return 'EndUserContact(id: $id, name: $name, photoUrl: $photoUrl)';
  }
}

/// @nodoc
abstract mixin class $EndUserContactCopyWith<$Res> {
  factory $EndUserContactCopyWith(
          EndUserContact value, $Res Function(EndUserContact) _then) =
      _$EndUserContactCopyWithImpl;
  @useResult
  $Res call({String id, String? name, String? photoUrl});
}

/// @nodoc
class _$EndUserContactCopyWithImpl<$Res>
    implements $EndUserContactCopyWith<$Res> {
  _$EndUserContactCopyWithImpl(this._self, this._then);

  final EndUserContact _self;
  final $Res Function(EndUserContact) _then;

  /// Create a copy of EndUserContact
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [EndUserContact].
extension EndUserContactPatterns on EndUserContact {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_EndUserContact value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _EndUserContact() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_EndUserContact value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EndUserContact():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_EndUserContact value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EndUserContact() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, String? name, String? photoUrl)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _EndUserContact() when $default != null:
        return $default(_that.id, _that.name, _that.photoUrl);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, String? name, String? photoUrl) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EndUserContact():
        return $default(_that.id, _that.name, _that.photoUrl);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, String? name, String? photoUrl)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _EndUserContact() when $default != null:
        return $default(_that.id, _that.name, _that.photoUrl);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _EndUserContact implements EndUserContact {
  const _EndUserContact({required this.id, this.name, this.photoUrl});
  factory _EndUserContact.fromJson(Map<String, dynamic> json) =>
      _$EndUserContactFromJson(json);

  @override
  final String id;
  @override
  final String? name;
  @override
  final String? photoUrl;

  /// Create a copy of EndUserContact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EndUserContactCopyWith<_EndUserContact> get copyWith =>
      __$EndUserContactCopyWithImpl<_EndUserContact>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EndUserContactToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EndUserContact &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, photoUrl);

  @override
  String toString() {
    return 'EndUserContact(id: $id, name: $name, photoUrl: $photoUrl)';
  }
}

/// @nodoc
abstract mixin class _$EndUserContactCopyWith<$Res>
    implements $EndUserContactCopyWith<$Res> {
  factory _$EndUserContactCopyWith(
          _EndUserContact value, $Res Function(_EndUserContact) _then) =
      __$EndUserContactCopyWithImpl;
  @override
  @useResult
  $Res call({String id, String? name, String? photoUrl});
}

/// @nodoc
class __$EndUserContactCopyWithImpl<$Res>
    implements _$EndUserContactCopyWith<$Res> {
  __$EndUserContactCopyWithImpl(this._self, this._then);

  final _EndUserContact _self;
  final $Res Function(_EndUserContact) _then;

  /// Create a copy of EndUserContact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_EndUserContact(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _self.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on

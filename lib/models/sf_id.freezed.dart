// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sf_id.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SfId {
  @ParseSfIdConverter()
  String get value;

  /// Create a copy of SfId
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<SfId> get copyWith =>
      _$SfIdCopyWithImpl<SfId>(this as SfId, _$identity);

  /// Serializes this SfId to a JSON map.
  Map<String, dynamic> toJson();
}

/// @nodoc
abstract mixin class $SfIdCopyWith<$Res> {
  factory $SfIdCopyWith(SfId value, $Res Function(SfId) _then) =
      _$SfIdCopyWithImpl;
  @useResult
  $Res call({@ParseSfIdConverter() String value});
}

/// @nodoc
class _$SfIdCopyWithImpl<$Res> implements $SfIdCopyWith<$Res> {
  _$SfIdCopyWithImpl(this._self, this._then);

  final SfId _self;
  final $Res Function(SfId) _then;

  /// Create a copy of SfId
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_self.copyWith(
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [SfId].
extension SfIdPatterns on SfId {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SfId value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SfId() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SfId value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SfId():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SfId value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SfId() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() String value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SfId() when $default != null:
        return $default(_that.value);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(@ParseSfIdConverter() String value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SfId():
        return $default(_that.value);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(@ParseSfIdConverter() String value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SfId() when $default != null:
        return $default(_that.value);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SfId extends SfId {
  _SfId([@ParseSfIdConverter() this.value = '']) : super._();
  factory _SfId.fromJson(Map<String, dynamic> json) => _$SfIdFromJson(json);

  @override
  @JsonKey()
  @ParseSfIdConverter()
  final String value;

  /// Create a copy of SfId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SfIdCopyWith<_SfId> get copyWith =>
      __$SfIdCopyWithImpl<_SfId>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SfIdToJson(
      this,
    );
  }
}

/// @nodoc
abstract mixin class _$SfIdCopyWith<$Res> implements $SfIdCopyWith<$Res> {
  factory _$SfIdCopyWith(_SfId value, $Res Function(_SfId) _then) =
      __$SfIdCopyWithImpl;
  @override
  @useResult
  $Res call({@ParseSfIdConverter() String value});
}

/// @nodoc
class __$SfIdCopyWithImpl<$Res> implements _$SfIdCopyWith<$Res> {
  __$SfIdCopyWithImpl(this._self, this._then);

  final _SfId _self;
  final $Res Function(_SfId) _then;

  /// Create a copy of SfId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? value = null,
  }) {
    return _then(_SfId(
      null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
